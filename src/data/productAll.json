{"redis_cached": 0, "product_brand": [{"id": 206, "index": 0, "featured": 0, "title": "#FR2", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "fr-2", "description": "FR2 F**king Rabbits is a provocative streetwear label that blends the worlds of fashion, design and photography, founded by one of Tokyo's most iconic streetwear figures, <PERSON><PERSON>.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210121d3d9541c97fbc5ec.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905199d1b3ba33d61be.png", "public": 1, "enabled": 1, "created_at": "2021-01-21T10:24:50.000Z", "updated_at": "2024-09-05T07:51:54.000Z"}, {"id": 309, "index": 0, "featured": 0, "title": "3CE", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "3ce", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241023a2b8f800099859c2.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-23T23:52:04.000Z", "updated_at": "2024-10-24T03:42:56.000Z"}, {"id": 266, "index": 0, "featured": 0, "title": "<PERSON><PERSON> & Söhne", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "a-lange-sohne", "description": "Lange & Söhne brand – exactly 145 years after <PERSON> laid the foundations for German fine watchmaking. The presentation of the first collection followed four years later, which, thanks to the vision of <PERSON> and <PERSON><PERSON><PERSON>, took A. Lange & Söhne to the top of the watchmaking world.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514ebb69754cc5069e7.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905652e78fab54a59cd.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:03:16.000Z", "updated_at": "2024-09-05T03:46:38.000Z"}, {"id": 165, "index": 10, "featured": 0, "title": "AAPE", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "aape", "description": "A Bathing Ape has been bathing it’s ape now since 1993. That was the year when founder <PERSON><PERSON>, real name <PERSON><PERSON><PERSON>, opened a shop in the world-renown fashion district Ura-Harajuku called NOWHERE in partnership with Undercover founder <PERSON>. Both attended Bunka Fashion College which <PERSON><PERSON> credits as having done “zero” for him other than giving him that chance to meet <PERSON><PERSON><PERSON>. The pair also had help from the “Godfather of Harajuku<PERSON> <PERSON><PERSON><PERSON>. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008100f291478e31e4531.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409059973aff66261be59.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-05T07:55:28.000Z"}, {"id": 259, "index": 0, "featured": 0, "title": "Acne Studios", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "acne-studios", "description": "Minimal, unconventional, experimental and punk. The history of Acne Studios, the brand founded by designer <PERSON><PERSON> in Stockholm in 1997.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202307095285f8eabffdc6c3.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905ea42e7cb02d3887c.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T13:40:41.000Z", "updated_at": "2024-09-05T07:53:38.000Z"}, {"id": 3, "index": 20, "featured": 12, "title": "Adidas", "title_tc": "", "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "adidas", "description": "The name adidas comes from its founder’s name, <PERSON>, and its abbreviation, <PERSON><PERSON><PERSON>.”   Adidas is one of the most famous German clothing brands that manufactures and sells sportswear, founded by <PERSON> <PERSON><PERSON><PERSON><PERSON> in Herzogenaurach, Germany, in 1949.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2021012960d521b2a514446c.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408295250b0b5979d7322.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:37.000Z", "updated_at": "2024-08-29T13:54:03.000Z"}, {"id": 1, "index": 30, "featured": 5, "title": "Air Jordan", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "air-jordan", "description": "Air Jordan is a line of basketball shoes produced by Nike, Inc. Related apparel and accessories are marketed under Jordan Brand.  The first Air Jordan shoe was produced for basketball player <PERSON> during his time with the Chicago Bulls on November 17, 1984 and released to the public on April 1, 1985.  The Jordan Logo, known as the \"Jumpman\", originated from a photograph by <PERSON><PERSON>, taken before <PERSON> played for Team USA in the 1984 Summer Olympics.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805a364b32e39ccf854.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240829ef41ee504852f73a.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:28.000Z", "updated_at": "2024-08-29T13:59:44.000Z"}, {"id": 148, "index": 40, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "alexander-mc-queen", "description": "<PERSON> is a British luxury house founded in 1992 by <PERSON>.  <PERSON> is distinctive for an expression of individuality, subversive strength and raw power. With a design studio and atelier based in London, the house is known for uncompromising quality and creative vision.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008056ff8e754ed86f117.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409034f60e505800123f5.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-09-03T13:40:06.000Z"}, {"id": 210, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "alexander-wang", "description": "New York fashion designer, <PERSON>, started his eponymous label in 2005, and quickly rose to become one of the most promising designers in the American fashion scene. <PERSON>’s designs are known for its unisex silhouette, dominantly black, white or earth colour palette and casual urban styling. Now a household name, <PERSON> produces both men’s and womenswear, as well as footwear and accessories.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210122bc581f7acfea4286.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827e645df43e0ef3866.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:21:04.000Z", "updated_at": "2024-08-27T06:40:00.000Z"}, {"id": 21, "index": 50, "featured": 0, "title": "AMBUSH", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "ambush", "description": "Conceived in 2008 as an experimental line of jewellery, AMBUSH® is the brainchild of Japanese designers VERBAL and YOON. Seamlessly portraying distinct Tokyo vibes through a pop art inspired collection, the brand's eclectic designs combine ingenuous style with utilitarian motifs.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805351c9417421f6795.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905a05d99dfe7621295.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:35:19.000Z", "updated_at": "2024-09-05T07:57:36.000Z"}, {"id": 200, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "ami-paris", "description": "Established in Paris in 2011, inspired by the city ever since — AMI is a luxury apparel house for men and women.  Like <PERSON>, its founder and creative director, AMI approaches fashion in a relaxed and authentic way, with stylish and comprehensive wardrobes composed of timeless basics. Capturing its hometown’s unique, effortless elegance, AMI blurs the boundaries between casual and chic. Its values celebrate love and friendship, inclusion and diversity.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202101140d4d51984bce38cf.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090516355ff449d76e1e.png", "public": 1, "enabled": 1, "created_at": "2021-01-14T04:10:19.000Z", "updated_at": "2024-09-05T07:59:37.000Z"}, {"id": 310, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "an<PERSON>a", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102305f5ff92eaf4865e.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-23T23:52:51.000Z", "updated_at": "2024-10-24T03:43:01.000Z"}, {"id": 311, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": "", "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "anna-sui", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241023d536e213d4fe49b2.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-23T23:53:19.000Z", "updated_at": "2024-10-24T03:43:06.000Z"}, {"id": 261, "index": 0, "featured": 0, "title": "ANTA", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "anta", "description": "ANTA was established in China in 1990s. As a leading sports brand in China, ANTA has always been committed to providing consumers with functional, professional and technology-driven sports products across a diverse range of sporting categories, from popular sports such as running, cross-training, basketball, and others, to professional and niche sports.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20231106b8bcfecfef541a12.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240829bb023cba168077e0.png", "public": 1, "enabled": 1, "created_at": "2023-11-06T12:14:25.000Z", "updated_at": "2024-08-29T14:08:06.000Z"}, {"id": 201, "index": 0, "featured": 0, "title": "ARC'TERYX", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "arcteryx", "description": "Arc'teryx is known for its waterproof Gore-Tex shell jackets, knitwear, and down parkas. Founded in 1989 as Rock Solid, the company re-branded in 1991 as Arc'teryx to produce outerwear and climbing gear for the Coast Mountains in Canada.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210114eae42f036293bb52.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905f883d293cae45697.png", "public": 1, "enabled": 1, "created_at": "2021-01-14T04:10:46.000Z", "updated_at": "2024-09-05T08:01:28.000Z"}, {"id": 202, "index": 0, "featured": 0, "title": "Aritz<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "aritzia", "description": "Artizia was founded as a women’s fashion brand in 1984 by Canadian entrepreneur, <PERSON> <PERSON> founded the upscale clothing store brand in Vancouver B.C. where it opened its first store at the Oakridge Centre shopping mall.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210114751f596e7fe128d7.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905ed184e8293f0dffd.png", "public": 1, "enabled": 1, "created_at": "2021-01-14T04:11:26.000Z", "updated_at": "2024-09-05T08:03:20.000Z"}, {"id": 238, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "armani-handbags-watches", "description": "Armani, is an Italian luxury fashion house founded in Milan by <PERSON> which designs, manufactures, distributes and retails haute couture, ready-to-wear, leather goods, shoes, accessories, and home interiors.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827bf19baf0e4edeed0.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408270b863d06fde61c3f.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T07:49:39.000Z", "updated_at": "2024-08-27T06:58:07.000Z"}, {"id": 239, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "armani-shoes-comestic", "description": "Armani, is an Italian luxury fashion house founded in Milan by <PERSON> which designs, manufactures, distributes and retails haute couture, ready-to-wear, leather goods, shoes, accessories, and home interiors.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024082729ce46dd4a8bdb51.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827fda205fd9c1f0c60.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T07:50:04.000Z", "updated_at": "2024-10-29T11:44:07.000Z"}, {"id": 240, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "armani-clothing", "description": "Armani, is an Italian luxury fashion house founded in Milan by <PERSON> which designs, manufactures, distributes and retails haute couture, ready-to-wear, leather goods, shoes, accessories, and home interiors.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024082757d4fc939ca82310.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408279ae85151398906c5.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T07:50:21.000Z", "updated_at": "2024-10-29T11:43:56.000Z"}, {"id": 155, "index": 60, "featured": 0, "title": "Asics", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "asics", "description": "Onitsuka Co. was originally founded in 1949, later merging with GTO Co. and JELENK Co. in 1977. Following the merger, the company officially formed “ASICS”. Today, under the umbrella of the ASICS company are the ASICS and Onitsuka Tiger brands.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008102047fc97298e8cbd.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240829300a5732afbe69c5.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-08-29T14:11:28.000Z"}, {"id": 267, "index": 0, "featured": 0, "title": "AUDEMARS PIGUET", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "audemars-piguet", "description": "Audemars Piguet is the favourite brand of many famous athletes, celebrities and music stars. It's easy to see why, since it remains one of the last family-owned, independent, luxury jewellery and watch manufacturers in Switzerland.  Le Brassus, the birthplace of Audemars Piguet, is a picturesque town in the Vallée de Joux, in Switzerland's Jura Mountains. Today, the brand's headquarters remain in the same location, near Geneva, providing the same inspirational setting more than 140 years on.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514b1b4ab00bf9eff06.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409053f0c50d0e462b5d6.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:04:00.000Z", "updated_at": "2024-09-05T03:54:57.000Z"}, {"id": 139, "index": 70, "featured": 11, "title": "<PERSON><PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "balenciaga", "description": "Balenciaga is a French-Spanish luxury fashion line that was founded in 1919 by Spanish couturier <PERSON><PERSON><PERSON><PERSON> in San Sebastián. Balenciaga produces ready-to-wear footwear, handbags, and accessories, and licenses its name and branding to Coty for fragrances.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805f35a8061c241166d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408273dad083bad077bed.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-08-27T07:00:41.000Z"}, {"id": 10, "index": 80, "featured": 0, "title": "BAPE", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "bape", "description": "A Bathing Ape (or BAPE) is a Japanese fashion brand founded by <PERSON><PERSON> (<PERSON><PERSON><PERSON>) in Ura-Harajuku in 1993. The brand specializes in men's, women's and children's lifestyle and street wear, running 19 stores in Japan, including Bape Stores, Bape Pirate Stores, Bape Kids Stores, Bapexclusive Aoyama, and Bapexclusive Kyoto. The Kyoto store also includes Bape Gallery, a space used for various events and art shows sponsored by Bape. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008059fe7617b4fc57d50.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240829976c592cc38adee4.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-08-29T14:14:36.000Z"}, {"id": 170, "index": 90, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "beams", "description": "Started in 1999 to house timeless men's clothing, remembering the good-old American styles that BEAMS grew up on. Such original styles are kept alive in a lineup of original, import, vintage pieces and accessories, while BEAMS+ is not merely revival or nostalgia, but instead a pursuit of authenticity in next generation casual wear.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810dcd1ca55def86795.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409055ca9bedd48034188.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-05T08:08:39.000Z"}, {"id": 28, "index": 100, "featured": 0, "title": "Bearbrick", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "bearbrick", "description": "The first figure was released 27 May 2001 as a free gift to visitors of the World Character Convention 12 in Tokyo. Since then Bearbricks have been released in several different sizes using a variety of materials, including wood, felt, and glow-in-the-dark plastic.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008055b0afc2a14047322.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240903832381ee1c5f3ce6.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-09-03T13:24:19.000Z"}, {"id": 268, "index": 0, "featured": 0, "title": "BLANCPAIN", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "blanc<PERSON>in", "description": "Blancpain is a Swiss luxury watch manufacturer, headquartered in Paudex/Le Brassus, Switzerland. It designs, manufactures, distributes, and sells prestige and luxury mechanical watches. Founded by <PERSON><PERSON><PERSON><PERSON> in Villeret, Switzerland in 1735, Blancpain is the oldest registered watch brand in the world. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514cd0ebc0718428c90.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409053e783ce1d93f6ce3.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:04:26.000Z", "updated_at": "2024-09-05T03:59:50.000Z"}, {"id": 312, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "bobbi-brown", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410233a516937a08991f5.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-23T23:53:41.000Z", "updated_at": "2024-10-24T03:43:12.000Z"}, {"id": 175, "index": 110, "featured": 0, "title": "Bottega Veneta", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "bottega-veneta", "description": "Bottega Veneta was established in 1966 in Vicenza, Italy by <PERSON> and <PERSON><PERSON>. Specialized in artisanal leather goods, the brand developed a distinctive leather weaving technique, the Intrecciato, which became Bottega Veneta's signature look.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810e7773a4c3678b8fc.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827319a226fa29a0e3a.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-10-24T03:20:44.000Z"}, {"id": 269, "index": 0, "featured": 0, "title": "BREGUET", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "breguet", "description": "Breguet was founded in 1775 by <PERSON><PERSON><PERSON>, a Swiss watchmaker born to Huguenot parents in Neuchâtel. He studied watchmaking for ten years under <PERSON> and <PERSON><PERSON><PERSON> before setting up his own watchmaking business at 51 Quai de l'Horloge on the Île de la Cité in Paris.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514b33bbea57e4bbec6.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905f2ff2d9195fd4d4a.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:04:45.000Z", "updated_at": "2024-09-05T04:02:32.000Z"}, {"id": 270, "index": 0, "featured": 0, "title": "BREITLING", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "breitling", "description": "Breitling is a Swiss luxury watchmaker founded in 1884 in Saint-Imier, Switzerland, by <PERSON>. The company is known for its precision-made chronometers designed for aviators and is based in Grenchen, Switzerland.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024051499663a1469394ef2.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090520e7e0218ba8a481.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:05:16.000Z", "updated_at": "2024-09-05T04:05:01.000Z"}, {"id": 241, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON>i", "description": "Founded in Rome in 1945 by visionary duo <PERSON><PERSON><PERSON>, a Master Tailor, and his business partner <PERSON><PERSON><PERSON>, Brion<PERSON> is recognized as the world’s most prestigious menswear luxury House.  <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s partnership was a marriage of technical skill and creative entrepreneurship that would define <PERSON><PERSON><PERSON>’s as the go-to sartorial destination for the elite. Society’s most prestigious and powerful, from Hollywood icons to heads of state, were queuing up to be dressed by the store’s artisan tailors and the brand became synonymous with status, indicating the lifestyle of a distinguished gentleman.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709fc88a4becf1f4157.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901859f82962d69c658.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T08:15:09.000Z", "updated_at": "2024-09-01T15:12:12.000Z"}, {"id": 140, "index": 120, "featured": 0, "title": "Burberry", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "burberry", "description": "Burberry is a British luxury fashion house established in 1856 by <PERSON> and headquartered in London, England. It designs and distributes ready to wear, including trench coats, leather accessories, and footwear.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805e9e9d0bb5be85045.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827e49beaccc173c047.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:21:07.000Z"}, {"id": 178, "index": 130, "featured": 0, "title": "Bvlgari", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "bvl<PERSON><PERSON>", "description": "Founded in Rome in 1884 by the talented Greek silversmith <PERSON><PERSON><PERSON>, the brand quickly established a reputation for Italian excellence with exquisite craftsmanship and magnificent jewellery creations. Over the decades, the Bulgari generations defined a distinctive style made of vibrant colour combinations, exquisitely balanced volumes and unmistakable motifs that pay homage to the Roman roots of the company. While always revering its cultural legacy, Bvlgari introduced innovations that rewrote the rules of the jewellery universe and launched new trends that stood out as icons of contemporary design.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810e4277f1750a7bcc2.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827fe9518455fb603e4.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-10-24T03:21:35.000Z"}, {"id": 314, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "calvin-klein", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102977f2a771821b2757.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-23T23:57:15.000Z", "updated_at": "2024-10-29T04:20:23.000Z"}, {"id": 184, "index": 140, "featured": 0, "title": "Canada Goose", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "canada-goose", "description": "Founded in 1957 in a small warehouse in Toronto, Canada Goose has grown into one of the world’s leading manufacturers of performance luxury apparel.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008105013953b4b2b9487.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901dac75417e09925bd.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T15:16:01.000Z"}, {"id": 171, "index": 150, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON>", "description": "Carhartt, Inc. is an American clothing company founded in 1889, known for heavy-duty work wear such as jackets, coats, overalls, coveralls, vests, shirts, jeans, dungarees, fire-resistant clothing and hunting apparel. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020081087b3d483fd67803e.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090572863cb64d953f80.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-05T08:10:47.000Z"}, {"id": 271, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "carl-f-bucherer", "description": "Carl F. Bucherer is a Swiss watch company based in Lucerne, Switzerland, which manufactures luxury men's and women's mechanical watches. From its founding in 1888 until its sale in 2023, the company was wholly owned by the <PERSON><PERSON><PERSON> family, making it one of the oldest luxury Swiss watchmakers continuously held by the founding family. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202405146d65d53a56651e5c.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905666abd94953de4da.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:08:15.000Z", "updated_at": "2024-09-05T04:08:46.000Z"}, {"id": 188, "index": 160, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "cartier", "description": "Cartier was founded in 1847 by Parisian watchmaker <PERSON><PERSON><PERSON> and is one of the most exquisite and high profile watch and jewellery brands in the world. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810b5615636e3469205.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409015594f6226ad32d58.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T15:21:04.000Z"}, {"id": 296, "index": 0, "featured": 0, "title": "Casio", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "casio", "description": "But while Casio is synonymous with watches, the company didn’t actually begin life selling timepieces. Casio was founded in 1946 in Japan in the aftermath of World War II. The founder, <PERSON><PERSON>, started out by producing mechanical parts. He was joined by his brothers, who had expertise in electronics and by 1949 they had begun producing calculators. In the 1950s, they successfully produced Japan’s first electronic calculator, and it was small enough to be portable and desk-size.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240523885a827f6534de68.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905fabf5bd341a28837.png", "public": 1, "enabled": 1, "created_at": "2024-05-23T17:13:01.000Z", "updated_at": "2024-09-05T08:12:40.000Z"}, {"id": 162, "index": 170, "featured": 0, "title": "Cav Empt", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "cav-empt", "description": "Cav Empt was founded in 2011 in the aftermath of the Tokyo earthquake by <PERSON>, SK8THING and <PERSON><PERSON><PERSON>. Their shared experience brings dystopian garment dyed products, military-inspired outerwear and digital, space influenced graphics.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020081099d2f28914b42506.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905d64e882aa5b57711.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-05T08:15:24.000Z"}, {"id": 22, "index": 180, "featured": 0, "title": "CDG Play", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "cdg-play", "description": "COMME DES GARÇONS PLAY, the sub-label of the iconic Japanese clothing brand COMME des GARÇONS, was founded in 2002. The accessible PLAY line has amassed a considerable fan base since, making its wearer “instantly part of the cult Comme des Garçons club.”", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080573a1423a6a20c495.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905709690c0ee4159b7.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:35:53.000Z", "updated_at": "2024-09-05T08:20:28.000Z"}, {"id": 143, "index": 190, "featured": 0, "title": "Celine", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "celine", "description": "<PERSON><PERSON> is renowned for producing luxury clothing and accessories that women actually want rather than exotic fashion that's unrealistic for day-to-day wear. Although the brand is often defined by <PERSON>'s modern, minimalistic and sophisticated aesthetic, <PERSON><PERSON> has a long history dating back to the 1940's.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008052d2035b3ec625e60.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408274ad5368029847022.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-08-27T07:37:15.000Z"}, {"id": 242, "index": 0, "featured": 0, "title": "Cerruti 1881", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "c<PERSON><PERSON>ti-1881", "description": "Cerruti 1881 is a brand of the fashion house Cerruti founded in 1967 in France by the Italian designer <PERSON><PERSON>. The French house designs and manufactures designer clothes, perfumes, leather goods, watches and luxury accessories.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709917a74f838588ef4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901b7a3af1a7491a383.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T08:25:05.000Z", "updated_at": "2024-09-01T15:31:39.000Z"}, {"id": 17, "index": 200, "featured": 0, "title": "Champion", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "champion", "description": "The company was established in 1919 by the Feinbloom Brothers as \"Knickerbocker Knitting Company.\" The company soon signed an agreement with the Michigan Wolverines to produce uniforms for their teams. In the 1930s the company was renamed \"Champion Knitting Mills Inc.\", producing sweatshirts and hoodies.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805b6ec04a380b78fce.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409052bb6849045965242.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-05T08:23:08.000Z"}, {"id": 135, "index": 210, "featured": 2, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "chanel", "description": "Chanel is a luxury fashion house founded in 1910 by <PERSON><PERSON> in Paris. It is privately owned by the <PERSON><PERSON><PERSON><PERSON> family and has been headquartered in London since 2018.  Chanel specializes in women's ready-to-wear, luxury goods, and accessories and licenses its name and branding to Luxottica for eyewear. Chanel is well known for its No. 5 perfume and \"Chanel Suit\". <PERSON><PERSON> is credited for revolutionizing haute couture and ready-to-wear by replacing structured, corseted silhouettes with more functional garments that women still found flattering.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008052bb4609295b1b106.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827f2f8525df955bb7c.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:21:57.000Z"}, {"id": 315, "index": 0, "featured": 0, "title": "Chantecaille", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "chantecaille", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241023b697f0b30d138b40.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-23T23:57:52.000Z", "updated_at": "2024-10-24T03:43:33.000Z"}, {"id": 185, "index": 220, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "charlie-luciano", "description": "<PERSON> was founded in Milan in 2015. The brand design team is good at presenting “contradictions and contrasts” in their products. They implement the core concepts through the entire product line through graffiti art and special tailoring.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810ca8064a55f36a8ae.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409012565610be22c8f06.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T15:36:48.000Z"}, {"id": 190, "index": 230, "featured": 0, "title": "Chaumet", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "chaumet", "description": "Chaumet is a French luxury jewellery and watch house based in Paris.  Chaumet is a jewellery and watchmaking designer founded in 1780 by <PERSON><PERSON><PERSON>. Fourteen artisans ply their trade in the workshop on Place Vendôme under the direction of foreman <PERSON><PERSON>. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020081008cee737722abf87.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240903f7b0cf73bf0098dd.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-03T13:42:19.000Z"}, {"id": 176, "index": 240, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "chloe", "description": "Chloé was founded in 1952 by <PERSON><PERSON>, an Egyptian-born Parisian who liberated women from the formal fashion of the era by pioneering luxury ready-to-wear. A true visionary, <PERSON><PERSON> believed that women should dare to be themselves.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810f1ef65f296beadf8.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827a99aaffa73682a82.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-10-24T03:22:22.000Z"}, {"id": 272, "index": 0, "featured": 0, "title": "CHOPARD", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "chopard", "description": "Renowned for its creativity, state-of-the-art technology and the virtuosity of its artisans, <PERSON><PERSON> has become one of the leading names in the luxury Swiss watch and jewellery industry. Discover its spectacular journey under the impetus of the <PERSON><PERSON><PERSON><PERSON> family, from 1860 to the present day.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024051412f7bc433a9d8106.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090561929dceb862dc84.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:08:54.000Z", "updated_at": "2024-09-05T04:11:41.000Z"}, {"id": 192, "index": 250, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "christian-loub<PERSON>in", "description": "<PERSON> opened his first store in Paris in 1992; his red-soled shoes, handcrafted in Italy, were soon taken up by Hollywood actresses and Paris’ fashion establishment. The designer believes that his shoes have the ability to \"make a woman look sexy, beautiful, to make her legs look as long as [he] can.\"", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810c617e54c09fbb3de.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409041b710698d85ba8af.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-04T12:41:31.000Z"}, {"id": 205, "index": 0, "featured": 0, "title": "Chrome Hearts", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "chrome-hearts", "description": "Chrome Hearts is a luxury brand from Hollywood, founded in 1988 by <PERSON>, <PERSON> and <PERSON>. The brand produces silver, gold, and diamond accessories, alongside eyewear, leather items, apparel, furniture, kitchenware, and random objects. They are known for using leather, silver, and ebony. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202101198b526b816d6aed68.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901e6b3e19786da2e40.png", "public": 1, "enabled": 1, "created_at": "2021-01-19T09:32:45.000Z", "updated_at": "2024-09-01T15:41:53.000Z"}, {"id": 316, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "clarins", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241023407827638a498863.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-23T23:58:22.000Z", "updated_at": "2024-10-24T03:43:26.000Z"}, {"id": 318, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "clé-de-peau", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024b3208281af90046a.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:00:41.000Z", "updated_at": "2024-10-24T03:43:39.000Z"}, {"id": 317, "index": 0, "featured": 0, "title": "Clinique", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "clinique", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241023385438241d36bc5a.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:00:05.000Z", "updated_at": "2024-10-24T03:43:46.000Z"}, {"id": 26, "index": 260, "featured": 0, "title": "CLOT", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "clot", "description": "CLOT® was proudly established in 2003 as a streetwear label from Hong Kong with the aim of bridging the East and the West through thoughtfully-designed apparel and goods. Founded by <PERSON>, the brand has collaborated with the likes of Nike, visvim, fragment design, Coca-Cola, Stüssy, Medicom Toy and more.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008106fc70b3f67568c44.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409066e03f3c0e9c42f02.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-09-06T03:32:34.000Z"}, {"id": 197, "index": 0, "featured": 0, "title": "Coach", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "coach", "description": "Coach is a global fashion house founded in New York in 1941. Inspired by the vision of Creative Director <PERSON> and the inclusive and courageous spirit of our hometown, we make beautiful things, crafted to last—for you to be yourself in.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20201116e7b25b230e23720c.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827541199f2868c1fdf.png", "public": 1, "enabled": 1, "created_at": "2020-11-16T10:22:57.000Z", "updated_at": "2024-08-27T12:13:05.000Z"}, {"id": 203, "index": 0, "featured": 0, "title": "Columbia", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "columbia", "description": "The Columbia Sportswear Company is an American company that manufactures and distributes outerwear, sportswear, and footwear, as well as headgear, camping equipment, ski apparel, and outerwear accessories.  It was founded in 1938 by <PERSON>, the father of <PERSON><PERSON>. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210114b2d22c818fbe2206.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090670a396d40d4e8268.png", "public": 1, "enabled": 1, "created_at": "2021-01-14T04:11:55.000Z", "updated_at": "2024-09-06T03:35:47.000Z"}, {"id": 5, "index": 270, "featured": 0, "title": "Converse", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "converse", "description": "In 1908, Marquis Mills Converse launched the “Converse Rubber Shoe Company” in Malden, Massachusetts. The first Converse “All-Star” was an elite basketball shoe that was made with the original canvas and rubber, both materials that are still used for production today. At this time, Converse shoes were made for functionality.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805391c932165fa5253.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240829cca7a55786088f3c.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:48:30.000Z", "updated_at": "2024-08-29T14:18:14.000Z"}, {"id": 278, "index": 0, "featured": 0, "title": "CORUM", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "corum", "description": "The adventure began with <PERSON>. With his strong entrepreneurial spirit and watchmaking experience acquired from the most prestigious watch manufacturers, he founded his own brand and created CORUM in 1955 with his uncle <PERSON>.  Particularly fascinated by the word “quorum” which means the minimum number of persons present and necessary to hold discussions and make valid decisions, <PERSON> nonetheless simplified the spelling to CORUM.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514f5dfd8c00dd9d9e3.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409051f7b53d3fc39d5ef.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:11:45.000Z", "updated_at": "2024-09-05T04:15:07.000Z"}, {"id": 319, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "cosme-decorte", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024934de2a512797555.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:01:13.000Z", "updated_at": "2024-10-24T03:43:54.000Z"}, {"id": 320, "index": 0, "featured": 0, "title": "Curel", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "curel", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024b23f71ca2eb01180.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:01:32.000Z", "updated_at": "2024-10-24T03:43:59.000Z"}, {"id": 243, "index": 0, "featured": 0, "title": "D.Exterior", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "d-exterior", "description": "D.Exterior is a distinctive Italian fashion brand renowned for its remarkable fusion of creativity and luxury. Founded in the 1980s, this label has carved a niche for itself by crafting unique and versatile knitwear. D.Exterior is celebrated for its ability to transform traditional knit techniques into contemporary, trend-setting designs. The brand utilizes high-quality materials like cashmere, fine wools, and innovative fabrics to create clothing that embodies comfort and style.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2023070970c074c6c2fcb3fa.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901beebd42ac7a44896.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T08:35:08.000Z", "updated_at": "2024-09-01T15:55:14.000Z"}, {"id": 301, "index": 0, "featured": 0, "title": "DAIWA PIER39", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "daiwa-pier39", "description": "DAIWA PIER39 specializes in fishing-inspired gear, created under the direction of Japanese retailer BEAMS. Since it was founded in 2020, DAIWA PIER39 has been working with the GORE-TEX Brand, using solutions such as GORE-TEX INFINIUM™. Product offerings from DAIWA PIER39 range from outerwear to headwear.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919658fc9dd5ef7fffc.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919a13f83d60ec0aefd.png", "public": 1, "enabled": 1, "created_at": "2024-09-19T06:15:38.000Z", "updated_at": "2024-09-19T09:57:07.000Z"}, {"id": 321, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON>", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102451e749160dca3202.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:01:54.000Z", "updated_at": "2024-10-24T03:44:03.000Z"}, {"id": 234, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON>", "description": "Founded in Brussels in 1829 by the innovative <PERSON>, Delvaux is the oldest fine leather goods house in the world. It is a truly Belgian Maison that has remained at the forefront of luxury for nearly two centuries, owing to its savoir-faire, uncompromising craftsmanship and outstanding quality.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230202210ac34500876bdc.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408278e949de8b6e7dec7.png", "public": 1, "enabled": 1, "created_at": "2023-02-02T06:48:10.000Z", "updated_at": "2024-08-27T12:16:48.000Z"}, {"id": 137, "index": 280, "featured": 10, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "dior", "description": "Christian Dior is a renowned French fashion house that was established by its eponymous founder, <PERSON>, in 1946. The brand quickly gained international recognition and became synonymous with elegance, luxury, and innovative design. Dior’s introduction marked a significant turning point in post-World War II fashion and is often credited with revitalizing the industry.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805929271894c63c928.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827e4717e6578195cf3.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:19:31.000Z"}, {"id": 181, "index": 290, "featured": 0, "title": "Dolce & Gabbana", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "dolce-gabbana", "description": "Dolce & Gabbana also known by initials D&G, is an Italian luxury fashion house founded in 1985 in Legnano by Italian designers <PERSON> and <PERSON>. The house specializes in ready-to-wear, handbags, accessories, cosmetics, and fragrances and licenses its name and branding to Luxottica for eyewear.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810a5aad73db2e42f2d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408270c51c330ede572f9.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-08-27T12:40:27.000Z"}, {"id": 220, "index": 0, "featured": 0, "title": "Drew House", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "drew-house", "description": "Drew House, founded by Canadian pop sensation <PERSON> in 2018, is a streetwear clothing brand that has quickly gained cultural relevance. The brand's name is derived from <PERSON><PERSON><PERSON>'s middle name, <PERSON>.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210315e9522e71330a0bd4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409062799cb6bbe30cc4a.png", "public": 1, "enabled": 1, "created_at": "2021-03-15T06:37:38.000Z", "updated_at": "2024-09-06T03:37:54.000Z"}, {"id": 244, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "elisabetta-franchi", "description": "Elisabetta Franchi was founded in 1996 thanks to a young couple of entrepreneur’s teamwork: <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON> is the creator of a perfectly crafted Ready to Wear collection that owes its success to her style and a particular production strategy based on the “Made in Italy” concept, on quality, excellence and care for details. The success of the brand is due to their creative and innovative spirit paying particular attention to the markets. The evolution of product development philosophy from start to finish is center on style and identity, as well as quality enhancement of a made in Italy production.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202307095daba401377c8522.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409016671ec6169e4fb1b.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T08:43:54.000Z", "updated_at": "2024-09-01T16:01:50.000Z"}, {"id": 322, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024f9ced3a991b94de7.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:02:22.000Z", "updated_at": "2024-10-24T03:44:15.000Z"}, {"id": 245, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "emilio-pucci", "description": "<PERSON>, Marquis of Barsento, founded the brand that bears his name in 1947. A citizen of the world, constantly traveling between his native Florence and the holiday resorts favored by the jet set, he envisioned the modern woman following a revolutionary intuition full of style and personality. He dressed such women with easy, immediate pieces that express a new idea of elegance: fast, joyful, spontaneous, effortless. He embraced, with a unique sense of color, the American idea of sportswear, the need for absolute comfort, which he combined with an innate taste for beauty and luxury. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202307099f1a0b3148784484.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090148a1adb3c646f82d.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T08:53:29.000Z", "updated_at": "2024-09-01T16:06:58.000Z"}, {"id": 163, "index": 300, "featured": 0, "title": "Emotionally Unavailable", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "emotionally-unavailable", "description": "Emotionally Unavailable was created with the intent of allowing hopeless romantics to express themselves and vent their love delusion. Born from the mind of the famous actor, rapper, and fashion icon <PERSON> and his co-founder <PERSON><PERSON>, Emotionally Unavailable focuses on text graphics and profound art messages placed on a selection of elevated everyday apparel. <PERSON> is well-known for being the co-founder of Hong Kong-based streetwear and fashion label CLOT, while <PERSON><PERSON> is the designer of the label Undefeated. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810a401b5e3c9a8eb38.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409061f1a5aa93bd9778e.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-06T03:41:55.000Z"}, {"id": 246, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "ermanno-scervino", "description": "Ermanno Scervino is an Italian fashion house headquartered in Florence, Italy. Entrepreneur <PERSON> and designer <PERSON><PERSON><PERSON> founded the fashion label in 2000.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202307096821be93c6dc5966.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409017a30cbc7077105a4.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T09:11:09.000Z", "updated_at": "2024-09-01T16:10:49.000Z"}, {"id": 323, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "estee-lauder", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410249e9246b859195f1f.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:02:44.000Z", "updated_at": "2024-10-24T03:44:20.000Z"}, {"id": 247, "index": 0, "featured": 0, "title": "ETRO", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "etro", "description": "Etro is a family-managed Italian luxury fashion house founded in 1968. It is mainly known for its paisley patterns, which the company started making in 1981.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709c4eee7d9519fed63.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901c169ba62b30078c1.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T09:17:30.000Z", "updated_at": "2024-09-01T16:18:26.000Z"}, {"id": 324, "index": 0, "featured": 0, "title": "FANCL", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "fancl", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410241ff09e84a946f873.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:03:03.000Z", "updated_at": "2024-10-24T03:44:24.000Z"}, {"id": 13, "index": 320, "featured": 0, "title": "FEAR OF GOD", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "fear-of-god", "description": "Fear of God is an independent American luxury fashion label, founded in 2013 in Los Angeles by <PERSON>. Crafting timeless, wearable garments, the brand’s distinct interpretation of the American expression has become an emblem of contemporary culture. Paying homage to the unique heritage with a contemplated and sophisticated fusion through high-grade materials and fine craftsmanship.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805088a44e88d4e3e01.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240829470f1cd058db2c8d.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-08-29T14:21:21.000Z"}, {"id": 19, "index": 330, "featured": 0, "title": "FEAR OF GOD Essentials", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "fear-of-god-essentials", "description": "Fear of God is an independent American luxury fashion label, founded in 2013 in Los Angeles by <PERSON>. Crafting timeless, wearable garments, the brand’s distinct interpretation of the American expression has become an emblem of contemporary culture. Paying homage to the unique heritage with a contemplated and sophisticated fusion through high-grade materials and fine craftsmanship.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008054d101f7102469237.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906186360b7f3ce31f6.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-06T03:47:45.000Z"}, {"id": 142, "index": 340, "featured": 11, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "fendi", "description": "It all began in a small boutique in the heart of Rome, founded in 1925 by <PERSON> and <PERSON><PERSON><PERSON>. Soon, with the opening of a specialised bag and fur workshop, a series of successful collections and special collaborations, the Maison is projected into the international fashion scene.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008051746e14f9c151d4a.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408276b6e013cedfd0ee0.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-08-27T12:46:24.000Z"}, {"id": 177, "index": 350, "featured": 0, "title": "Ferragamo", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "ferragamo", "description": "Salvatore Ferragamo S.p.A., is the parent Company of the Salvatore Ferragamo Group, one of the world's leaders in the luxury industry and whose origins date back to 1927. The Group is active in the creation, production and sale of shoes, leather goods, apparel, silk products and other accessories for men and women. The Group's product offer also includes eyewear, watches and perfumes, manufactured by licensees.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008102fa525a2bb06a5bd.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240827dbdf889b2a487acd.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-08-27T12:50:39.000Z"}, {"id": 152, "index": 360, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "fila", "description": "Fila was founded over 100 years ago in 1911 by two brothers who opened a textile shop, originally selling sewing materials to the local people of Biella at the foot of the Italian Alps. After a decade of notable growth and some gained experience, the brothers decide to expand their textile business into a knitwear manufacturing business, producing fine sweatshirts, scarves and jumpers.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008103845a80e86393ae2.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240829d609a23909fd6ab0.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-08-29T14:25:51.000Z"}, {"id": 325, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "filorga", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410244e8405e4300af972.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:03:40.000Z", "updated_at": "2024-10-24T03:44:29.000Z"}, {"id": 273, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "franck-muller", "description": "Franck Muller was founded in 1991 by Mr. <PERSON><PERSON><PERSON> and was rated as one of the top ten watch brands in the world. This famous watch brand from Switzerland has the reputation of \"Master of Complications\". The Franck Muller Classic Collection is known for its fusion of 1930s American modernity and traditional Swiss watchmaking. <PERSON><PERSON><PERSON>, chairman of the Franck Muller brand, claims that the mechanics of every Franck Muller watch are designed by himself.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024051440ad0a7d2aa27384.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905960b24fe5996b4ad.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:09:25.000Z", "updated_at": "2024-09-05T04:17:38.000Z"}, {"id": 326, "index": 0, "featured": 0, "title": "Fresh", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "fresh", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024902e35e91460828e.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:04:00.000Z", "updated_at": "2024-10-24T03:44:35.000Z"}, {"id": 274, "index": 0, "featured": 0, "title": "Girard-Perregaux", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "girard-perregaux", "description": "Girard-Perregaux is a Swiss Manufacture of Haute Horlogerie whose origins date back to 1791. The history of the Maison is marked by legendary watches, where meticulous design blends with technological innovation.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024051430e6bd569161dc3d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409058f08f1471f666566.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:09:50.000Z", "updated_at": "2024-09-05T04:20:27.000Z"}, {"id": 179, "index": 370, "featured": 0, "title": "Givenchy", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "givenchy", "description": "Givenchy is a French luxury fashion and perfume house. It hosts the brand of haute couture and ready-to-wear clothing, accessories, perfumes and cosmetics of Parfums Givenchy. The house of Givenchy was founded in 1952 by designer <PERSON> and is a member of Chambre Syndicale de la Haute Couture et du Prêt-à-Porter. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810f7e077b21194a753.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828863a18bf97cc020a.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-10-24T03:24:08.000Z"}, {"id": 275, "index": 0, "featured": 0, "title": "GLASHUTTE ORIGINAL", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "glash<PERSON>e-original", "description": "Glashütte Original’s roots stretch all the way back to 1845. More than 175 years ago, the first watchmakers came to Glashütte in Saxony to manufacture watch parts and watches in the small town. The Glashütte watch soon became a byword for high quality worldwide. Glashütte Original is descended from this tradition of precise, elegant craftsmanship, and continues to write the uninterrupted story of the art of German watchmaking to this day.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514b3302d9ee4a596a9.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090578b882691a00b28e.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:10:19.000Z", "updated_at": "2024-09-05T04:23:13.000Z"}, {"id": 194, "index": 380, "featured": 0, "title": "Golden Goose", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "golden-goose", "description": "Golden Goose is an Italian luxury lifestyle fashion brand, globally known for its iconic sneakers. The business was founded in Venice, Italy in 2000 by husband-and-wife duo <PERSON> and <PERSON>.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810fbd41bd6d406e4ff.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090463778055542728cc.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-04T12:44:05.000Z"}, {"id": 136, "index": 390, "featured": 0, "title": "Goyard", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "goyard", "description": "Maison Goyard, or simply Goyard, is a French trunk and leather goods maker established in 1792 as Maison Martin in Paris; the company also operated as Maison Morel, before becoming Maison Goyard in 1853. The brand is known for a certain amount of secrecy surrounding its products; little is known of the origins of the iconic interlocking Chevron pattern, seen on many Goyard bags.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805eaec347e71a82adb.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828bad729a1b6b21bdb.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-08-28T10:05:44.000Z"}, {"id": 133, "index": 400, "featured": 10, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "gucci", "description": "Since its founding in 1921, Gucci has transformed from a single shop opened on a side street in Florence to its position today as a world-renowned symbol of Italian craft, visionary creativity, and innovative design. Both mirroring and defining the decades that brought it forth, the House's history has itself influenced fashion and culture in indelible ways throughout the 20th and 21st centuries.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080554fd371438ffba2d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828aab63f5f556590a4.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:24:29.000Z"}, {"id": 327, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "guerlain", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024c87545df39deee3e.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:04:30.000Z", "updated_at": "2024-10-24T03:44:51.000Z"}, {"id": 276, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "hamilton", "description": "The Hamilton Watch Company is a Swiss manufacturer of wristwatches based in Bienne, Switzerland. Founded in 1892 as an American firm, the Hamilton Watch Company ended American manufacture in 1969, shifting manufacturing operations to the Buren factory in Switzerland. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202405142bb5fb69756ce28f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905aa774c96e476b4e1.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:10:45.000Z", "updated_at": "2024-09-05T04:29:37.000Z"}, {"id": 231, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "harry-winston", "description": "<PERSON>, Inc. is an American luxury jeweler and producer of Swiss timepieces. The company was founded in 1932 as Harry H<PERSON> Winston Jewels, Inc. and changed its name to Harry Winston Inc. in January 1936. The company is named after its founder, <PERSON>, who was called by many as the \"King of Diamonds\".  <PERSON> is widely regarded as one of the most prestigious jewelry manufacturers in the world.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210819807b4f042239dd32.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409035f70f89797800d8e.png", "public": 1, "enabled": 1, "created_at": "2021-08-19T05:09:27.000Z", "updated_at": "2024-09-03T13:45:52.000Z"}, {"id": 328, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "he<PERSON>-<PERSON><PERSON><PERSON>", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024459d63032c5038f3.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:04:58.000Z", "updated_at": "2024-10-24T03:44:58.000Z"}, {"id": 141, "index": 410, "featured": 3, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "hermes", "description": "The story began in 1837 when <PERSON><PERSON><PERSON> founded the family business. In those early days, long before the company would earn its fame for scarves and highly sought-after Birkin bags, <PERSON><PERSON><PERSON> served as a skilled maker of saddles and leather goods, primarily catering to European royal households.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008055c83fe633c963976.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408282c41cfdb37a9694b.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:24:45.000Z"}, {"id": 160, "index": 420, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "heron-preston", "description": "n 2016 <PERSON> collaborated with the NYC Sanitation Department (DSNY) to create UNIFORM. <PERSON><PERSON> approached the Department after a brush with garbage while swimming left him disenchanted with streetwear’s “vacant messaging.” The collection featured handpicked donated clothing and authentic DSNY uniforms upcycled with screen printing and embroidery and an official label hand sewn into every piece. Part of the proceeds from the collection went to the Foundation for New York’s Strongest — a nonprofit organization dedicated to honoring the service of sanitation workers in New York City.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020081083a0fe722e55c298.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409065a1708ad71cec6ce.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-06T03:50:43.000Z"}, {"id": 225, "index": 0, "featured": 0, "title": "Hoka", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "hoka-streetwear", "description": "Hoka One One (stylized as HOKA) is a sportswear company that designs and markets running shoes. It was founded in 2009 in Annecy, France, and had been based in Richmond, California before it was acquired by Deckers Brands in 2013. Hoka first gained attention in the running industry by producing shoes with oversized midsoles, dubbed \"maximalist\" shoes, in contrast to the minimalist shoe trend that was gaining popularity at the time.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2021031530f3a5d812750955.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906c08a76bbf188c815.png", "public": 1, "enabled": 1, "created_at": "2021-03-15T07:10:11.000Z", "updated_at": "2024-09-06T03:52:00.000Z"}, {"id": 159, "index": 430, "featured": 0, "title": "Hoka", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "hoka-sneakers", "description": "Hoka One One (stylized as HOKA) is a sportswear company that designs and markets running shoes. It was founded in 2009 in Annecy, France, and had been based in Richmond, California before it was acquired by Deckers Brands in 2013. Hoka first gained attention in the running industry by producing shoes with oversized midsoles, dubbed \"maximalist\" shoes, in contrast to the minimalist shoe trend that was gaining popularity at the time.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008101bb1850c3385031e.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408290302e968ed04f62e.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-08-29T14:29:09.000Z"}, {"id": 277, "index": 0, "featured": 0, "title": "HUBLOT", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "hublot", "description": "The luxury watch brand Hublot based in Nyon, Switzerland was founded in 1980 by the Italian <PERSON>. The company founder was able to combine Italian design with Swiss craftsmanship and produce unconventional products.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514f34145456bb0f77b.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905d07a486b338b30e1.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:11:07.000Z", "updated_at": "2024-09-05T04:31:59.000Z"}, {"id": 248, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "hugo-boss", "description": "<PERSON> Boss was founded in 1924 in Metzingen, Germany by <PERSON> himself. Initially a clothing company specializing in uniforms and workwear, the brand shifted its focus to men's suits in the 1960s and expanded to include women's wear in the 1990s. Today, Hugo Boss is known for its high-quality, modern designs and has become a global fashion powerhouse, with stores in over 120 countries worldwide. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2023070988e1f3f638dc14ea.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901ae6435d6bd057eb7.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T09:22:40.000Z", "updated_at": "2024-09-01T16:21:59.000Z"}, {"id": 196, "index": 0, "featured": 0, "title": "Human Made", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "human-made", "description": "Launched in 2010, Human Made sprang from the visionary mind of NIGO®, the founder of the revolutionary streetwear brand 'A Bathing Ape' (BAPE). Collaborating with <PERSON><PERSON><PERSON>, NIGO® set out to create a brand that not only pays tribute to vintage aesthetics but also charts a unique course in the fashion world.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20201116a1ec967a38c4ccfe.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090651f6d934d7acf3b4.png", "public": 1, "enabled": 1, "created_at": "2020-11-16T10:15:23.000Z", "updated_at": "2024-09-06T03:54:36.000Z"}, {"id": 211, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "is<PERSON>-miyake", "description": "Since designer <PERSON><PERSON> presented his first collection in 1971, ISSEY MIYAKE has been designing and making clothing that explores the relationship—the ease and ma (the unfilled space)—between the wearer’s body and the fabric of the wear, which is founded upon the brand’s unwavering approach to the concept of a piece of cloth, thereby developing clothing that is universal and unbound by the Western vs. Eastern conventions of clothing design. Today, the brand continues to engage in research that begins with developing yarn from which original fabrics are created.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210122ba980076ba77d380.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828f44d468667acc43c.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:21:39.000Z", "updated_at": "2024-10-29T11:57:40.000Z"}, {"id": 279, "index": 0, "featured": 0, "title": "IWC", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "iwc", "description": "Founded in 1868, IWC differs from most large watch making companies as it was not created by a European watchmaker. It was in fact <PERSON><PERSON><PERSON><PERSON>, a 27-year-old American that started the company. He was a watchmaker who resided in Boston and decided that the combination of American production techniques and world-renowned Swiss craftsmanship would be powerful.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514ce316c2664b85ed4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905b2f6831777c5f7e7.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:12:20.000Z", "updated_at": "2024-09-05T04:35:27.000Z"}, {"id": 280, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>-LeCoultre", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Jaeger-LeCoultre is a Swiss luxury watch brand with a rich history that dates back to 1833. Known for its technical innovation and exceptional craftsmanship, Jaeger-LeCoultre has established itself as a pioneer in the world of watchmaking.  The brand’s popular models include the iconic Reverso, a classic Art Deco-inspired watch with a reversible case, and the Master Control, a contemporary timepiece with a clean and elegant design. Each watch is crafted with precision and expertise, using only the finest materials and cutting-edge techniques, resulting in a truly exceptional timepiece.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514f16a0a74e499f32a.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409051b4437c634ef3d84.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:12:41.000Z", "updated_at": "2024-09-05T04:39:34.000Z"}, {"id": 350, "index": 0, "featured": 0, "title": "JellyCat", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "jellycat", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20250210d9c3bc4b85705e56.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2025-02-10T11:08:50.000Z", "updated_at": "2025-02-10T11:22:02.000Z"}, {"id": 193, "index": 440, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "jimmy-choo", "description": "<PERSON> Choo is a British luxury fashion house specialising in shoes, handbags, accessories and fragrances. The company, J. Choo Limited, was founded in 1996 by Malaysian Chinese couture shoe designer <PERSON> and British Vogue accessories editor <PERSON>. The brand claims to have been a favourite of <PERSON>, Princess of Wales.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810059de9b06b5a43bb.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240904cc4308ee20e558d5.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-04T12:47:10.000Z"}, {"id": 329, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "jo-malone", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024309458034db38c65.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:05:22.000Z", "updated_at": "2024-10-24T03:45:35.000Z"}, {"id": 330, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "juicy-couture", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102431d0ee6e092f8404.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:05:46.000Z", "updated_at": "2024-10-24T03:45:58.000Z"}, {"id": 250, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "just-cavalli", "description": "Besides the main line, which is sold in over fifty countries worldwide, <PERSON> designs RC Menswear, the youth-aimed line Just Cavalli, launched in 1998 and comprising menswear, womenswear and accessories, perfumes, underwear and beachwear.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202307097b646381bce84352.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240902326a8e1dfcff4f1b.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T09:41:10.000Z", "updated_at": "2024-09-02T11:55:48.000Z"}, {"id": 169, "index": 450, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "kapital", "description": "Founded in 1984 by <PERSON><PERSON><PERSON><PERSON> in Kojima, a semi-industrial prefecture on the outskirts of Okayama that’s often referred to as the Denim Capital of Japan, the brand embodies a mish-mash of these elements, blending streetwear, luxury and heritage through a distinctly Japanese lens. It’s that same daring and versatility, however, that makes Ka<PERSON><PERSON> so daunting to the uninitiated.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810581171adfafbf4cd.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090601d9f88217bdf357.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-06T03:56:56.000Z"}, {"id": 198, "index": 0, "featured": 0, "title": "KAWS", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "kaws", "description": "<PERSON><PERSON><PERSON>, aka <PERSON>, is an American graffiti artist and designer. Born in Jersey City, he’s been at his craft since the late 80s.   His larger-than-life sculptures, colorful paintings, and limited edition toy collections have broken the mold of street art and hypebeast culture. They’ve created a unique niche in the fine art industry.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202012073ea806c1cc33d123.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240903de83a957be5dfeac.png", "public": 1, "enabled": 1, "created_at": "2020-12-07T07:23:16.000Z", "updated_at": "2024-09-03T13:29:12.000Z"}, {"id": 331, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "kenzo", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024d79b27a34501ca63.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:06:08.000Z", "updated_at": "2024-10-24T03:46:04.000Z"}, {"id": 332, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>'s", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "k<PERSON><PERSON>'s", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410243c4aea061382d22a.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:06:37.000Z", "updated_at": "2024-10-24T03:46:11.000Z"}, {"id": 11, "index": 460, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "kith", "description": "Established in 2011 in New York City, Kith is a lifestyle brand and progressive retail concept. Founded by Creative Director <PERSON>, Kith offers seasonal collections of men’s, women’s and children’s apparel, accessories and footwear through a distinct lens of personal storytelling, effortless styling, and uncompromising detail to fabrication and design. <PERSON><PERSON> embodies a multi-faceted lifestyle, working closely alongside collaborators around the world who are leaders in their respective product categories.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805fc3006ba76a0e75b.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906b46686d21eb4c0e4.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-06T03:59:29.000Z"}, {"id": 334, "index": 0, "featured": 0, "title": "Lalique", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "lalique", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024dbc4222b0beb3c89.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:07:52.000Z", "updated_at": "2024-10-24T03:45:42.000Z"}, {"id": 333, "index": 0, "featured": 0, "title": "LAMER", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "lamer", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410244feb07b0462f2ac1.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:07:31.000Z", "updated_at": "2024-10-24T03:56:17.000Z"}, {"id": 335, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "la<PERSON><PERSON>", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102460ce0102a45e5e34.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:08:17.000Z", "updated_at": "2024-10-24T03:45:49.000Z"}, {"id": 232, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "lanvin", "description": "Lanvin is a French fashion house that was founded in Paris in 1889 by <PERSON>. Born in 1867, <PERSON><PERSON><PERSON> was the eldest of 11 children and grew up in a working-class family. She began her career as a milliner, creating hats for the fashionable women of Paris.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20211004609bd253ccf5478f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409027e6a08015d233252.png", "public": 1, "enabled": 1, "created_at": "2021-10-04T04:33:06.000Z", "updated_at": "2024-10-24T03:24:56.000Z"}, {"id": 153, "index": 470, "featured": 0, "title": "<PERSON><PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "li-ning", "description": "Li-Ning Company Limited is a Chinese sportswear and sports equipment company founded by former Olympic gymnast <PERSON>. The company endorses a number of athletes and teams worldwide.  The company was founded in 1989 by <PERSON>, a former Chinese Olympic gymnast.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020081068b2f3741cab9c74.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240829cd7acd98e51d5ad5.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-08-29T14:32:15.000Z"}, {"id": 174, "index": 480, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "loewe", "description": "Founded in Spain in 1846, we’re approaching 178 years as one of the world’s major luxury houses.  From our humble beginnings as a leathermaking collective to becoming a leading global brand, our journey has always been defined by an obsessive focus on craftsmanship and an unmatched expertise with leather. Based out of our main workshop in Madrid, which remains in operation to this day, our master artisans combine their accumulated craft knowledge with new technologies and innovative ways of thinking to produce truly modern objects of desire.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810410cdd6c1e9f6f31.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408280aec8ff1abb2ced0.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-10-24T03:25:08.000Z"}, {"id": 249, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "longchamp", "description": "Longchamp is a French leather goods company, founded in Paris in 1948 by <PERSON>. The company pioneered luxury leather-covered pipes before expanding into small leather goods. Longchamp debuted women's handbags in 1971, becoming one of France's leading leather goods makers. Today, the company designs and manufactures leather and canvas handbags, luggage, shoes, travel items, fashion accessories, and women's ready-to-wear.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202307093a61f7b43a6f875f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828ce762d61c64e1868.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T09:35:04.000Z", "updated_at": "2024-08-28T10:23:37.000Z"}, {"id": 281, "index": 0, "featured": 0, "title": "LONGINES", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "longines", "description": "The history of the Longines brand started in 1832 in Saint-Imier, when <PERSON> founded his watchmakers’ concern. In this type of establishment, known as a comptoir d’établissage, peasants made the watches at home, and <PERSON><PERSON><PERSON><PERSON> would then finish the assembly and sell them. But the firm already had an international reach at the time, selling watches in as far flung places as the United States of America.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202405143356cb2b9d499c73.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905af579c2f66061e87.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:13:10.000Z", "updated_at": "2024-09-05T04:42:23.000Z"}, {"id": 255, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "loro-piana", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709dca729155e7122a9.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2023-07-09T12:12:16.000Z", "updated_at": "2024-10-28T10:54:19.000Z"}, {"id": 132, "index": 490, "featured": 1, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "description": "<PERSON>, founded in 1854, is a renowned French luxury fashion house known for its iconic monogram canvas and exquisite craftsmanship. Renowned for its timeless designs, exceptional quality, and global appeal, <PERSON> continues to be a symbol of elegance and sophistication.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080577a4ee919f3f54da.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408226b41826d1d6dc8cf.jpg", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-08-22T09:31:21.000Z"}, {"id": 349, "index": 0, "featured": 0, "title": "MAC", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "mac", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102432a8005941879faf.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T03:35:49.000Z", "updated_at": "2024-10-24T03:44:10.000Z"}, {"id": 218, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "maison-margiela", "description": "Maison Margiela is a French luxury fashion house founded by Belgian designer <PERSON> and <PERSON> in 1988 and headquartered in Paris. The house produces both haute couture-inspired artisanal collections and ready-to-wear collections, with the former influencing the designs of the latter. Product lines include womenswear, menswear, jewellery, footwear, accessories, leather goods, perfumes and household goods", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210122d3786c3980825cfd.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090223f82db482e13d7f.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:30:54.000Z", "updated_at": "2024-10-24T03:25:36.000Z"}, {"id": 336, "index": 0, "featured": 0, "title": "MAKE UP FOR EVER", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "make-up-for-ever", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024796339787a18c9a7.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:08:50.000Z", "updated_at": "2024-10-24T03:46:24.000Z"}, {"id": 164, "index": 510, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "marcelo-burlon", "description": "<PERSON><PERSON>lon, the fashion brand he founded in 2012 that bears his name.  The genesis of Marcelo Burlon - County of Milan was a t-shirt project that quickly transformed into a comprehensive brand that uniquely blended fashion, clubbing, and merchandise creating a blueprint that would become the new paradigm of the industry with <PERSON>, who was among <PERSON><PERSON><PERSON>'s past collaborators. His designs often draw inspiration from the culture of his native Patagonia, mixing traditional crafts with a modern, techno-folk aesthetic.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008107df7407629264aeb.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906c47fb9ade59784ad.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-06T04:03:25.000Z"}, {"id": 180, "index": 520, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "marni", "description": "MARNI is a luxury fashion brand founded in Milan in 1994. Innovative and multifaceted, <PERSON><PERSON> celebrates individuality through an unpredictable visual language. Marni’s is an artistic, life-embracing, colour-savvy, off-beat world of luxury that expresses a unique flair for mixing prints and shapes.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810d78222a06fbafc1d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828ccda3a24b5319428.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-08-28T10:27:15.000Z"}, {"id": 182, "index": 530, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "max-mara", "description": "The Max Mara Group was founded in 1951 by <PERSON><PERSON><PERSON>. With great vision and business acumen, <PERSON><PERSON><PERSON> decided to specialize in the production of coats, stylistically inspired by the most sophisticated French Haute Couture manufactured with innovative industrial tailoring techniques. <PERSON><PERSON><PERSON> created sophisticated garments such as the camel coat and geranium red suit of the first collection. The idea was to clothe the new middle class population. “Made in Italy” style made its mark on the world as a result of the Max Mara brand. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020081078aeefdd42245817.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090273cf8a9f49e2f017.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-02T12:17:24.000Z"}, {"id": 134, "index": 540, "featured": 0, "title": "MCM", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "mcm", "description": "MCM is a luxury fashion brand originally founded in 1976 as the initialism of \"Michael Cromer Munich\". The brand’s signature logo-printed material, called Cognac Visetos, appears on many of its products. Its brass plate insignia is found on all heritage collection bags and most products; each brass plate is identified by a unique number at the bottom.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805b72c4ef8dfc320ff.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828b9d8cee6ad599b4d.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:26:31.000Z"}, {"id": 213, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> is a world-renowned, award-winning designer of luxury accessories and ready-to-wear. His namesake company, established in 1981, currently produces a range of products under his signature Michael <PERSON> Collection, MICHAEL <PERSON> and <PERSON> Mens labels. These products include accessories, footwear, watches, jewelry, women’s and men’s ready-to-wear, wearable technology, eyewear and a full line of fragrance products.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2021012267c8264db9a35512.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024082883cf7951c39d0e56.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:27:59.000Z", "updated_at": "2024-08-28T10:32:40.000Z"}, {"id": 282, "index": 0, "featured": 0, "title": "MIDO", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "mido", "description": "Mido was founded in 1918 by <PERSON> in Biel/Bienne, Switzerland. In the 1920s, Mido introduced ladies' watches with color-enameled shaped cases and modern straps as well as timepieces for gentlemen in art deco style. Mido found a market in the flourishing automotive market by producing watches in the shape of radiator grills of a wide range of brands such as Buick, Bugatti, Fiat, Ford, Excelsior, Hispano-Suiza, etc.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202405143b0f267624ef30e0.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905f1850b8d7649e93a.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:13:31.000Z", "updated_at": "2024-09-05T04:45:43.000Z"}, {"id": 157, "index": 500, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "MIHARAYASUHIRO started as a shoe brand in 1997. <PERSON><PERSON>’s design extracting everyone’s focus by his unique and surprising ideas. He always thinks out of box to bring people enjoy the playful theme of each garments in every collection.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810c2009de51c638344.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901d5d5d47e2f131d74.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T12:51:30.000Z"}, {"id": 208, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "mitchell-ness", "description": "It was founded in 1904 in Philadelphia by <PERSON><PERSON><PERSON> and <PERSON>. Firstly they specialized in stringing tennis racquets, constructing custom-made golf clubs and making uniforms for local Philadelphia baseball and football teams.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202101226ad491bf87d43a12.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906bc7afdeedd416916.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:19:43.000Z", "updated_at": "2024-09-06T04:05:24.000Z"}, {"id": 173, "index": 550, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> was established in 1992 by <PERSON><PERSON><PERSON>. The name was conceived from <PERSON><PERSON><PERSON>'s family nickname. It was publicly launched in 1993, with a cowgirl-themed collection of fringed suede jackets and patchwork prairie skirts.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810bc198be4ea125b0a.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828301d56c16293c038.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-10-24T03:27:13.000Z"}, {"id": 209, "index": 0, "featured": 0, "title": "MLB", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "mlb", "description": "In 1997, F&F has obtained a permit to operate the MLB Korea brand which selling athletic street style apparel, by Major League Baseball. MLB Korea has inspired by the traditional sport – Baseball and develops it into a fashionable sportswear brand. MLB is all about bringing together “Sport”, “Fashion” and “Culture”. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210122e685190fa4ebeac4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901a002bcf00afc5949.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:20:14.000Z", "updated_at": "2024-09-01T13:00:05.000Z"}, {"id": 183, "index": 560, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON>", "description": "The Moncler brand was born in 1952 in Monestier-de-Clermont, a small village in the mountains near Grenoble, with a focus on sports clothing for the mountain.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810cbf838b6d7b9e018.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240902a8203c9b2414dbce.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-02T12:20:00.000Z"}, {"id": 215, "index": 0, "featured": 0, "title": "Montbla<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "montblanc", "description": "Montblanc is a German manufacturer and distributer of luxury goods, founded in Berlin in 1906, and currently based in Hamburg. The company is most known for its luxury pens; it also designs and distributes bags, perfumes, small leather goods, and watches. Since 1993, Montblanc has been part of the Swiss Richemont group.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210122134ffe30a8b7405a.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828845172aef331acd9.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:29:17.000Z", "updated_at": "2024-10-24T03:29:30.000Z"}, {"id": 265, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "moose-knuckles", "description": "Moose Knuckles, a Canadian brand well-known for producing high-quality, heavy-duty outerwear and modern apparel tailored for urban living. Founded in 2009, Moose Knuckles emerged with a mission to create the “Leanest, toughest and most luxurious sportswear”. Since then, Moose Knuckles has established itself as a brand with a strong presence and impeccable craftsmanship, exquisite designs, and outstanding quality.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202403121d9495939c6145a4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090250feb4bd47e9828d.png", "public": 1, "enabled": 1, "created_at": "2024-03-12T14:01:07.000Z", "updated_at": "2024-09-02T12:24:15.000Z"}, {"id": 207, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "moschino", "description": "<PERSON><PERSON><PERSON> was founded in Italy in 1983 by <PERSON>. Originally aspiring to be a painter, he translated his love for prints and color into fashion design. After collaborating with Italian fashion house Versace as an illustrator, the <PERSON><PERSON><PERSON><PERSON> graduate started with his own namesake brand. Soon, <PERSON><PERSON><PERSON> became known for interpreting classic styles of other houses, giving them a twist by adding confidence and boldness to the designs.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210122f7583ddd2d421ab9.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906e402df9f70ace005.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:18:01.000Z", "updated_at": "2024-09-06T04:07:17.000Z"}, {"id": 263, "index": 0, "featured": 0, "title": "MOYNAT", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "moynat", "description": "Moynat is a Parisian trunkmaker, founded in Paris in 1849 by <PERSON><PERSON><PERSON> and <PERSON>. They collaborated with specialist <PERSON> in travel goods to open the company's first store at Avenue de l'Opera, France. The house participated in various World's Fairs.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202403035c20666dc9284fb7.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828f7c6bdeed1117670.png", "public": 1, "enabled": 1, "created_at": "2024-03-03T10:34:23.000Z", "updated_at": "2024-08-28T10:54:35.000Z"}, {"id": 264, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "mulberry", "description": "Mulberry Group PLC is a British fashion company founded in 1971, best known for its luxury leather goods, particularly women's handbags. The hallmarks of these Mulberry creations - timeless design coupled with traditional quality and a sense of the here and now - are the threads that run through everything we make. Then, today and tomorrow.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024031249e1659968fef1ff.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024082823162f7762f1c904.png", "public": 1, "enabled": 1, "created_at": "2024-03-12T13:53:26.000Z", "updated_at": "2024-08-28T10:57:33.000Z"}, {"id": 302, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "n-hoolywood", "description": "Obana's N.Hoolywood label made its debut in Tokyo in 2002. Ever since then, it has been rising toward becoming a world-renowned fashion brand. Obana's designs are based off of particular themes. His debut theme was \"Reclamation\", based on the theme of used clothing. It featured clothes that were oversized or baggy. Subsequent themes have included the 1960s, Apollo 11, activists, and Alaskan culture.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919d1081ce1b99ba3a3.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409198a49b3a51c0d009d.png", "public": 1, "enabled": 1, "created_at": "2024-09-19T06:16:44.000Z", "updated_at": "2024-09-19T09:59:43.000Z"}, {"id": 303, "index": 0, "featured": 0, "title": "Nanamica", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "nanamica", "description": "Nanamica is an outdoors-centric casualwear fashion label based in Tokyo, Japan, founded by <PERSON><PERSON><PERSON><PERSON>, in 2003. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409192a873031fef59e99.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919cf7a377b80148c67.png", "public": 1, "enabled": 1, "created_at": "2024-09-19T06:17:28.000Z", "updated_at": "2024-09-19T10:04:28.000Z"}, {"id": 337, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "narcis<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102451ec8de247ae1fc5.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:09:15.000Z", "updated_at": "2024-10-24T03:46:30.000Z"}, {"id": 338, "index": 0, "featured": 0, "title": "Nars", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "nars", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410249c711e0ace5fc6d1.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:09:38.000Z", "updated_at": "2024-10-24T03:46:36.000Z"}, {"id": 308, "index": 0, "featured": 0, "title": "Nautica", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "nautica", "description": "Nautica’s story began in 1983 when founder and designer, <PERSON>, had a vision to create apparel which embodies the inspiration of the sea in everyday style around the world.  Nautica began with only six outerwear styles but quickly grew into a renowned lifestyle brand globally, with products being sold in over 65 countries across the world. Nautica continues to be defined by its unique nautical style, incorporating the water and waves in each design.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409199a2794f919be55fa.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024091977723ed57dd7b4ca.png", "public": 1, "enabled": 1, "created_at": "2024-09-19T06:23:34.000Z", "updated_at": "2024-09-19T10:13:43.000Z"}, {"id": 223, "index": 0, "featured": 0, "title": "needles", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "needles", "description": "Needles is a Japanese fashion brand founded by Japanese designer <PERSON><PERSON><PERSON> in 1997. It was to complement the product range in his independent NEPENTHES boutique store. Needles clothing designs take elements from casual American influence, such as outdoor-, and workwear clothing, mixing them with a more traditional Japanese fashion to create a unique look.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210315f4d4acb749b9f43d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906ce4cb7b652de8055.png", "public": 1, "enabled": 1, "created_at": "2021-03-15T06:40:56.000Z", "updated_at": "2024-09-06T04:09:45.000Z"}, {"id": 168, "index": 570, "featured": 0, "title": "NEIGHBOURHOOD", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "neighbourhood", "description": "<PERSON><PERSON> launched his brand Neighborhood back in 1994. Neighborhood makes it their mission to produce “basic clothing created by digesting unique interpretations of elements from motorcycles, military, outdoor, trad, etc. and also suggesting this lifestyle.” ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020081069409c846198596e.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906c44eacff8b31ef0c.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-06T04:15:47.000Z"}, {"id": 150, "index": 580, "featured": 0, "title": "New Balance", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "new-balance", "description": "New Balance one of the very first sneaker brands was born in Massachusetts in 1906, long before Puma, Nike or Adidas, under the exact name of The New Balance Arch Company. <PERSON>, a shoemaker by trade, created the brand with the aim of helping people with foot problems. The first activity of New Balance was therefore the development of orthopaedic insoles designed to provide better support for the arch of the foot. The name \" New Balance \" was directly inspired by the shape of a chicken's feet. By observing the chicken's feet, <PERSON> realised that their 3 points of support offered a perfect balance of balance and force distribution, which was very useful in the creation of his orthopaedic insoles.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810830ddee3b62e42ea.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409010ba2d93b168da924.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T13:03:52.000Z"}, {"id": 2, "index": 590, "featured": 9, "title": "Nike", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "nike", "description": "Rubber-soled shoes were first mass-marketed as canvas top \"sneakers\" by U.S. Rubber, with its Keds® in 1917. But the elevation of athletic shoe manufacturing to both a science and a fashion was largely due to <PERSON> and <PERSON> of Oregon.  In 1958, <PERSON>, a business major at the University of Oregon and a miler on the track team, shared with his coach, <PERSON>, a dissatisfaction with the clumsiness of American running shoes. They formed a company in 1964 to market a lighter and more comfortable shoe designed by <PERSON><PERSON>. In 1968, this company became NIKE, Inc. – named for the Greek goddess of Victory.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008057146e477927daff3.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090106098ac564ae0571.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:16.000Z", "updated_at": "2024-09-01T13:26:48.000Z"}, {"id": 16, "index": 600, "featured": 0, "title": "NOAH", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "noah", "description": "Noah is an American men's clothing brand founded by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, based out of its flagship store on 195 Mulberry St. in Soho, New York City. <PERSON><PERSON>don’s free-thinking vision has long been at the heart of the movement merging the rebellious vitality of skate, surf, and music cultures with an innovative appreciation of classic menswear. <PERSON> was created to realize this vision fully, in uncompromising pursuit of quality, integrity, and originality.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008051bef1e73c0cf68cf.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906acd01c01f988c8fa.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-06T04:18:15.000Z"}, {"id": 14, "index": 610, "featured": 0, "title": "OFF-WHITE", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "off-white", "description": "<PERSON> founded Off-White™ in 2013 as a multi-platform creative endeavor, a space for his continuous experimentation. The main medium being fashion yet he combined ideas of streetwear, luxury, art, music, and architecture. <PERSON>'s visionary approach to the medium of fashion remains iconoclastic and profoundly conceptual yet accessible to a broad global audience.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805ec539ed237d3a62a.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409017effdd414a95ea16.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-10-24T18:25:07.000Z"}, {"id": 283, "index": 0, "featured": 0, "title": "OMEGA", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "omega", "description": "Founded in 1848, Omega is a watch brand synonymous with excellence, innovation and precision. The company has constantly been defined by its pioneering spirit, demonstrated by its conquests of the oceans as well as space. Since 1965, the Omega Speedmaster has been worn on each of NASA's piloted missions including all six moon landings and many of history’s greatest explorations beyond Earth.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202405141d4f448cfac2eac3.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905b3dc785541f6dd66.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:13:50.000Z", "updated_at": "2024-09-05T04:48:58.000Z"}, {"id": 262, "index": 0, "featured": 0, "title": "Onitsuka Tiger", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "onitsuka-tiger", "description": "Onitsuka Co., Ltd. was established with the purpose of contributing to the development of Japan's youth and rebuilding a better future for them. In pursuit of technical strength and comfort, we have brought to the world innovative sports shoes. In 1977, the company name changed and the brand temporarily disappeared.  However, supported by the retro boom, Onitsuka Tiger was revived as a fashion brand in Europe in 2002. We continue to innovate with fashion items in heritage styles arranged from archives and high-end mode ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024011524a2ca4df70ffc32.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409013e2d122008cd9e3a.png", "public": 1, "enabled": 1, "created_at": "2024-01-15T08:29:17.000Z", "updated_at": "2024-09-01T13:39:51.000Z"}, {"id": 339, "index": 0, "featured": 0, "title": "origins", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "origins", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410247c5a95f5f3eb85f6.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:10:01.000Z", "updated_at": "2024-10-24T03:46:49.000Z"}, {"id": 12, "index": 620, "featured": 0, "title": "Palace", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "palace", "description": "Palace is a London-based skateboarding and clothing brand established in 2009. The brand was founded by <PERSON> and <PERSON>. Palace focuses on skater fashion with heavy 90s and pop culture influences alongside VHS style clothing advertisements.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805e322208d04d93118.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409063cae3baded3c8e21.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-06T04:20:43.000Z"}, {"id": 199, "index": 0, "featured": 0, "title": "Palm Angels", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "palm-angels", "description": "Palm Angels is founded by <PERSON>, who worked as an art director at Moncler. The brand is heavily influenced by the Californian skateboarding culture, bits and pieces of 70s preppy chic can also be picked up in <PERSON>'s designs.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020123101a7d916f8e2745c.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409066f73c4b4ad82085b.png", "public": 1, "enabled": 1, "created_at": "2020-12-31T05:09:51.000Z", "updated_at": "2024-09-06T04:24:10.000Z"}, {"id": 284, "index": 0, "featured": 0, "title": "PANERAI", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "panerai", "description": "The Origins of Panerai. The concern known today as Officine Panerai was established in Florence in 1860 –one year before the formation of the modern Italian state. At the time, its founder, <PERSON>, established an office combining the functions of a watchmaking school, a repair workshop, and a sales showroom.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202405146ba5c92e9e35f065.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409051cd903471518cd1a.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:14:10.000Z", "updated_at": "2024-09-05T04:50:47.000Z"}, {"id": 285, "index": 0, "featured": 0, "title": "Parmigiani Fleurier", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON><PERSON>-fleurier", "description": "<PERSON>, a young watchmaker and restorer, opens in 1976 “Mesure et Art du Temps,” a traditional workshop devoted to restoring antique timepieces. While plying this trade, he indulges his creative impulses by designing and crafting complex timekeeping creations of his own.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514b1121584cf909073.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090503bc325e8acce3d5.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:14:30.000Z", "updated_at": "2024-09-05T04:53:26.000Z"}, {"id": 286, "index": 0, "featured": 0, "title": "PATEK PHILIPPE", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "patek-phil<PERSON><PERSON>", "description": "<PERSON><PERSON> Philippe, now a world-renowned brand, had humble beginnings. In 1835, the brand's founder, <PERSON>, struggled to make a living by reselling watches to his Polish relatives. However, through hard work and determination, he was able to make significant contributions to the field of watchmaking and eventually establish Patek Philippe as one of the top watch brands in Switzerland.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202405146e70f828688429b2.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090556cc3fcc370e644e.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:14:50.000Z", "updated_at": "2024-09-05T04:55:56.000Z"}, {"id": 251, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "patrizia-pepe", "description": "The passion of <PERSON><PERSON><PERSON> (Creative Director) combined with the entrepreneurial spirit of <PERSON> (President), culminated in the creation of Patrizia <PERSON>. Florence, 1993. The name “<PERSON><PERSON><PERSON>” was immediately identified with an irreverent sensuality, made of contrasts and opposites. From this moment on, women could now recognise themselves in a brand which seamlessly combines everyday practicality with glamour for all those important moments, from morning to evening, thanks to the collection’s dual versatility and cutting silhouettes. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709c954bc58340e9b08.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240902352b3394fbdc880b.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T09:51:31.000Z", "updated_at": "2024-09-02T12:30:17.000Z"}, {"id": 340, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "paul-smith", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410240d652e4940d9459b.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:10:25.000Z", "updated_at": "2024-10-24T03:47:00.000Z"}, {"id": 341, "index": 0, "featured": 0, "title": "Penhaligon's", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "penhali<PERSON>'s", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410244941a22353bb90fe.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:10:43.000Z", "updated_at": "2024-10-24T03:46:55.000Z"}, {"id": 230, "index": 0, "featured": 0, "title": "Piaget", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "pia<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> felt a passion for watchmaking from a young age, in 1874 at only 19 he set up his first workshop on the family farm. Here, he dedicated himself to creating high precision movements and components. Even in these early days, his motto \"always do better than necessary\" is what pushed him to have a self-surpassing attitude that would continue to motivate <PERSON><PERSON><PERSON> today. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210819d0b32b4fa58d73d7.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240903aaf8795bef53d1b7.png", "public": 1, "enabled": 1, "created_at": "2021-08-03T09:48:14.000Z", "updated_at": "2024-09-03T13:50:21.000Z"}, {"id": 214, "index": 0, "featured": 0, "title": "Pink<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "pinko", "description": "PINKO is an innovative contemporary fashion brand that relies completely upon research and quality of Italian handcrafted fabrics, for original creations that are easy to wear.  Pinko clothing was founded in Italy, in the late 1980s, by <PERSON> and his wife <PERSON>, with the aim of creating a brand for daring, independent women.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2021012257991d6058eb1c90.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828f8334a2162d29e3b.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:28:33.000Z", "updated_at": "2024-08-28T11:02:25.000Z"}, {"id": 299, "index": 0, "featured": 0, "title": "Pokemon", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "pokemon", "description": "The first set of cards was released on October 20, 1996, containing 102 cards, with drawings by <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>. The cards soon became very popular, and three years later in 1999, they were introduced to North America by Wizards of the Coast and shortly after the rest of the world.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240911bd3d306b7cdba1c0.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919478a9315d44c1c06.png", "public": 1, "enabled": 1, "created_at": "2024-09-11T10:47:29.000Z", "updated_at": "2024-09-19T09:41:40.000Z"}, {"id": 144, "index": 630, "featured": 10, "title": "Prada", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "prada", "description": "Prada was founded in 1913 by <PERSON> and his brother <PERSON><PERSON> as Fratelli <PERSON>, a leather goods shop in Milan. The first store opened in the prestigious Galleria Vittorio Emanuele II in Milan (that is still functioning today) where they sold bags, trunks, and travel accessories. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008055331104ef59a61ad.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024082892e85f1c553ca1cb.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:29:42.000Z"}, {"id": 6, "index": 640, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "puma", "description": "PUMA is relentlessly pushing sports and culture forward by creating the fastest products for the world’s fastest athletes. Since 1948, PUMA has drawn strength and credibility from its heritage in sports. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008053dbb4b15bee7f021.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409017aff7f8ca7dea966.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:48:45.000Z", "updated_at": "2024-09-01T13:42:47.000Z"}, {"id": 287, "index": 0, "featured": 0, "title": "RADO", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "rado", "description": "As an innovative watchmaker with roots going back to 1917, <PERSON><PERSON> strives to capture the present and shape the future. To this end, we work tirelessly on the development of new materials, colours and shapes at our headquarters in Lengnau, Switzerland, and around the world. After all, there is a reason why we are referred to as the “Master of Materials”.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514f3f402fe5c08c1e2.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409053d0012db351f4d36.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:15:58.000Z", "updated_at": "2024-09-05T04:59:31.000Z"}, {"id": 7, "index": 650, "featured": 0, "title": "Reebok", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "reebok", "description": "The Reebok brand has been around longer than you likely realize. The brand was first founded in 1895 in Bolton, England by <PERSON>. Before the brand was “Reebok,” it was named <PERSON><PERSON><PERSON><PERSON>, after the founder. At the time, Reebok was one of the first businesses to produce sports shoes and these shoes were made by hand. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080583ba883e7e18d1b7.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409019d4ec88688e4e615.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:48:55.000Z", "updated_at": "2024-09-01T13:53:30.000Z"}, {"id": 154, "index": 660, "featured": 0, "title": "Revenge X Storm", "title_tc": "", "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "revenge-x-storm", "description": "REVENGE X STORM was born in the United States in 2017. Since then, REVENGE X STORM has developed a lineup based on the good old school style that has not passed the times. REVENGE X STORM is worn by famous artists and has gained worldwide support. In addition, REVENGE X STORM has succeeded in collaborating with famous brands and famous artists, and is engaged in activities that are not bound by genre.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810edbe562faa68d6ee.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090197464df3fbf84eb5.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T14:02:34.000Z"}, {"id": 288, "index": 0, "featured": 0, "title": "RICHARD MILLE", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "richard-mille", "description": "Richard Mille is a Swiss luxury watch company founded in 2001 by <PERSON> and <PERSON>, based in Les Breuleux, Switzerland. The brand specialises in high-priced clockwork watches.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514f9c7c2d641688929.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090553b7d467ad41a88d.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:16:23.000Z", "updated_at": "2024-09-05T05:01:47.000Z"}, {"id": 158, "index": 670, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "rick-owens", "description": "<PERSON> – A luxury brand that stands for aesthetics, quality and simple fashion. The designer places particular emphasis on uncomplicated, yet exciting cuts, as well as neutral colors, such as black, gray and beige. In the realm of the glitzy fashion world, <PERSON> is considered the “Lord of Darkness.” Born in California, <PERSON> ‘ fashion is in complete contrast to the clichés of the US state: deep dark night and post-apocalyptic glamour instead of sunshine and happiness. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008102e104dd25555387e.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901a6ade4d93c7c4c3c.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T14:06:20.000Z"}, {"id": 25, "index": 680, "featured": 0, "title": "RIPNDIP", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "rip<PERSON>p", "description": "Started in 2009 by <PERSON> in Orlando, FL. Currently located in Los Angeles, CA. The RIPNDIP brand has organically grown through skateboarding and good times to what it is today: a company dedicated to creating ridiculous designs to keep all wild kids out there pumped up to go shred with their crew.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080548e902486a9dd27f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906af4d7bc888e713b9.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:27.000Z", "updated_at": "2024-09-06T04:26:35.000Z"}, {"id": 252, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "roberto-cavalli", "description": "<PERSON> was an Italian fashion designer and inventor. He was known for exotic prints and for creating the sand-blasted look for jeans. The <PERSON> fashion house sells luxury clothing, perfume and leather accessories.  In 1972, he opened his first boutique in Saint-Tropez. In 1975, he founded the house of <PERSON> fashion, incorporating \"femininity, spiritedness, and leopard print\" as its pillars. The brand acquired appeal for its stable and unvarying looks despite the changing trends.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709f24c84b1d6a57486.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240902dec9a8d2e815e4d4.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T10:11:06.000Z", "updated_at": "2024-09-02T12:38:32.000Z"}, {"id": 297, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "roger-<PERSON><PERSON>s", "description": "The story of <PERSON> is marked by incredible success. The company was founded in 1995. Striking a fine balance between meticulous watchmaking expertise and avant-garde design, its watches immediately made their mark and turned the company into a renowned specialist in architectural skeletonized movements.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240524a830777e69d648bd.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905d3b4a56a0be4af7d.png", "public": 1, "enabled": 1, "created_at": "2024-05-24T05:58:39.000Z", "updated_at": "2024-09-05T05:04:32.000Z"}, {"id": 195, "index": 690, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "roger-vivier", "description": "<PERSON> was a French fashion designer who specialized in shoes. He is best known for creating the modern day stiletto heel and for placing a chrome-plated buckle on an elegant black pump, which became a must-have fashion statement for many celebrities and stars in the 50s and 60s. His namesake label is <PERSON> (brand).", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810fb570f46d32593a4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240904bf9bc60321c92d66.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-04T12:51:55.000Z"}, {"id": 289, "index": 0, "featured": 0, "title": "ROLEX", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "rolex", "description": "Rolex was founded in 1905 by <PERSON> and <PERSON> in London, England and was recently ranked 57 on the worlds most powerful brands.  The original name of the company was Wilsdorf and Davis, and in 1919, operations were relocated to Geneva, Switzerland. The brand today produces industry icons such as the popular Rolex Submariner collection, Daytona, and GMT-Master to name a few. The brands catalog competes with other leading luxury watch companies like Patek Philippe, Omega, and Panerai and sponsors many major sports events in tennis, golf, yachting, and racing.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514a2c8fe249bb703b5.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090584dcc846f4ca8250.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:16:59.000Z", "updated_at": "2024-09-05T05:07:50.000Z"}, {"id": 304, "index": 0, "featured": 0, "title": "Sacai", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "sacai", "description": "Sacai was established by <PERSON><PERSON><PERSON> in 1999 in Tokyo, Japan.   Sacai incorporates the idea of hybridization through juxtapositions of contrasting textures of fabrics, such as knits with super fine wovens and the usage of techniques based on reinterpretations of patterns of the garment, transforming the pieces into unexpected shapes and silhouettes. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409193d39a95781875f5d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919a53b6806478c7467.png", "public": 1, "enabled": 1, "created_at": "2024-09-19T06:17:48.000Z", "updated_at": "2024-09-19T10:06:19.000Z"}, {"id": 138, "index": 700, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Founded in 1961, Yves <PERSON> is one of the most prominent fashion houses of the 20th century. Originally a House of Haute Couture, <PERSON> revolutionized the way fashion and society merge and interact in 1966 with the introduction of high-end made clothes produced on a larger scale than the exclusive collections.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805ad3a3a1b0f600fd8.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828569d5a3278138d52.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:29:53.000Z"}, {"id": 236, "index": 0, "featured": 0, "title": "Salomon", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "salomon", "description": "Salomon was founded in 1947 in the city of Annecy in the heart of the French Alps. <PERSON> launched the company by producing ski edges in a small workshop, with only his wife and son, <PERSON>, to help.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230213bb815de5b35b7fb4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090158dafdcae3ff5865.png", "public": 1, "enabled": 1, "created_at": "2023-02-13T11:09:09.000Z", "updated_at": "2024-09-01T14:10:36.000Z"}, {"id": 342, "index": 0, "featured": 0, "title": "SHISEIDO", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "s<PERSON><PERSON><PERSON>", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410242344264c405da3cc.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:11:20.000Z", "updated_at": "2024-10-24T03:46:42.000Z"}, {"id": 343, "index": 0, "featured": 0, "title": "shu u<PERSON>ura", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "shu-<PERSON><PERSON><PERSON>", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102476dc609cd055b109.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:11:40.000Z", "updated_at": "2024-10-24T03:47:35.000Z"}, {"id": 344, "index": 0, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "sisley", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024edf68f2be221b3aa.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:12:02.000Z", "updated_at": "2024-10-24T03:47:30.000Z"}, {"id": 345, "index": 0, "featured": 0, "title": "SK-II", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "sk-ii", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102495da9c629fdd13da.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:12:29.000Z", "updated_at": "2024-10-24T03:47:25.000Z"}, {"id": 260, "index": 890, "featured": 0, "title": "Skechers", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "skechers", "description": "Skechers is a relatively young company with its beginnings in 1992. Skechers is a worldwide designer, developer, marketer, and distributor of casual, active, and dress style footwear. The company’s products are designed to appeal to active, fashion-conscious consumers at affordable prices. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2023110693763a1e8b61e07f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901d726b7b2e7f7daaf.png", "public": 1, "enabled": 1, "created_at": "2023-11-06T10:05:34.000Z", "updated_at": "2024-09-01T14:14:08.000Z"}, {"id": 346, "index": 0, "featured": 0, "title": "SkinCeuticals", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "skinceuticals", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024102451b8975c1b9b4d4d.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:12:51.000Z", "updated_at": "2024-10-24T03:47:15.000Z"}, {"id": 300, "index": 0, "featured": 0, "title": "Sport Cards", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "sport-cards", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024091109416be45e505371.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-09-11T13:18:29.000Z", "updated_at": "2024-09-19T06:25:55.000Z"}, {"id": 167, "index": 710, "featured": 0, "title": "SSUR", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "ssur", "description": "SSUR was founded in 1988 by visionary artist <PERSON><PERSON><PERSON>, a creative force born in Soviet Era Odessa, Ukraine. Raised amidst the kaleidoscopic energy of Brooklyn's Coney Island, <PERSON><PERSON><PERSON> etched his mark on New York City's Downtown art scene, laying the cornerstone for what would become a legendary brand.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020081063fd2e417292fc10.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090643b9aa54d87451b9.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-06T04:28:12.000Z"}, {"id": 253, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "stella-mccartney", "description": "<PERSON> McCartney is a British luxury fashion house, founded in 2001 by its eponymous director and designer. The business now operates in over 17 freestanding stores in locations including Manhattan, London, LA, Paris and Barcelona.  A lifelong vegetarian, <PERSON> does not use any animal leather or fur and has always been in support of animal rights.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709799ed32c30c39a9f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202408287b557248b928fdc6.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T10:30:46.000Z", "updated_at": "2024-08-28T11:12:38.000Z"}, {"id": 204, "index": 0, "featured": 0, "title": "Stone Island", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "stone-island", "description": "A culture of material research, innovation and functionality are the values that have always defined Stone Island, an apparel brand founded in 1982 by <PERSON><PERSON> and <PERSON>, with a center of excellence in Ravarino – a small town in the province of Modena – and intended to become a symbol of extreme research on fibres and fabrics, applied to an innovative design.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210114196f4578ff4e90eb.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906c2bdd22c1985ae6f.png", "public": 1, "enabled": 1, "created_at": "2021-01-14T04:12:15.000Z", "updated_at": "2024-09-06T04:30:21.000Z"}, {"id": 258, "index": 0, "featured": 0, "title": "STUART WEITZMAN", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "s<PERSON><PERSON><PERSON>we<PERSON><PERSON>", "description": "Born in 1942, <PERSON> is the founder of high-end international shoe brand, <PERSON>. He garnered worldwide attention for the use of unconventional and unique materials like vinyl, cork, wallpaper, lucite and gold, as well as his objective to produce something flawless. His shoe designs are sold in more than seventy countries worldwide.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2023070997486430e7cfa6d5.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240904f84d046b6330d342.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T13:11:18.000Z", "updated_at": "2024-09-04T12:54:55.000Z"}, {"id": 24, "index": 720, "featured": 0, "title": "Stussy", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "stussy", "description": "Stüssy is an American privately held fashion house founded in the early 1980s by <PERSON>. It benefited from the surfwear trend originating in Orange County, California, but was later adopted by the skateboard and hip hop scenes.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805383f3f40c4da704f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906de00989925d891bd.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:19.000Z", "updated_at": "2024-09-06T04:32:02.000Z"}, {"id": 9, "index": 730, "featured": 0, "title": "Supreme", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "supreme", "description": "In April 1994, Supreme opened its doors on Lafayette Street in downtown Manhattan and became the home of New York City skate culture. At its core was a group of neighborhood kids, New York skaters, and local artists who became the store's staff, crew, and customers.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805536cf4d15e821f13.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906e58ced08142f3809.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-06T04:34:04.000Z"}, {"id": 347, "index": 0, "featured": 0, "title": "SUQQU", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "suqqu", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024a48bbdbe3b1afde2.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:13:15.000Z", "updated_at": "2024-10-24T03:47:20.000Z"}, {"id": 290, "index": 0, "featured": 0, "title": "T<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "tag-heuer", "description": "<PERSON> Heuer is a Swiss luxury watch brand that has been producing high-quality timepieces for over 150 years. Founded in 1860 by <PERSON><PERSON>, the company has a rich history and has become one of the most well-known and respected watch brands in the world.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514a527c1cb78fe95e1.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090570eea4769c3914fb.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:18:33.000Z", "updated_at": "2024-09-05T05:10:45.000Z"}, {"id": 27, "index": 740, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "ta<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> (村上 隆, <PERSON><PERSON><PERSON>, born February 1, 1962) is a Japanese contemporary artist. He works in fine arts media (such as painting and sculpture) as well as commercial media (such as fashion, merchandise, and animation) and is known for blurring the line between high and low arts. He coined the term \"superflat\", which describes both the aesthetic characteristics of the Japanese artistic tradition and the nature of post-war Japanese culture and society, and is also used for <PERSON><PERSON><PERSON>’s own artistic style and that of other Japanese artists he has influenced.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805c5558dd324147ae5.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090368ff4bc03211e6da.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-09-03T13:35:30.000Z"}, {"id": 145, "index": 750, "featured": 0, "title": "Telfar", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "telfar", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805f037457114a05b94.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2020-08-05T07:54:24.000Z"}, {"id": 146, "index": 760, "featured": 0, "title": "<PERSON> <PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "the-marc-jaco<PERSON>", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805bb8531785eb68ccf.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:47:10.000Z"}, {"id": 15, "index": 770, "featured": 0, "title": "THE NORTH FACE", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "the-north-face", "description": "Named for the most challenging side of the mountain, The North Face has equipped explorers since 1966 to reach their dreams. Driven by the mantra of Never Stop Exploring™, our expeditions inspire us to test the outer limits of performance and possibility.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805d1082f2e2949d0fb.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906ac2eb37c380577bc.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-06T04:35:47.000Z"}, {"id": 348, "index": 0, "featured": 0, "title": "THE WHOO", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "the-whoo", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20241024084e2323fdffa38b.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2024-10-24T00:13:43.000Z", "updated_at": "2024-10-24T03:47:05.000Z"}, {"id": 186, "index": 780, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "thom-browne", "description": "<PERSON> is an American fashion designer. He is the founder and head of design for Thom Browne, a luxury fashion brand based in New York City. <PERSON> debuted his womenswear collection in 2014.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810c49bc251fc4938bc.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240902be3709ea282d2404.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-02T12:47:26.000Z"}, {"id": 18, "index": 790, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "thrasher", "description": "Thrasher quickly became the most respected skate magazine in the world; skateboarding had found a mouthpiece and made its mark on the landscape. What began as a 32-page tabloid evolved into several respected brands - including Juxtapoz and SLAP magazines - as well as a series of books, ramp building guides, skate rock albums, Thrasher videos and DVDs, a hugely popular product and softgoods line, and the number one online authority on skateboarding.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805cb3cff403a03e993.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906435ec8e507e02eef.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-06T04:38:19.000Z"}, {"id": 187, "index": 800, "featured": 0, "title": "Tiffany & Co.", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "tiffany-co", "description": "Founded in 1837 by <PERSON> in New York City, Tiffany & Co. is one of the world’s most prestigious houses for jewelry and accessories. Love has been the driving force of Tiffany & Co. since its inception, uniting the jeweler’s core values of inventiveness, craft and joy in designs that endure across generations.  As a global pioneer in the art of fine jewelry, Tiffany has spent almost two centuries perfecting its craft and setting benchmarks within the industry. It is through this unwavering vow to excellence and expertise, to heritage and innovation, to optimism and possibility that Tiffany continues its legacy, creating designs that inspire people to express and celebrate the many facets of love.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810370c86a820a86111.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240903b11c8a7c21b71022.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-03T13:53:50.000Z"}, {"id": 216, "index": 0, "featured": 0, "title": "Timberland", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "timberland", "description": "In the vast landscape of fashion and apparel, very few brands can boast a unique combination of style, quality and commitment to sustainability like Timberland. It all began with a skilled shoemaker named <PERSON> in the year 1952, acquiring the factory where he worked for more than 3 decades called the Abington Shoe Company. The Timberland brand was founded in 1973 in a small town in New Market, New Hampshire, with <PERSON> and his brother, <PERSON><PERSON><PERSON><PERSON>’s sons. The company wanted to create a perfect, durable, fully waterproof boot that would last.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210122d528af6d7fdb4eae.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409016a7a0e3b62816d33.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:29:43.000Z", "updated_at": "2024-09-01T14:24:36.000Z"}, {"id": 291, "index": 0, "featured": 0, "title": "TISSOT", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "tissot", "description": "Tissot was founded way back in 1853 by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and his son <PERSON><PERSON><PERSON><PERSON> in the Swiss city of Le Locle – a place where the brand still remains to this day. Located in the Neuchâtel area of the Jura Mountains, Tissot initially built its reputation by building highly-reliable, gold-cased pocket watches that found a strong and appreciative audience all over the world.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024051412e9d8db4efb662d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409059c88e2f9543f73d5.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:19:02.000Z", "updated_at": "2024-09-05T06:08:01.000Z"}, {"id": 256, "index": 0, "featured": 0, "title": "TOD'S", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "tods", "description": "Tod's S.p.A. is an Italian luxury fashion house specialized in footwear, apparel, and related accessories headquartered in Marche, Italy. Its core branding includes an oval nameplate and roaring lion, with signature brown and orange packaging. The company is an influencer in the Sprezzatura (Italian \"casual chic\") fashion movement. Its highest-selling products are pebble-sole “Go<PERSON><PERSON> driving shoes, leather (suede) loafers, boots, sneakers, and handbags.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709f58663b62ac42179.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828cdfc1c26361a784c.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T12:47:33.000Z", "updated_at": "2024-08-28T11:16:17.000Z"}, {"id": 257, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "tom-ford", "description": "Tom Ford is a global design house offering exceptional luxury products across women’s and men’s fashion, accessories, eyewear and beauty. Founded in 2005 by <PERSON>, Today the brand has a presence in more than 100 markets globally, and is widely recognized as the architect of luxury glamour. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20230709162ceb295bb00025.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090223303dbb663ca073.png", "public": 1, "enabled": 1, "created_at": "2023-07-09T13:03:07.000Z", "updated_at": "2024-10-24T03:30:05.000Z"}, {"id": 212, "index": 0, "featured": 0, "title": "<PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "tory-burch", "description": "Founded in New York City in 2004, <PERSON> has redefined American luxury with a global point of view. The company’s purpose is to empower women and women entrepreneurs.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202101227f2dabc15c9db03e.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828400b9b90724dbc98.png", "public": 1, "enabled": 1, "created_at": "2021-01-22T08:27:31.000Z", "updated_at": "2024-08-28T11:21:11.000Z"}, {"id": 292, "index": 0, "featured": 0, "title": "TUDOR", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "tudor", "description": "In February 1926, <PERSON> had the trademark “The Tudor” registered and started making watches bearing that signature on the dial. Just after the Second World War, <PERSON> knew that the time had come to expand and give the brand a proper identity of its own. On 6 March 1946, he created “Montres TUDOR S.A.”, specialising in models for both men and women. Rolex would guarantee the technical, aesthetic and functional characteristics, along with the distribution and after-sales service.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024051405307bca6aad78a4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905b967f4942b8cbdca.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:19:23.000Z", "updated_at": "2024-09-05T06:13:59.000Z"}, {"id": 293, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "ulysse-nardin", "description": "Ulysse Nardin SA is a Swiss luxury watchmaking company founded in 1846 in Le Locle, Switzerland. The company became known for manufacturing highly accurate marine chronometers and complicated precision exclusive timepieces used by over 50 of the world's navies from the end of the 19th century till 1950.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024051440a52a739b6dc01f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905df812950d77b012a.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:19:57.000Z", "updated_at": "2024-09-05T06:15:48.000Z"}, {"id": 23, "index": 810, "featured": 0, "title": "Undefeated", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "undefeated", "description": "Founded in 2002 by <PERSON> and <PERSON>, LA-based streetwear and sneaker boutique UNDEFEATED have blazed a trail within both realms for close to 20 years.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080500f0cab7510b2b09.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090698894c650e99d581.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:04.000Z", "updated_at": "2024-09-06T04:40:21.000Z"}, {"id": 151, "index": 820, "featured": 0, "title": "Under Armour", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "under-armour", "description": "Founded in 1996 by former University of Maryland football player <PERSON>, Under Armour is driven by a passion for high performance clothing – engineered to keep athletes cool, dry and light throughout the course of a game, practice or workout. The technology behind Under Armour’s diverse product assortment for men, women and youth is complex, but the program for reaping the benefits is simple: wear HeatGear when it’s hot, ColdGear when it’s cold, and AllSeasonGear between the extremes.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008107be65aab9d3eddd3.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901c81f462cf9921d03.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T14:30:23.000Z"}, {"id": 294, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "v<PERSON><PERSON>-constantin", "description": "Founded in 1755, Vacheron Constantin is the oldest watchmaking manufacture in uninterrupted activity for more than 265 years. At no time in its history has it ever stopped creating, enhancing and reinventing itself. Backed by a strong heritage of passing watchmaking excellence down generations of master craftsmen, the company’s creations embody the exact standards of Fine Watchmaking. A technical signature and distinctive look.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514b61600bb225eff4d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409051d63008d0685be5f.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:20:35.000Z", "updated_at": "2024-09-05T06:19:39.000Z"}, {"id": 172, "index": 830, "featured": 0, "title": "Valentino", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "valentino", "description": "Maison Valentino was founded in 1960 by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> in Rome. A symbol of crafting excellence, creativity and uniqueness, Valentino continues to inspire individuality by redefining the values of mastery and emotional beauty that are deeply intertwined with its roots as the most established Italian Maison de Couture.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008107804b3eca87494a4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024082897d4967d4fa692c3.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-08-28T11:23:19.000Z"}, {"id": 189, "index": 840, "featured": 0, "title": "Van Cleef & Arpels", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "van-cleef-<PERSON><PERSON><PERSON>", "description": "Van Cleef & Arpels was born as a union between two lovers, <PERSON><PERSON><PERSON>, the daughter of a dealer in precious stones, and <PERSON>, the son of a stone cutter. The couple married in 1895 and were driven by their pioneering spirit and passion for precious stones to create the Van Cleef jewelry brand.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810f15d6dab5e994af3.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240903e68ad9d384b44ebc.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-10-24T03:30:17.000Z"}, {"id": 8, "index": 850, "featured": 0, "title": "<PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "vans", "description": "Started by <PERSON>, <PERSON>, and partners <PERSON> and <PERSON> in 1966, the Van Doren Rubber Company manufactured and sold their shoes directly to the public.  Whether it’s a Vans Authentic, Old Skool or Era, and whether you’re a skater, office worker or celebrity chances are you’ve owned a Vans shoe at least once in your life.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080593cc27b8969ef96f.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090134dc27bef53bb765.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:51:22.000Z", "updated_at": "2024-09-01T14:40:09.000Z"}, {"id": 147, "index": 860, "featured": 0, "title": "Versace", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "versace", "description": "<PERSON>ersace is one of the most recognizable names in the world. The luxury House immediately became a leader in fashion and culture following its founding in 1978 by <PERSON><PERSON><PERSON>, and it continues to lead with purpose under the creative vision of <PERSON><PERSON><PERSON> and leadership of <PERSON>.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008058a23006502cf8ee0.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240828c3f9768722655cea.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:36:34.000Z", "updated_at": "2024-10-24T03:30:29.000Z"}, {"id": 233, "index": 0, "featured": 0, "title": "Vetements", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "vetements", "description": "Vetements is a fashion brand that was founded in 2014 by Georgian fashion designer <PERSON><PERSON><PERSON>. Headquartered in Zürich, Switzerland, Vetements has gained significant attention in the fashion industry with its unique approach to design and unconventional inspirations. The brand is known for creating a wide range of menswear and womenswear that draws inspiration from everyday fashion and regular people. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20211004d39cb7d5f57101ab.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240906e84136d9bfc5ecfd.png", "public": 1, "enabled": 1, "created_at": "2021-10-04T05:59:05.000Z", "updated_at": "2024-09-06T04:44:14.000Z"}, {"id": 305, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "visvim", "description": "Visvim was founded in 2001 by <PERSON><PERSON><PERSON>. Born in Kofu, Japan, <PERSON> grew up in eighties Tokyo, spending most of his teenage years in the capital.  Fashion in post-war Japan was largely informed by casual American clothing and <PERSON> soon gained an understanding – and a fascination – of Americana style.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409198de11829b5dd2938.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919ea47277571f0b232.png", "public": 1, "enabled": 1, "created_at": "2024-09-19T06:18:10.000Z", "updated_at": "2024-09-19T10:08:29.000Z"}, {"id": 224, "index": 0, "featured": 0, "title": "Vlone", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "vlone", "description": null, "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210318e26e7cd214916013.png", "cover_image_url": null, "public": 1, "enabled": 1, "created_at": "2021-03-15T06:43:21.000Z", "updated_at": "2021-03-18T02:56:09.000Z"}, {"id": 306, "index": 0, "featured": 0, "title": "Wacko Maria", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "wacko-maria", "description": "Established in 2005 by <PERSON><PERSON><PERSON>, WACKO MARIA is a brand known for its streetwear pieces heavily infused with a romantic and glamorous lens. The label draws its inspirations from everyday life, music, and film brought together with an eye for detail and craftsmanship.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409196aa29765c9526847.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919324699939fdd761a.png", "public": 1, "enabled": 1, "created_at": "2024-09-19T06:18:34.000Z", "updated_at": "2024-09-19T10:09:55.000Z"}, {"id": 161, "index": 870, "featured": 0, "title": "We11Done", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "we-11-done", "description": "<PERSON> launched the brand with <PERSON> (who just so happens to be K-pop king <PERSON><PERSON><PERSON>'s sister) back in 2015, a year after opening Seoul-based Rare Market – a concept store that introduced many foreign brands to South Korea for the first time.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810d337fff80958a07d.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409069774df6e2a03bccd.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-06T04:42:21.000Z"}, {"id": 20, "index": 880, "featured": 0, "title": "Wtaps", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "wtaps", "description": "WTAPS has been at the forefront of Japanese streetwear for over twenty years. Since emerging from the brimming Harajuku streetwear scene of the 1990s, WTAPS  has pushed the boundaries of the streetwear clothing genre with charming apparel that compounds influences from historic military garb, outdoor clothing, and contemporary sub-cultures like punk and skateboarding.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805d453623d335f29fd.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409060ed76a1e78f8f51c.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T01:30:34.000Z", "updated_at": "2024-09-06T04:45:50.000Z"}, {"id": 191, "index": 890, "featured": 0, "title": "XVESSEL", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "xvessel", "description": "The narrative of xVESSEL began in 2017, marked by a fusion of deconstructionism with iconic design elements. It stands as a brand committed to delivering the forefront of fashion perspectives for enthusiasts.  The 'x' within the xVESSEL name conveys a daring and assured sense of exploration, while 'VESSEL' embodies the concept of a \"ship\" and also resonates with the notion of a \"blood vessel.\" This dual symbolism portrays the brand as a courageous vessel venturing into uncharted territories and as the life force coursing through individuals' experiences.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200810012c1a042e010f7a.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024090113fbcb7bd5d611c9.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T14:42:14.000Z"}, {"id": 156, "index": 900, "featured": 0, "title": "Y-3", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "y-3", "description": "Y-3 is a fashion brand founded in 2003 by designer <PERSON><PERSON><PERSON> and Adidas. The brand is known for its modern and contemporary designs, often incorporating technical fabrics and innovative details. Y-3 has gained a following among fashion enthusiasts and has been worn by celebrities and influencers. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008105cac6249f3176fab.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409016b40aa1707a84ab3.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T14:46:22.000Z"}, {"id": 4, "index": 910, "featured": 12, "title": "Yeezy", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "yeezy", "description": "Yeezy was a fashion collaboration between American rapper, designer, and entrepreneur <PERSON><PERSON><PERSON>'s[a] Yeezy and German sportswear company Adidas. It offered sneakers in limited edition colorways, as well as shirts, jackets, track pants, socks, slides, lingerie and slippers. The first shoe model (\"Boost 750\") was released in February 2015. ", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805fc69204ea1fee5e0.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240901fc66043922bca431.png", "public": 1, "enabled": 1, "created_at": "2020-08-05T00:47:47.000Z", "updated_at": "2024-09-01T14:58:17.000Z"}, {"id": 307, "index": 0, "featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> is a Japanese fashion designer. After graduating from Bunkafukuso Gakuin School of Fashion in 1969, he went on to present his first womenswear collections in Tokyo, Paris and New York in 1977, and his first menswear collection in Paris in 1984.", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240919f94f857ca8f60717.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024091999930533fe30d1a2.png", "public": 1, "enabled": 1, "created_at": "2024-09-19T06:19:12.000Z", "updated_at": "2024-09-19T10:11:48.000Z"}, {"id": 295, "index": 0, "featured": 0, "title": "ZENITH", "title_tc": null, "title_sc": null, "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "slug": "zenith", "description": "Zenith was founded in 1865 by <PERSON> in Le Locle, in the canton of Neuchâtel, Switzerland. He brought all the major watchmaking professions under one roof (movement, dial, case makers) and is considered one of the first to develop the concept of a Manufacture. Among the most famous owners of a Zenith piece is <PERSON><PERSON><PERSON>, who carried a pocket watch with an alarm complication (which sold at auction in 2009 for approximately USD 2 million).", "description_tc": null, "description_sc": null, "description_ja": null, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2024051431bdfad5be927282.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240905c9b55bf20d6ffbff.png", "public": 1, "enabled": 1, "created_at": "2024-05-14T03:20:56.000Z", "updated_at": "2024-09-05T06:22:31.000Z"}], "product_category": [{"id": 1, "index": 2, "featured": 7, "marketplace_featured": 0, "title": "Sneakers", "title_tc": "球鞋", "title_sc": "球鞋", "title_es": null, "title_ja": "スニーカー", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 248, "case_count": 1012516, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2022121855af36a336d46fa9.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020101683c75c145db5e719.png", "public": 1, "enabled": 1, "last_ad_created_at": "2022-09-15T17:30:53.000Z", "last_case_created_at": "2025-02-13T11:47:43.000Z", "created_at": "2020-08-05T00:35:54.000Z", "updated_at": "2025-02-13T11:47:43.000Z"}, {"id": 2, "index": 10, "featured": 6, "marketplace_featured": 0, "title": "Streetwear", "title_tc": "潮流服飾", "title_sc": "潮流服饰", "title_es": null, "title_ja": "ストリートファッション", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 9, "case_count": 51608, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080594643c63fba91706.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20201016df3dbadb4f7b6f99.png", "public": 1, "enabled": 1, "last_ad_created_at": "2022-09-06T03:06:26.000Z", "last_case_created_at": "2025-02-13T11:28:26.000Z", "created_at": "2020-08-05T00:35:54.000Z", "updated_at": "2025-02-13T11:28:26.000Z"}, {"id": 3, "index": 11, "featured": 5, "marketplace_featured": 0, "title": "Toys & Figures", "title_tc": "玩具和模型", "title_sc": "玩具和模型", "title_es": null, "title_ja": "おもちゃと模型", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 1, "case_count": 1704, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020080515b4e0c3322c7f55.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020101690c4c93d5690d50f.png", "public": 1, "enabled": 1, "last_ad_created_at": "2021-08-12T17:15:13.000Z", "last_case_created_at": "2025-02-13T11:48:07.000Z", "created_at": "2020-08-05T00:35:54.000Z", "updated_at": "2025-02-13T11:48:07.000Z"}, {"id": 4, "index": 1, "featured": 4, "marketplace_featured": 0, "title": "Luxury Handbags", "title_tc": "奢侈品箱包", "title_sc": "奢侈品箱包", "title_es": null, "title_ja": "ブランドバック", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 18, "case_count": 68133, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20200805685a2f715813985c.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20201016041c0b4ff21df9b9.png", "public": 1, "enabled": 1, "last_ad_created_at": "2022-09-22T02:54:18.000Z", "last_case_created_at": "2025-02-13T11:49:44.000Z", "created_at": "2020-08-05T00:35:54.000Z", "updated_at": "2025-02-13T11:49:44.000Z"}, {"id": 5, "index": 4, "featured": 3, "marketplace_featured": 0, "title": "<PERSON><PERSON><PERSON>", "title_tc": "奢侈品服裝", "title_sc": "奢侈品服装", "title_es": null, "title_ja": "アパレル", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 6, "case_count": 21608, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202212180b1d0a6a230b0750.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/2020101634ac63be6bf5f719.png", "public": 1, "enabled": 1, "last_ad_created_at": "2022-09-06T03:03:19.000Z", "last_case_created_at": "2025-02-13T10:52:53.000Z", "created_at": "2020-08-05T00:35:54.000Z", "updated_at": "2025-02-13T10:52:53.000Z"}, {"id": 6, "index": 4, "featured": 2, "marketplace_featured": 0, "title": "Luxury Shoes", "title_tc": "奢侈品鞋類", "title_sc": "奢侈品鞋类", "title_es": null, "title_ja": "ブランドシューズ", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 5, "case_count": 35128, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202008103c5055498041e1cd.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202010162e431209fd4a813c.png", "public": 1, "enabled": 1, "last_ad_created_at": "2022-07-21T05:41:50.000Z", "last_case_created_at": "2025-02-13T11:17:48.000Z", "created_at": "2020-08-05T00:35:54.000Z", "updated_at": "2025-02-13T11:17:48.000Z"}, {"id": 7, "index": 5, "featured": 1, "marketplace_featured": 0, "title": "Luxury Accessories", "title_tc": "奢侈品飾品", "title_sc": "奢侈品饰品", "title_es": null, "title_ja": "ブランドアクセサリー", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 1, "case_count": 20090, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210819025ee1a26d5ad170.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20210819897c7e7d86f89400.png", "public": 1, "enabled": 1, "last_ad_created_at": "2021-09-14T08:15:46.000Z", "last_case_created_at": "2025-02-13T11:49:22.000Z", "created_at": "2021-08-19T04:58:27.000Z", "updated_at": "2025-02-13T11:49:22.000Z"}, {"id": 8, "index": 16, "featured": 8, "marketplace_featured": 0, "title": "Product Code Checking", "title_tc": "產品查碼服務", "title_sc": "产品查码服务", "title_es": null, "title_ja": "シリアルナンバー確認", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 0, "case_count": 460, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202302031e309b2c38894c19.png", "cover_image_url": null, "public": 1, "enabled": 1, "last_ad_created_at": null, "last_case_created_at": "2025-02-13T11:10:25.000Z", "created_at": "2023-02-03T04:07:50.000Z", "updated_at": "2025-02-13T11:10:25.000Z"}, {"id": 9, "index": 9, "featured": 0, "marketplace_featured": 0, "title": "<PERSON><PERSON><PERSON> Watches", "title_tc": "名貴腕錶", "title_sc": "名贵腕表", "title_es": null, "title_ja": "高級腕時計", "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 0, "case_count": 707, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240514493bc18945ad71c2.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240516c82319fc59d1bc08.png", "public": 1, "enabled": 1, "last_ad_created_at": null, "last_case_created_at": "2025-02-13T09:17:00.000Z", "created_at": "2024-05-14T02:15:52.000Z", "updated_at": "2025-02-13T09:17:00.000Z"}, {"id": 10, "index": 12, "featured": 12, "marketplace_featured": 0, "title": "Trading Cards", "title_tc": "集換式卡牌", "title_sc": "集换式卡牌", "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 0, "case_count": 36, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/20240911cf315a9ad3a7bca4.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202409120ba8e4e68c2a35f2.png", "public": 1, "enabled": 1, "last_ad_created_at": null, "last_case_created_at": "2025-02-08T16:44:58.000Z", "created_at": "2024-09-11T10:46:58.000Z", "updated_at": "2025-02-08T16:44:58.000Z"}, {"id": 11, "index": 13, "featured": 0, "marketplace_featured": 0, "title": "Cosmetic Products", "title_tc": "美妝產品", "title_sc": "美妆产品", "title_es": null, "title_ja": null, "title_ko": null, "title_vi": null, "title_th": null, "ad_count": 0, "case_count": 65, "icon_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410283ca0573ed42207f8.png", "cover_image_url": "https://legitapp-prod.oss-cn-hongkong.aliyuncs.com/202410289ab2cf07be544e8f.png", "public": 1, "enabled": 1, "last_ad_created_at": null, "last_case_created_at": "2025-02-12T10:45:52.000Z", "created_at": "2024-10-23T23:48:49.000Z", "updated_at": "2025-02-12T10:45:52.000Z"}], "product_category_brand_map": [{"id": 17, "index": 100, "featured": 1, "category_id": 1, "brand_id": 1, "enabled": 1, "created_at": "2020-08-05T00:52:35.000Z", "updated_at": "2020-08-05T00:52:35.000Z"}, {"id": 188, "index": 0, "featured": 0, "category_id": 2, "brand_id": 1, "enabled": 1, "created_at": "2021-03-25T03:36:31.000Z", "updated_at": "2021-03-25T03:36:31.000Z"}, {"id": 18, "index": 99, "featured": 0, "category_id": 1, "brand_id": 2, "enabled": 1, "created_at": "2020-08-05T00:52:42.000Z", "updated_at": "2020-08-05T00:52:42.000Z"}, {"id": 142, "index": 0, "featured": 0, "category_id": 2, "brand_id": 2, "enabled": 1, "created_at": "2020-12-23T05:41:10.000Z", "updated_at": "2020-12-23T05:41:10.000Z"}, {"id": 16, "index": 97, "featured": 0, "category_id": 1, "brand_id": 3, "enabled": 1, "created_at": "2020-08-05T00:52:27.000Z", "updated_at": "2020-08-05T00:52:27.000Z"}, {"id": 186, "index": 0, "featured": 0, "category_id": 2, "brand_id": 3, "enabled": 1, "created_at": "2021-03-24T10:08:16.000Z", "updated_at": "2021-03-24T10:08:16.000Z"}, {"id": 15, "index": 98, "featured": 0, "category_id": 1, "brand_id": 4, "enabled": 1, "created_at": "2020-08-05T00:52:21.000Z", "updated_at": "2020-08-05T00:52:21.000Z"}, {"id": 14, "index": 95, "featured": 0, "category_id": 1, "brand_id": 5, "enabled": 1, "created_at": "2020-08-05T00:52:15.000Z", "updated_at": "2020-08-05T00:52:15.000Z"}, {"id": 13, "index": 0, "featured": 0, "category_id": 1, "brand_id": 6, "enabled": 1, "created_at": "2020-08-05T00:52:07.000Z", "updated_at": "2020-08-05T00:52:07.000Z"}, {"id": 185, "index": 0, "featured": 0, "category_id": 2, "brand_id": 6, "enabled": 1, "created_at": "2021-03-24T10:04:22.000Z", "updated_at": "2021-03-24T10:04:22.000Z"}, {"id": 12, "index": 0, "featured": 0, "category_id": 1, "brand_id": 7, "enabled": 1, "created_at": "2020-08-05T00:52:00.000Z", "updated_at": "2020-08-05T00:52:00.000Z"}, {"id": 187, "index": 0, "featured": 0, "category_id": 2, "brand_id": 7, "enabled": 1, "created_at": "2021-03-24T10:11:31.000Z", "updated_at": "2021-03-24T10:11:31.000Z"}, {"id": 141, "index": 94, "featured": 0, "category_id": 1, "brand_id": 8, "enabled": 1, "created_at": "2020-12-11T08:12:29.000Z", "updated_at": "2020-12-11T08:12:29.000Z"}, {"id": 220, "index": 0, "featured": 0, "category_id": 2, "brand_id": 8, "enabled": 1, "created_at": "2022-08-26T07:23:12.000Z", "updated_at": "2022-08-26T07:23:12.000Z"}, {"id": 19, "index": 100, "featured": 0, "category_id": 2, "brand_id": 9, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 20, "index": 99, "featured": 0, "category_id": 2, "brand_id": 10, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 48, "index": 0, "featured": 0, "category_id": 1, "brand_id": 10, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 22, "index": 98, "featured": 0, "category_id": 2, "brand_id": 12, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 23, "index": 97, "featured": 0, "category_id": 2, "brand_id": 13, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 47, "index": 0, "featured": 0, "category_id": 1, "brand_id": 13, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 24, "index": 96, "featured": 0, "category_id": 2, "brand_id": 14, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 25, "index": 0, "featured": 0, "category_id": 2, "brand_id": 15, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 26, "index": 0, "featured": 0, "category_id": 2, "brand_id": 16, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 27, "index": 0, "featured": 0, "category_id": 2, "brand_id": 17, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 28, "index": 0, "featured": 0, "category_id": 2, "brand_id": 18, "enabled": 1, "created_at": "2020-08-05T01:38:15.000Z", "updated_at": "2020-08-05T01:38:15.000Z"}, {"id": 59, "index": 0, "featured": 0, "category_id": 2, "brand_id": 19, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 60, "index": 0, "featured": 0, "category_id": 2, "brand_id": 20, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 61, "index": 0, "featured": 0, "category_id": 2, "brand_id": 21, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 62, "index": 95, "featured": 0, "category_id": 2, "brand_id": 22, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 63, "index": 0, "featured": 0, "category_id": 2, "brand_id": 23, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 64, "index": 0, "featured": 0, "category_id": 2, "brand_id": 24, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 155, "index": 0, "featured": 0, "category_id": 2, "brand_id": 25, "enabled": 1, "created_at": "2021-01-21T10:49:09.000Z", "updated_at": "2021-01-21T10:49:09.000Z"}, {"id": 138, "index": 0, "featured": 0, "category_id": 2, "brand_id": 26, "enabled": 1, "created_at": "2020-12-07T06:55:22.000Z", "updated_at": "2020-12-07T06:55:22.000Z"}, {"id": 29, "index": 0, "featured": 0, "category_id": 3, "brand_id": 27, "enabled": 1, "created_at": "2020-08-05T01:39:40.000Z", "updated_at": "2020-08-05T01:39:40.000Z"}, {"id": 30, "index": 0, "featured": 0, "category_id": 3, "brand_id": 28, "enabled": 1, "created_at": "2020-08-05T01:39:40.000Z", "updated_at": "2020-08-05T01:39:40.000Z"}, {"id": 31, "index": 100, "featured": 0, "category_id": 4, "brand_id": 132, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 88, "index": 0, "featured": 0, "category_id": 5, "brand_id": 132, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 119, "index": 0, "featured": 0, "category_id": 6, "brand_id": 132, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 190, "index": 0, "featured": 0, "category_id": 7, "brand_id": 132, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 325, "index": 0, "featured": 0, "category_id": 9, "brand_id": 132, "enabled": 1, "created_at": "2024-05-24T11:59:44.000Z", "updated_at": "2024-05-24T11:59:44.000Z"}, {"id": 32, "index": 99, "featured": 0, "category_id": 4, "brand_id": 133, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 89, "index": 0, "featured": 0, "category_id": 5, "brand_id": 133, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 117, "index": 0, "featured": 0, "category_id": 6, "brand_id": 133, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 191, "index": 0, "featured": 0, "category_id": 7, "brand_id": 133, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 326, "index": 0, "featured": 0, "category_id": 9, "brand_id": 133, "enabled": 1, "created_at": "2024-05-24T11:59:55.000Z", "updated_at": "2024-05-24T11:59:55.000Z"}, {"id": 392, "index": 0, "featured": 0, "category_id": 11, "brand_id": 133, "enabled": 1, "created_at": "2024-10-24T03:24:29.000Z", "updated_at": "2024-10-24T03:24:29.000Z"}, {"id": 33, "index": 0, "featured": 0, "category_id": 4, "brand_id": 134, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 398, "index": 0, "featured": 0, "category_id": 11, "brand_id": 134, "enabled": 1, "created_at": "2024-10-24T03:26:31.000Z", "updated_at": "2024-10-24T03:26:31.000Z"}, {"id": 34, "index": 97, "featured": 0, "category_id": 4, "brand_id": 135, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 91, "index": 0, "featured": 0, "category_id": 5, "brand_id": 135, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 192, "index": 0, "featured": 0, "category_id": 7, "brand_id": 135, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 228, "index": 0, "featured": 0, "category_id": 8, "brand_id": 135, "enabled": 1, "created_at": "2023-02-03T04:07:50.000Z", "updated_at": "2023-02-03T04:07:50.000Z"}, {"id": 235, "index": 0, "featured": 0, "category_id": 6, "brand_id": 135, "enabled": 1, "created_at": "2023-05-29T07:35:00.000Z", "updated_at": "2023-05-29T07:35:00.000Z"}, {"id": 322, "index": 0, "featured": 0, "category_id": 9, "brand_id": 135, "enabled": 1, "created_at": "2024-05-24T11:59:12.000Z", "updated_at": "2024-05-24T11:59:12.000Z"}, {"id": 388, "index": 0, "featured": 0, "category_id": 11, "brand_id": 135, "enabled": 1, "created_at": "2024-10-24T03:21:57.000Z", "updated_at": "2024-10-24T03:21:57.000Z"}, {"id": 35, "index": 96, "featured": 0, "category_id": 4, "brand_id": 136, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 36, "index": 95, "featured": 0, "category_id": 4, "brand_id": 137, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 92, "index": 0, "featured": 0, "category_id": 5, "brand_id": 137, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 121, "index": 0, "featured": 0, "category_id": 6, "brand_id": 137, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 193, "index": 0, "featured": 0, "category_id": 7, "brand_id": 137, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 327, "index": 0, "featured": 0, "category_id": 9, "brand_id": 137, "enabled": 1, "created_at": "2024-05-24T12:00:15.000Z", "updated_at": "2024-05-24T12:00:15.000Z"}, {"id": 384, "index": 98, "featured": 0, "category_id": 11, "brand_id": 137, "enabled": 1, "created_at": "2024-10-24T03:19:31.000Z", "updated_at": "2024-10-24T03:19:31.000Z"}, {"id": 37, "index": 0, "featured": 0, "category_id": 4, "brand_id": 138, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 93, "index": 0, "featured": 0, "category_id": 5, "brand_id": 138, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 122, "index": 0, "featured": 0, "category_id": 6, "brand_id": 138, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 194, "index": 0, "featured": 0, "category_id": 7, "brand_id": 138, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 330, "index": 0, "featured": 0, "category_id": 9, "brand_id": 138, "enabled": 1, "created_at": "2024-05-24T12:01:36.000Z", "updated_at": "2024-05-24T12:01:36.000Z"}, {"id": 402, "index": 99, "featured": 0, "category_id": 11, "brand_id": 138, "enabled": 1, "created_at": "2024-10-24T03:29:53.000Z", "updated_at": "2024-10-24T03:29:53.000Z"}, {"id": 38, "index": 0, "featured": 0, "category_id": 4, "brand_id": 139, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 94, "index": 0, "featured": 0, "category_id": 5, "brand_id": 139, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 115, "index": 100, "featured": 0, "category_id": 6, "brand_id": 139, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 195, "index": 0, "featured": 0, "category_id": 7, "brand_id": 139, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 39, "index": 0, "featured": 0, "category_id": 4, "brand_id": 140, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 95, "index": 0, "featured": 0, "category_id": 5, "brand_id": 140, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 120, "index": 0, "featured": 0, "category_id": 6, "brand_id": 140, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 225, "index": 0, "featured": 0, "category_id": 7, "brand_id": 140, "enabled": 1, "created_at": "2022-08-31T03:21:28.000Z", "updated_at": "2022-08-31T03:21:28.000Z"}, {"id": 386, "index": 0, "featured": 0, "category_id": 11, "brand_id": 140, "enabled": 1, "created_at": "2024-10-24T03:21:07.000Z", "updated_at": "2024-10-24T03:21:07.000Z"}, {"id": 135, "index": 98, "featured": 0, "category_id": 4, "brand_id": 141, "enabled": 1, "created_at": "2020-11-30T09:52:05.000Z", "updated_at": "2020-11-30T09:52:05.000Z"}, {"id": 136, "index": 0, "featured": 0, "category_id": 5, "brand_id": 141, "enabled": 1, "created_at": "2020-11-30T09:52:05.000Z", "updated_at": "2020-11-30T09:52:05.000Z"}, {"id": 137, "index": 0, "featured": 0, "category_id": 6, "brand_id": 141, "enabled": 1, "created_at": "2020-11-30T09:52:05.000Z", "updated_at": "2020-11-30T09:52:05.000Z"}, {"id": 196, "index": 0, "featured": 0, "category_id": 7, "brand_id": 141, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 323, "index": 0, "featured": 0, "category_id": 9, "brand_id": 141, "enabled": 1, "created_at": "2024-05-24T11:59:22.000Z", "updated_at": "2024-05-24T11:59:22.000Z"}, {"id": 393, "index": 0, "featured": 0, "category_id": 11, "brand_id": 141, "enabled": 1, "created_at": "2024-10-24T03:24:45.000Z", "updated_at": "2024-10-24T03:24:45.000Z"}, {"id": 41, "index": 0, "featured": 0, "category_id": 4, "brand_id": 142, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 96, "index": 0, "featured": 0, "category_id": 5, "brand_id": 142, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 197, "index": 0, "featured": 0, "category_id": 7, "brand_id": 142, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 278, "index": 0, "featured": 0, "category_id": 6, "brand_id": 142, "enabled": 1, "created_at": "2023-07-09T13:49:37.000Z", "updated_at": "2023-07-09T13:49:37.000Z"}, {"id": 328, "index": 0, "featured": 0, "category_id": 9, "brand_id": 142, "enabled": 1, "created_at": "2024-05-24T12:00:24.000Z", "updated_at": "2024-05-24T12:00:24.000Z"}, {"id": 97, "index": 0, "featured": 0, "category_id": 5, "brand_id": 143, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 130, "index": 0, "featured": 0, "category_id": 6, "brand_id": 143, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 140, "index": 0, "featured": 0, "category_id": 4, "brand_id": 143, "enabled": 1, "created_at": "2020-12-10T18:42:59.000Z", "updated_at": "2020-12-10T18:42:59.000Z"}, {"id": 198, "index": 0, "featured": 0, "category_id": 7, "brand_id": 143, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 43, "index": 0, "featured": 0, "category_id": 4, "brand_id": 144, "enabled": 1, "created_at": "2020-08-05T01:43:29.000Z", "updated_at": "2020-08-05T01:43:29.000Z"}, {"id": 98, "index": 0, "featured": 0, "category_id": 5, "brand_id": 144, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 131, "index": 0, "featured": 0, "category_id": 6, "brand_id": 144, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 199, "index": 0, "featured": 0, "category_id": 7, "brand_id": 144, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 401, "index": 0, "featured": 0, "category_id": 11, "brand_id": 144, "enabled": 1, "created_at": "2024-10-24T03:29:42.000Z", "updated_at": "2024-10-24T03:29:42.000Z"}, {"id": 397, "index": 0, "featured": 0, "category_id": 11, "brand_id": 146, "enabled": 1, "created_at": "2024-10-24T03:26:05.000Z", "updated_at": "2024-10-24T03:26:05.000Z"}, {"id": 146, "index": 0, "featured": 0, "category_id": 4, "brand_id": 147, "enabled": 1, "created_at": "2021-01-09T11:31:56.000Z", "updated_at": "2021-01-09T11:31:56.000Z"}, {"id": 257, "index": 0, "featured": 0, "category_id": 6, "brand_id": 147, "enabled": 1, "created_at": "2023-07-09T10:22:30.000Z", "updated_at": "2023-07-09T10:22:30.000Z"}, {"id": 258, "index": 0, "featured": 0, "category_id": 5, "brand_id": 147, "enabled": 1, "created_at": "2023-07-09T10:22:30.000Z", "updated_at": "2023-07-09T10:22:30.000Z"}, {"id": 405, "index": 0, "featured": 0, "category_id": 11, "brand_id": 147, "enabled": 1, "created_at": "2024-10-24T03:30:29.000Z", "updated_at": "2024-10-24T03:30:29.000Z"}, {"id": 116, "index": 100, "featured": 0, "category_id": 6, "brand_id": 148, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 200, "index": 0, "featured": 0, "category_id": 7, "brand_id": 148, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 49, "index": 96, "featured": 0, "category_id": 1, "brand_id": 150, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 184, "index": 0, "featured": 0, "category_id": 2, "brand_id": 150, "enabled": 1, "created_at": "2021-03-24T10:01:09.000Z", "updated_at": "2021-03-24T10:01:09.000Z"}, {"id": 50, "index": 0, "featured": 0, "category_id": 1, "brand_id": 151, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 53, "index": 0, "featured": 0, "category_id": 1, "brand_id": 152, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 51, "index": 0, "featured": 0, "category_id": 1, "brand_id": 153, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 280, "index": 0, "featured": 0, "category_id": 2, "brand_id": 153, "enabled": 1, "created_at": "2023-11-06T12:06:49.000Z", "updated_at": "2023-11-06T12:06:49.000Z"}, {"id": 52, "index": 0, "featured": 0, "category_id": 1, "brand_id": 154, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 54, "index": 0, "featured": 0, "category_id": 1, "brand_id": 155, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 55, "index": 0, "featured": 0, "category_id": 1, "brand_id": 156, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 170, "index": 0, "featured": 0, "category_id": 1, "brand_id": 157, "enabled": 1, "created_at": "2021-02-01T02:33:47.000Z", "updated_at": "2021-02-01T02:33:47.000Z"}, {"id": 56, "index": 0, "featured": 0, "category_id": 1, "brand_id": 158, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 65, "index": 0, "featured": 0, "category_id": 2, "brand_id": 158, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 57, "index": 0, "featured": 0, "category_id": 1, "brand_id": 159, "enabled": 1, "created_at": "2020-08-10T09:26:45.000Z", "updated_at": "2020-08-10T09:26:45.000Z"}, {"id": 67, "index": 0, "featured": 0, "category_id": 2, "brand_id": 160, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 66, "index": 0, "featured": 0, "category_id": 2, "brand_id": 161, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 68, "index": 0, "featured": 0, "category_id": 2, "brand_id": 162, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 69, "index": 0, "featured": 0, "category_id": 2, "brand_id": 163, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 70, "index": 0, "featured": 0, "category_id": 2, "brand_id": 164, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 72, "index": 0, "featured": 0, "category_id": 2, "brand_id": 166, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 73, "index": 0, "featured": 0, "category_id": 2, "brand_id": 167, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 74, "index": 0, "featured": 0, "category_id": 2, "brand_id": 168, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 75, "index": 0, "featured": 0, "category_id": 2, "brand_id": 169, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 76, "index": 0, "featured": 0, "category_id": 2, "brand_id": 170, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 77, "index": 0, "featured": 0, "category_id": 2, "brand_id": 171, "enabled": 1, "created_at": "2020-08-10T09:30:56.000Z", "updated_at": "2020-08-10T09:30:56.000Z"}, {"id": 78, "index": 0, "featured": 0, "category_id": 4, "brand_id": 172, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 99, "index": 0, "featured": 0, "category_id": 5, "brand_id": 172, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 123, "index": 100, "featured": 0, "category_id": 6, "brand_id": 172, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 201, "index": 0, "featured": 0, "category_id": 7, "brand_id": 172, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 79, "index": 0, "featured": 0, "category_id": 4, "brand_id": 173, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 100, "index": 0, "featured": 0, "category_id": 5, "brand_id": 173, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 124, "index": 0, "featured": 0, "category_id": 6, "brand_id": 173, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 399, "index": 0, "featured": 0, "category_id": 11, "brand_id": 173, "enabled": 1, "created_at": "2024-10-24T03:26:51.000Z", "updated_at": "2024-10-24T03:26:51.000Z"}, {"id": 80, "index": 0, "featured": 0, "category_id": 4, "brand_id": 174, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 101, "index": 0, "featured": 0, "category_id": 5, "brand_id": 174, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 125, "index": 0, "featured": 0, "category_id": 6, "brand_id": 174, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 202, "index": 0, "featured": 0, "category_id": 7, "brand_id": 174, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 395, "index": 0, "featured": 0, "category_id": 11, "brand_id": 174, "enabled": 1, "created_at": "2024-10-24T03:25:08.000Z", "updated_at": "2024-10-24T03:25:08.000Z"}, {"id": 81, "index": 0, "featured": 0, "category_id": 4, "brand_id": 175, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 214, "index": 0, "featured": 0, "category_id": 5, "brand_id": 175, "enabled": 1, "created_at": "2021-10-04T04:01:47.000Z", "updated_at": "2021-10-04T04:01:47.000Z"}, {"id": 215, "index": 0, "featured": 0, "category_id": 6, "brand_id": 175, "enabled": 1, "created_at": "2021-10-04T04:14:50.000Z", "updated_at": "2021-10-04T04:14:50.000Z"}, {"id": 224, "index": 0, "featured": 0, "category_id": 7, "brand_id": 175, "enabled": 1, "created_at": "2022-08-31T02:35:31.000Z", "updated_at": "2022-08-31T02:35:31.000Z"}, {"id": 385, "index": 0, "featured": 0, "category_id": 11, "brand_id": 175, "enabled": 1, "created_at": "2024-10-24T03:20:44.000Z", "updated_at": "2024-10-24T03:20:44.000Z"}, {"id": 82, "index": 0, "featured": 0, "category_id": 4, "brand_id": 176, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 203, "index": 0, "featured": 0, "category_id": 7, "brand_id": 176, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 389, "index": 0, "featured": 0, "category_id": 11, "brand_id": 176, "enabled": 1, "created_at": "2024-10-24T03:22:22.000Z", "updated_at": "2024-10-24T03:22:22.000Z"}, {"id": 83, "index": 0, "featured": 0, "category_id": 4, "brand_id": 177, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 102, "index": 0, "featured": 0, "category_id": 5, "brand_id": 177, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 204, "index": 0, "featured": 0, "category_id": 7, "brand_id": 177, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 84, "index": 0, "featured": 0, "category_id": 4, "brand_id": 178, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 103, "index": 0, "featured": 0, "category_id": 5, "brand_id": 178, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 205, "index": 0, "featured": 0, "category_id": 7, "brand_id": 178, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 232, "index": 0, "featured": 0, "category_id": 8, "brand_id": 178, "enabled": 1, "created_at": "2023-02-03T04:07:50.000Z", "updated_at": "2023-02-03T04:07:50.000Z"}, {"id": 324, "index": 0, "featured": 0, "category_id": 9, "brand_id": 178, "enabled": 1, "created_at": "2024-05-24T11:59:35.000Z", "updated_at": "2024-05-24T11:59:35.000Z"}, {"id": 387, "index": 0, "featured": 0, "category_id": 11, "brand_id": 178, "enabled": 1, "created_at": "2024-10-24T03:21:35.000Z", "updated_at": "2024-10-24T03:21:35.000Z"}, {"id": 85, "index": 0, "featured": 0, "category_id": 4, "brand_id": 179, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 104, "index": 0, "featured": 0, "category_id": 5, "brand_id": 179, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 391, "index": 96, "featured": 0, "category_id": 11, "brand_id": 179, "enabled": 1, "created_at": "2024-10-24T03:24:08.000Z", "updated_at": "2024-10-24T03:24:08.000Z"}, {"id": 86, "index": 0, "featured": 0, "category_id": 4, "brand_id": 180, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 87, "index": 0, "featured": 0, "category_id": 4, "brand_id": 181, "enabled": 1, "created_at": "2020-08-10T09:33:31.000Z", "updated_at": "2020-08-10T09:33:31.000Z"}, {"id": 275, "index": 0, "featured": 0, "category_id": 6, "brand_id": 181, "enabled": 1, "created_at": "2023-07-09T13:27:19.000Z", "updated_at": "2023-07-09T13:27:19.000Z"}, {"id": 276, "index": 0, "featured": 0, "category_id": 5, "brand_id": 181, "enabled": 1, "created_at": "2023-07-09T13:27:19.000Z", "updated_at": "2023-07-09T13:27:19.000Z"}, {"id": 106, "index": 0, "featured": 0, "category_id": 5, "brand_id": 182, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 107, "index": 0, "featured": 0, "category_id": 5, "brand_id": 183, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 108, "index": 0, "featured": 0, "category_id": 5, "brand_id": 184, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 109, "index": 0, "featured": 0, "category_id": 5, "brand_id": 185, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 110, "index": 0, "featured": 0, "category_id": 5, "brand_id": 186, "enabled": 1, "created_at": "2020-08-10T09:37:43.000Z", "updated_at": "2020-08-10T09:37:43.000Z"}, {"id": 206, "index": 0, "featured": 0, "category_id": 7, "brand_id": 187, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 329, "index": 0, "featured": 0, "category_id": 9, "brand_id": 187, "enabled": 1, "created_at": "2024-05-24T12:00:35.000Z", "updated_at": "2024-05-24T12:00:35.000Z"}, {"id": 207, "index": 0, "featured": 0, "category_id": 7, "brand_id": 188, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 223, "index": 0, "featured": 0, "category_id": 5, "brand_id": 188, "enabled": 1, "created_at": "2022-08-29T06:32:24.000Z", "updated_at": "2022-08-29T06:32:24.000Z"}, {"id": 231, "index": 0, "featured": 0, "category_id": 8, "brand_id": 188, "enabled": 1, "created_at": "2023-02-03T04:07:50.000Z", "updated_at": "2023-02-03T04:07:50.000Z"}, {"id": 319, "index": 92, "featured": 0, "category_id": 9, "brand_id": 188, "enabled": 1, "created_at": "2024-05-24T11:58:06.000Z", "updated_at": "2024-05-24T11:58:06.000Z"}, {"id": 208, "index": 0, "featured": 0, "category_id": 7, "brand_id": 189, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 230, "index": 0, "featured": 0, "category_id": 8, "brand_id": 189, "enabled": 1, "created_at": "2023-02-03T04:07:50.000Z", "updated_at": "2023-02-03T04:07:50.000Z"}, {"id": 321, "index": 0, "featured": 0, "category_id": 9, "brand_id": 189, "enabled": 1, "created_at": "2024-05-24T11:58:39.000Z", "updated_at": "2024-05-24T11:58:39.000Z"}, {"id": 404, "index": 0, "featured": 0, "category_id": 11, "brand_id": 189, "enabled": 1, "created_at": "2024-10-24T03:30:17.000Z", "updated_at": "2024-10-24T03:30:17.000Z"}, {"id": 209, "index": 0, "featured": 0, "category_id": 7, "brand_id": 190, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 143, "index": 0, "featured": 0, "category_id": 1, "brand_id": 191, "enabled": 1, "created_at": "2020-12-27T11:17:42.000Z", "updated_at": "2020-12-27T11:17:42.000Z"}, {"id": 127, "index": 0, "featured": 0, "category_id": 6, "brand_id": 192, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 128, "index": 100, "featured": 0, "category_id": 6, "brand_id": 193, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 126, "index": 0, "featured": 0, "category_id": 6, "brand_id": 194, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 129, "index": 0, "featured": 0, "category_id": 6, "brand_id": 195, "enabled": 1, "created_at": "2020-08-10T09:40:55.000Z", "updated_at": "2020-08-10T09:40:55.000Z"}, {"id": 133, "index": 0, "featured": 0, "category_id": 2, "brand_id": 196, "enabled": 1, "created_at": "2020-11-16T10:15:23.000Z", "updated_at": "2020-11-16T10:15:23.000Z"}, {"id": 134, "index": 0, "featured": 0, "category_id": 4, "brand_id": 197, "enabled": 1, "created_at": "2020-11-16T10:22:57.000Z", "updated_at": "2020-11-16T10:22:57.000Z"}, {"id": 139, "index": 0, "featured": 0, "category_id": 3, "brand_id": 198, "enabled": 1, "created_at": "2020-12-07T07:23:16.000Z", "updated_at": "2020-12-07T07:23:16.000Z"}, {"id": 144, "index": 0, "featured": 0, "category_id": 2, "brand_id": 199, "enabled": 1, "created_at": "2020-12-31T05:09:51.000Z", "updated_at": "2020-12-31T05:09:51.000Z"}, {"id": 147, "index": 0, "featured": 0, "category_id": 2, "brand_id": 200, "enabled": 1, "created_at": "2021-01-14T04:10:19.000Z", "updated_at": "2021-01-14T04:10:19.000Z"}, {"id": 149, "index": 0, "featured": 0, "category_id": 2, "brand_id": 201, "enabled": 1, "created_at": "2021-01-14T04:11:35.000Z", "updated_at": "2021-01-14T04:11:35.000Z"}, {"id": 148, "index": 0, "featured": 0, "category_id": 2, "brand_id": 202, "enabled": 1, "created_at": "2021-01-14T04:11:26.000Z", "updated_at": "2021-01-14T04:11:26.000Z"}, {"id": 150, "index": 0, "featured": 0, "category_id": 2, "brand_id": 203, "enabled": 1, "created_at": "2021-01-14T04:11:55.000Z", "updated_at": "2021-01-14T04:11:55.000Z"}, {"id": 151, "index": 0, "featured": 0, "category_id": 2, "brand_id": 204, "enabled": 1, "created_at": "2021-01-14T04:12:15.000Z", "updated_at": "2021-01-14T04:12:15.000Z"}, {"id": 152, "index": 0, "featured": 0, "category_id": 5, "brand_id": 205, "enabled": 1, "created_at": "2021-01-19T09:40:48.000Z", "updated_at": "2021-01-19T09:40:48.000Z"}, {"id": 154, "index": 0, "featured": 0, "category_id": 2, "brand_id": 206, "enabled": 1, "created_at": "2021-01-21T10:35:28.000Z", "updated_at": "2021-01-21T10:35:28.000Z"}, {"id": 156, "index": 0, "featured": 0, "category_id": 2, "brand_id": 207, "enabled": 1, "created_at": "2021-01-22T08:18:01.000Z", "updated_at": "2021-01-22T08:18:01.000Z"}, {"id": 157, "index": 0, "featured": 0, "category_id": 2, "brand_id": 208, "enabled": 1, "created_at": "2021-01-22T08:19:43.000Z", "updated_at": "2021-01-22T08:19:43.000Z"}, {"id": 158, "index": 0, "featured": 0, "category_id": 2, "brand_id": 209, "enabled": 1, "created_at": "2021-01-22T08:20:14.000Z", "updated_at": "2021-01-22T08:20:14.000Z"}, {"id": 179, "index": 0, "featured": 0, "category_id": 1, "brand_id": 209, "enabled": 1, "created_at": "2021-03-18T07:44:57.000Z", "updated_at": "2021-03-18T07:44:57.000Z"}, {"id": 159, "index": 0, "featured": 0, "category_id": 4, "brand_id": 210, "enabled": 1, "created_at": "2021-01-22T08:21:04.000Z", "updated_at": "2021-01-22T08:21:04.000Z"}, {"id": 213, "index": 0, "featured": 0, "category_id": 5, "brand_id": 210, "enabled": 1, "created_at": "2021-10-04T04:01:25.000Z", "updated_at": "2021-10-04T04:01:25.000Z"}, {"id": 216, "index": 0, "featured": 0, "category_id": 6, "brand_id": 210, "enabled": 1, "created_at": "2021-10-04T04:15:28.000Z", "updated_at": "2021-10-04T04:15:28.000Z"}, {"id": 160, "index": 0, "featured": 0, "category_id": 5, "brand_id": 211, "enabled": 1, "created_at": "2021-01-22T08:21:39.000Z", "updated_at": "2021-01-22T08:21:39.000Z"}, {"id": 161, "index": 0, "featured": 0, "category_id": 4, "brand_id": 211, "enabled": 1, "created_at": "2021-01-22T08:22:36.000Z", "updated_at": "2021-01-22T08:22:36.000Z"}, {"id": 408, "index": 0, "featured": 0, "category_id": 11, "brand_id": 211, "enabled": 1, "created_at": "2024-10-29T11:57:40.000Z", "updated_at": "2024-10-29T11:57:40.000Z"}, {"id": 162, "index": 0, "featured": 0, "category_id": 4, "brand_id": 212, "enabled": 1, "created_at": "2021-01-22T08:27:31.000Z", "updated_at": "2021-01-22T08:27:31.000Z"}, {"id": 163, "index": 0, "featured": 0, "category_id": 4, "brand_id": 213, "enabled": 1, "created_at": "2021-01-22T08:27:59.000Z", "updated_at": "2021-01-22T08:27:59.000Z"}, {"id": 164, "index": 0, "featured": 0, "category_id": 4, "brand_id": 214, "enabled": 1, "created_at": "2021-01-22T08:28:33.000Z", "updated_at": "2021-01-22T08:28:33.000Z"}, {"id": 165, "index": 0, "featured": 0, "category_id": 4, "brand_id": 215, "enabled": 1, "created_at": "2021-01-22T08:29:17.000Z", "updated_at": "2021-01-22T08:29:17.000Z"}, {"id": 400, "index": 0, "featured": 0, "category_id": 11, "brand_id": 215, "enabled": 1, "created_at": "2024-10-24T03:29:30.000Z", "updated_at": "2024-10-24T03:29:30.000Z"}, {"id": 169, "index": 0, "featured": 0, "category_id": 1, "brand_id": 216, "enabled": 1, "created_at": "2021-01-22T10:05:35.000Z", "updated_at": "2021-01-22T10:05:35.000Z"}, {"id": 167, "index": 0, "featured": 0, "category_id": 6, "brand_id": 217, "enabled": 1, "created_at": "2021-01-22T08:30:15.000Z", "updated_at": "2021-01-22T08:30:15.000Z"}, {"id": 168, "index": 0, "featured": 0, "category_id": 6, "brand_id": 218, "enabled": 1, "created_at": "2021-01-22T08:30:54.000Z", "updated_at": "2021-01-22T08:30:54.000Z"}, {"id": 212, "index": 0, "featured": 0, "category_id": 5, "brand_id": 218, "enabled": 1, "created_at": "2021-10-04T04:01:01.000Z", "updated_at": "2021-10-04T04:01:01.000Z"}, {"id": 396, "index": 0, "featured": 0, "category_id": 11, "brand_id": 218, "enabled": 1, "created_at": "2024-10-24T03:25:36.000Z", "updated_at": "2024-10-24T03:25:36.000Z"}, {"id": 171, "index": 0, "featured": 0, "category_id": 2, "brand_id": 219, "enabled": 1, "created_at": "2021-03-15T06:32:32.000Z", "updated_at": "2021-03-15T06:32:32.000Z"}, {"id": 174, "index": 0, "featured": 0, "category_id": 2, "brand_id": 221, "enabled": 1, "created_at": "2021-03-15T06:38:31.000Z", "updated_at": "2021-03-15T06:38:31.000Z"}, {"id": 176, "index": 0, "featured": 0, "category_id": 2, "brand_id": 223, "enabled": 1, "created_at": "2021-03-15T06:40:56.000Z", "updated_at": "2021-03-15T06:40:56.000Z"}, {"id": 178, "index": 0, "featured": 0, "category_id": 2, "brand_id": 225, "enabled": 1, "created_at": "2021-03-15T07:10:11.000Z", "updated_at": "2021-03-15T07:10:11.000Z"}, {"id": 180, "index": 0, "featured": 0, "category_id": 2, "brand_id": 226, "enabled": 1, "created_at": "2021-03-24T09:19:01.000Z", "updated_at": "2021-03-24T09:19:01.000Z"}, {"id": 181, "index": 0, "featured": 0, "category_id": 2, "brand_id": 227, "enabled": 1, "created_at": "2021-03-24T09:19:27.000Z", "updated_at": "2021-03-24T09:19:27.000Z"}, {"id": 183, "index": 0, "featured": 0, "category_id": 2, "brand_id": 229, "enabled": 1, "created_at": "2021-03-24T09:20:28.000Z", "updated_at": "2021-03-24T09:20:28.000Z"}, {"id": 210, "index": 0, "featured": 0, "category_id": 7, "brand_id": 230, "enabled": 1, "created_at": "2021-08-19T05:07:22.000Z", "updated_at": "2021-08-19T05:07:22.000Z"}, {"id": 229, "index": 0, "featured": 0, "category_id": 8, "brand_id": 230, "enabled": 1, "created_at": "2023-02-03T04:07:50.000Z", "updated_at": "2023-02-03T04:07:50.000Z"}, {"id": 320, "index": 0, "featured": 0, "category_id": 9, "brand_id": 230, "enabled": 1, "created_at": "2024-05-24T11:58:26.000Z", "updated_at": "2024-05-24T11:58:26.000Z"}, {"id": 211, "index": 0, "featured": 0, "category_id": 7, "brand_id": 231, "enabled": 1, "created_at": "2021-08-19T05:09:28.000Z", "updated_at": "2021-08-19T05:09:28.000Z"}, {"id": 217, "index": 0, "featured": 0, "category_id": 6, "brand_id": 232, "enabled": 1, "created_at": "2021-10-04T04:33:06.000Z", "updated_at": "2021-10-04T04:33:06.000Z"}, {"id": 218, "index": 0, "featured": 0, "category_id": 5, "brand_id": 232, "enabled": 1, "created_at": "2021-10-04T04:33:06.000Z", "updated_at": "2021-10-04T04:33:06.000Z"}, {"id": 394, "index": 0, "featured": 0, "category_id": 11, "brand_id": 232, "enabled": 1, "created_at": "2024-10-24T03:24:56.000Z", "updated_at": "2024-10-24T03:24:56.000Z"}, {"id": 219, "index": 0, "featured": 0, "category_id": 2, "brand_id": 233, "enabled": 1, "created_at": "2021-10-04T05:59:05.000Z", "updated_at": "2021-10-04T05:59:05.000Z"}, {"id": 226, "index": 0, "featured": 0, "category_id": 4, "brand_id": 234, "enabled": 1, "created_at": "2023-02-02T06:48:10.000Z", "updated_at": "2023-02-02T06:48:10.000Z"}, {"id": 227, "index": 0, "featured": 0, "category_id": 6, "brand_id": 235, "enabled": 1, "created_at": "2023-02-02T10:50:49.000Z", "updated_at": "2023-02-02T10:50:49.000Z"}, {"id": 233, "index": 0, "featured": 0, "category_id": 1, "brand_id": 236, "enabled": 1, "created_at": "2023-02-13T11:09:09.000Z", "updated_at": "2023-02-13T11:09:09.000Z"}, {"id": 236, "index": 0, "featured": 0, "category_id": 4, "brand_id": 238, "enabled": 1, "created_at": "2023-07-09T07:49:39.000Z", "updated_at": "2023-07-09T07:49:39.000Z"}, {"id": 331, "index": 0, "featured": 0, "category_id": 9, "brand_id": 238, "enabled": 1, "created_at": "2024-05-24T12:02:36.000Z", "updated_at": "2024-05-24T12:02:36.000Z"}, {"id": 237, "index": 0, "featured": 0, "category_id": 6, "brand_id": 239, "enabled": 1, "created_at": "2023-07-09T07:50:04.000Z", "updated_at": "2023-07-09T07:50:04.000Z"}, {"id": 407, "index": 94, "featured": 0, "category_id": 11, "brand_id": 239, "enabled": 1, "created_at": "2024-10-29T11:44:07.000Z", "updated_at": "2024-10-29T11:44:07.000Z"}, {"id": 238, "index": 0, "featured": 0, "category_id": 5, "brand_id": 240, "enabled": 1, "created_at": "2023-07-09T07:50:21.000Z", "updated_at": "2023-07-09T07:50:21.000Z"}, {"id": 239, "index": 0, "featured": 0, "category_id": 5, "brand_id": 241, "enabled": 1, "created_at": "2023-07-09T08:15:09.000Z", "updated_at": "2023-07-09T08:15:09.000Z"}, {"id": 240, "index": 0, "featured": 0, "category_id": 6, "brand_id": 242, "enabled": 1, "created_at": "2023-07-09T08:25:05.000Z", "updated_at": "2023-07-09T08:25:05.000Z"}, {"id": 241, "index": 0, "featured": 0, "category_id": 5, "brand_id": 242, "enabled": 1, "created_at": "2023-07-09T08:25:05.000Z", "updated_at": "2023-07-09T08:25:05.000Z"}, {"id": 242, "index": 0, "featured": 0, "category_id": 5, "brand_id": 243, "enabled": 1, "created_at": "2023-07-09T08:35:08.000Z", "updated_at": "2023-07-09T08:35:08.000Z"}, {"id": 243, "index": 0, "featured": 0, "category_id": 6, "brand_id": 243, "enabled": 1, "created_at": "2023-07-09T08:35:08.000Z", "updated_at": "2023-07-09T08:35:08.000Z"}, {"id": 244, "index": 0, "featured": 0, "category_id": 5, "brand_id": 244, "enabled": 1, "created_at": "2023-07-09T08:43:54.000Z", "updated_at": "2023-07-09T08:43:54.000Z"}, {"id": 245, "index": 0, "featured": 0, "category_id": 5, "brand_id": 245, "enabled": 1, "created_at": "2023-07-09T08:53:29.000Z", "updated_at": "2023-07-09T08:53:29.000Z"}, {"id": 246, "index": 0, "featured": 0, "category_id": 5, "brand_id": 246, "enabled": 1, "created_at": "2023-07-09T09:11:09.000Z", "updated_at": "2023-07-09T09:11:09.000Z"}, {"id": 247, "index": 0, "featured": 0, "category_id": 5, "brand_id": 247, "enabled": 1, "created_at": "2023-07-09T09:17:30.000Z", "updated_at": "2023-07-09T09:17:30.000Z"}, {"id": 248, "index": 0, "featured": 0, "category_id": 6, "brand_id": 248, "enabled": 1, "created_at": "2023-07-09T09:22:40.000Z", "updated_at": "2023-07-09T09:22:40.000Z"}, {"id": 249, "index": 0, "featured": 0, "category_id": 5, "brand_id": 248, "enabled": 1, "created_at": "2023-07-09T09:22:40.000Z", "updated_at": "2023-07-09T09:22:40.000Z"}, {"id": 250, "index": 0, "featured": 0, "category_id": 4, "brand_id": 249, "enabled": 1, "created_at": "2023-07-09T09:35:04.000Z", "updated_at": "2023-07-09T09:35:04.000Z"}, {"id": 251, "index": 0, "featured": 0, "category_id": 6, "brand_id": 250, "enabled": 1, "created_at": "2023-07-09T09:41:10.000Z", "updated_at": "2023-07-09T09:41:10.000Z"}, {"id": 252, "index": 0, "featured": 0, "category_id": 5, "brand_id": 250, "enabled": 1, "created_at": "2023-07-09T09:41:10.000Z", "updated_at": "2023-07-09T09:41:10.000Z"}, {"id": 253, "index": 0, "featured": 0, "category_id": 6, "brand_id": 251, "enabled": 1, "created_at": "2023-07-09T09:51:31.000Z", "updated_at": "2023-07-09T09:51:31.000Z"}, {"id": 254, "index": 0, "featured": 0, "category_id": 5, "brand_id": 251, "enabled": 1, "created_at": "2023-07-09T09:51:31.000Z", "updated_at": "2023-07-09T09:51:31.000Z"}, {"id": 255, "index": 0, "featured": 0, "category_id": 6, "brand_id": 252, "enabled": 1, "created_at": "2023-07-09T10:11:06.000Z", "updated_at": "2023-07-09T10:11:06.000Z"}, {"id": 256, "index": 0, "featured": 0, "category_id": 5, "brand_id": 252, "enabled": 1, "created_at": "2023-07-09T10:11:06.000Z", "updated_at": "2023-07-09T10:11:06.000Z"}, {"id": 259, "index": 0, "featured": 0, "category_id": 4, "brand_id": 253, "enabled": 1, "created_at": "2023-07-09T10:30:46.000Z", "updated_at": "2023-07-09T10:30:46.000Z"}, {"id": 260, "index": 0, "featured": 0, "category_id": 6, "brand_id": 253, "enabled": 1, "created_at": "2023-07-09T10:30:46.000Z", "updated_at": "2023-07-09T10:30:46.000Z"}, {"id": 261, "index": 0, "featured": 0, "category_id": 5, "brand_id": 253, "enabled": 1, "created_at": "2023-07-09T10:30:46.000Z", "updated_at": "2023-07-09T10:30:46.000Z"}, {"id": 262, "index": 0, "featured": 0, "category_id": 4, "brand_id": 254, "enabled": 1, "created_at": "2023-07-09T11:19:08.000Z", "updated_at": "2023-07-09T11:19:08.000Z"}, {"id": 263, "index": 0, "featured": 0, "category_id": 6, "brand_id": 254, "enabled": 1, "created_at": "2023-07-09T11:19:08.000Z", "updated_at": "2023-07-09T11:19:08.000Z"}, {"id": 264, "index": 0, "featured": 0, "category_id": 5, "brand_id": 254, "enabled": 1, "created_at": "2023-07-09T11:19:08.000Z", "updated_at": "2023-07-09T11:19:08.000Z"}, {"id": 265, "index": 0, "featured": 0, "category_id": 4, "brand_id": 255, "enabled": 1, "created_at": "2023-07-09T12:12:16.000Z", "updated_at": "2023-07-09T12:12:16.000Z"}, {"id": 266, "index": 0, "featured": 0, "category_id": 6, "brand_id": 255, "enabled": 1, "created_at": "2023-07-09T12:12:16.000Z", "updated_at": "2023-07-09T12:12:16.000Z"}, {"id": 267, "index": 0, "featured": 0, "category_id": 5, "brand_id": 255, "enabled": 1, "created_at": "2023-07-09T12:12:16.000Z", "updated_at": "2023-07-09T12:12:16.000Z"}, {"id": 268, "index": 0, "featured": 0, "category_id": 4, "brand_id": 256, "enabled": 1, "created_at": "2023-07-09T12:47:33.000Z", "updated_at": "2023-07-09T12:47:33.000Z"}, {"id": 269, "index": 0, "featured": 0, "category_id": 6, "brand_id": 256, "enabled": 1, "created_at": "2023-07-09T12:47:33.000Z", "updated_at": "2023-07-09T12:47:33.000Z"}, {"id": 270, "index": 0, "featured": 0, "category_id": 5, "brand_id": 256, "enabled": 1, "created_at": "2023-07-09T12:47:33.000Z", "updated_at": "2023-07-09T12:47:33.000Z"}, {"id": 271, "index": 0, "featured": 0, "category_id": 6, "brand_id": 257, "enabled": 1, "created_at": "2023-07-09T13:03:07.000Z", "updated_at": "2023-07-09T13:03:07.000Z"}, {"id": 272, "index": 0, "featured": 0, "category_id": 5, "brand_id": 257, "enabled": 1, "created_at": "2023-07-09T13:03:07.000Z", "updated_at": "2023-07-09T13:03:07.000Z"}, {"id": 403, "index": 0, "featured": 0, "category_id": 11, "brand_id": 257, "enabled": 1, "created_at": "2024-10-24T03:30:05.000Z", "updated_at": "2024-10-24T03:30:05.000Z"}, {"id": 273, "index": 0, "featured": 0, "category_id": 6, "brand_id": 258, "enabled": 1, "created_at": "2023-07-09T13:11:18.000Z", "updated_at": "2023-07-09T13:11:18.000Z"}, {"id": 277, "index": 0, "featured": 0, "category_id": 2, "brand_id": 259, "enabled": 1, "created_at": "2023-07-09T13:40:41.000Z", "updated_at": "2023-07-09T13:40:41.000Z"}, {"id": 279, "index": 0, "featured": 0, "category_id": 1, "brand_id": 260, "enabled": 1, "created_at": "2023-11-06T10:06:57.000Z", "updated_at": "2023-11-06T10:06:57.000Z"}, {"id": 281, "index": 0, "featured": 0, "category_id": 1, "brand_id": 261, "enabled": 1, "created_at": "2023-11-06T12:14:25.000Z", "updated_at": "2023-11-06T12:14:25.000Z"}, {"id": 282, "index": 0, "featured": 0, "category_id": 2, "brand_id": 261, "enabled": 1, "created_at": "2023-11-06T12:14:25.000Z", "updated_at": "2023-11-06T12:14:25.000Z"}, {"id": 283, "index": 0, "featured": 0, "category_id": 1, "brand_id": 262, "enabled": 1, "created_at": "2024-01-15T08:29:17.000Z", "updated_at": "2024-01-15T08:29:17.000Z"}, {"id": 284, "index": 0, "featured": 0, "category_id": 4, "brand_id": 263, "enabled": 1, "created_at": "2024-03-03T10:34:23.000Z", "updated_at": "2024-03-03T10:34:23.000Z"}, {"id": 285, "index": 0, "featured": 0, "category_id": 4, "brand_id": 264, "enabled": 1, "created_at": "2024-03-12T13:53:26.000Z", "updated_at": "2024-03-12T13:53:26.000Z"}, {"id": 286, "index": 0, "featured": 0, "category_id": 5, "brand_id": 265, "enabled": 1, "created_at": "2024-03-12T14:01:07.000Z", "updated_at": "2024-03-12T14:01:07.000Z"}, {"id": 287, "index": 0, "featured": 0, "category_id": 9, "brand_id": 266, "enabled": 1, "created_at": "2024-05-14T03:03:16.000Z", "updated_at": "2024-05-14T03:03:16.000Z"}, {"id": 288, "index": 98, "featured": 0, "category_id": 9, "brand_id": 267, "enabled": 1, "created_at": "2024-05-14T03:04:00.000Z", "updated_at": "2024-05-14T03:04:00.000Z"}, {"id": 289, "index": 0, "featured": 0, "category_id": 9, "brand_id": 268, "enabled": 1, "created_at": "2024-05-14T03:04:26.000Z", "updated_at": "2024-05-14T03:04:26.000Z"}, {"id": 290, "index": 0, "featured": 0, "category_id": 9, "brand_id": 269, "enabled": 1, "created_at": "2024-05-14T03:04:45.000Z", "updated_at": "2024-05-14T03:04:45.000Z"}, {"id": 291, "index": 0, "featured": 0, "category_id": 9, "brand_id": 270, "enabled": 1, "created_at": "2024-05-14T03:05:16.000Z", "updated_at": "2024-05-14T03:05:16.000Z"}, {"id": 292, "index": 0, "featured": 0, "category_id": 9, "brand_id": 271, "enabled": 1, "created_at": "2024-05-14T03:08:16.000Z", "updated_at": "2024-05-14T03:08:16.000Z"}, {"id": 293, "index": 0, "featured": 0, "category_id": 9, "brand_id": 272, "enabled": 1, "created_at": "2024-05-14T03:08:54.000Z", "updated_at": "2024-05-14T03:08:54.000Z"}, {"id": 294, "index": 0, "featured": 0, "category_id": 9, "brand_id": 273, "enabled": 1, "created_at": "2024-05-14T03:09:25.000Z", "updated_at": "2024-05-14T03:09:25.000Z"}, {"id": 295, "index": 0, "featured": 0, "category_id": 9, "brand_id": 274, "enabled": 1, "created_at": "2024-05-14T03:09:50.000Z", "updated_at": "2024-05-14T03:09:50.000Z"}, {"id": 296, "index": 0, "featured": 0, "category_id": 9, "brand_id": 275, "enabled": 1, "created_at": "2024-05-14T03:10:19.000Z", "updated_at": "2024-05-14T03:10:19.000Z"}, {"id": 297, "index": 0, "featured": 0, "category_id": 9, "brand_id": 276, "enabled": 1, "created_at": "2024-05-14T03:10:45.000Z", "updated_at": "2024-05-14T03:10:45.000Z"}, {"id": 298, "index": 0, "featured": 0, "category_id": 9, "brand_id": 277, "enabled": 1, "created_at": "2024-05-14T03:11:07.000Z", "updated_at": "2024-05-14T03:11:07.000Z"}, {"id": 299, "index": 0, "featured": 0, "category_id": 9, "brand_id": 278, "enabled": 1, "created_at": "2024-05-14T03:11:45.000Z", "updated_at": "2024-05-14T03:11:45.000Z"}, {"id": 300, "index": 94, "featured": 0, "category_id": 9, "brand_id": 279, "enabled": 1, "created_at": "2024-05-14T03:12:20.000Z", "updated_at": "2024-05-14T03:12:20.000Z"}, {"id": 301, "index": 91, "featured": 0, "category_id": 9, "brand_id": 280, "enabled": 1, "created_at": "2024-05-14T03:12:41.000Z", "updated_at": "2024-05-14T03:12:41.000Z"}, {"id": 302, "index": 0, "featured": 0, "category_id": 9, "brand_id": 281, "enabled": 1, "created_at": "2024-05-14T03:13:10.000Z", "updated_at": "2024-05-14T03:13:10.000Z"}, {"id": 303, "index": 0, "featured": 0, "category_id": 9, "brand_id": 282, "enabled": 1, "created_at": "2024-05-14T03:13:31.000Z", "updated_at": "2024-05-14T03:13:31.000Z"}, {"id": 304, "index": 97, "featured": 0, "category_id": 9, "brand_id": 283, "enabled": 1, "created_at": "2024-05-14T03:13:50.000Z", "updated_at": "2024-05-14T03:13:50.000Z"}, {"id": 305, "index": 95, "featured": 0, "category_id": 9, "brand_id": 284, "enabled": 1, "created_at": "2024-05-14T03:14:10.000Z", "updated_at": "2024-05-14T03:14:10.000Z"}, {"id": 306, "index": 0, "featured": 0, "category_id": 9, "brand_id": 285, "enabled": 1, "created_at": "2024-05-14T03:14:30.000Z", "updated_at": "2024-05-14T03:14:30.000Z"}, {"id": 307, "index": 96, "featured": 0, "category_id": 9, "brand_id": 286, "enabled": 1, "created_at": "2024-05-14T03:14:50.000Z", "updated_at": "2024-05-14T03:14:50.000Z"}, {"id": 308, "index": 0, "featured": 0, "category_id": 9, "brand_id": 287, "enabled": 1, "created_at": "2024-05-14T03:15:58.000Z", "updated_at": "2024-05-14T03:15:58.000Z"}, {"id": 309, "index": 0, "featured": 0, "category_id": 9, "brand_id": 288, "enabled": 1, "created_at": "2024-05-14T03:16:23.000Z", "updated_at": "2024-05-14T03:16:23.000Z"}, {"id": 310, "index": 100, "featured": 0, "category_id": 9, "brand_id": 289, "enabled": 1, "created_at": "2024-05-14T03:16:59.000Z", "updated_at": "2024-05-14T03:16:59.000Z"}, {"id": 311, "index": 0, "featured": 0, "category_id": 9, "brand_id": 290, "enabled": 1, "created_at": "2024-05-14T03:18:33.000Z", "updated_at": "2024-05-14T03:18:33.000Z"}, {"id": 312, "index": 0, "featured": 0, "category_id": 9, "brand_id": 291, "enabled": 1, "created_at": "2024-05-14T03:19:02.000Z", "updated_at": "2024-05-14T03:19:02.000Z"}, {"id": 313, "index": 99, "featured": 0, "category_id": 9, "brand_id": 292, "enabled": 1, "created_at": "2024-05-14T03:19:23.000Z", "updated_at": "2024-05-14T03:19:23.000Z"}, {"id": 314, "index": 0, "featured": 0, "category_id": 9, "brand_id": 293, "enabled": 1, "created_at": "2024-05-14T03:19:57.000Z", "updated_at": "2024-05-14T03:19:57.000Z"}, {"id": 315, "index": 0, "featured": 0, "category_id": 9, "brand_id": 294, "enabled": 1, "created_at": "2024-05-14T03:20:35.000Z", "updated_at": "2024-05-14T03:20:35.000Z"}, {"id": 316, "index": 0, "featured": 0, "category_id": 9, "brand_id": 295, "enabled": 1, "created_at": "2024-05-14T03:20:56.000Z", "updated_at": "2024-05-14T03:20:56.000Z"}, {"id": 317, "index": 0, "featured": 0, "category_id": 2, "brand_id": 296, "enabled": 1, "created_at": "2024-05-23T17:13:01.000Z", "updated_at": "2024-05-23T17:13:01.000Z"}, {"id": 318, "index": 0, "featured": 0, "category_id": 9, "brand_id": 297, "enabled": 1, "created_at": "2024-05-24T05:58:39.000Z", "updated_at": "2024-05-24T05:58:39.000Z"}, {"id": 334, "index": 0, "featured": 0, "category_id": 10, "brand_id": 299, "enabled": 1, "created_at": "2024-09-11T10:47:29.000Z", "updated_at": "2024-09-11T10:47:29.000Z"}, {"id": 335, "index": 0, "featured": 0, "category_id": 10, "brand_id": 300, "enabled": 1, "created_at": "2024-09-11T13:18:29.000Z", "updated_at": "2024-09-11T13:18:29.000Z"}, {"id": 336, "index": 0, "featured": 0, "category_id": 2, "brand_id": 301, "enabled": 1, "created_at": "2024-09-19T06:15:38.000Z", "updated_at": "2024-09-19T06:15:38.000Z"}, {"id": 337, "index": 0, "featured": 0, "category_id": 2, "brand_id": 302, "enabled": 1, "created_at": "2024-09-19T06:16:44.000Z", "updated_at": "2024-09-19T06:16:44.000Z"}, {"id": 338, "index": 0, "featured": 0, "category_id": 2, "brand_id": 303, "enabled": 1, "created_at": "2024-09-19T06:17:28.000Z", "updated_at": "2024-09-19T06:17:28.000Z"}, {"id": 339, "index": 0, "featured": 0, "category_id": 2, "brand_id": 304, "enabled": 1, "created_at": "2024-09-19T06:17:48.000Z", "updated_at": "2024-09-19T06:17:48.000Z"}, {"id": 340, "index": 0, "featured": 0, "category_id": 2, "brand_id": 305, "enabled": 1, "created_at": "2024-09-19T06:18:10.000Z", "updated_at": "2024-09-19T06:18:10.000Z"}, {"id": 341, "index": 0, "featured": 0, "category_id": 2, "brand_id": 306, "enabled": 1, "created_at": "2024-09-19T06:18:34.000Z", "updated_at": "2024-09-19T06:18:34.000Z"}, {"id": 342, "index": 0, "featured": 0, "category_id": 2, "brand_id": 307, "enabled": 1, "created_at": "2024-09-19T06:19:12.000Z", "updated_at": "2024-09-19T06:19:12.000Z"}, {"id": 343, "index": 0, "featured": 0, "category_id": 2, "brand_id": 308, "enabled": 1, "created_at": "2024-09-19T06:23:34.000Z", "updated_at": "2024-09-19T06:23:34.000Z"}, {"id": 344, "index": 0, "featured": 0, "category_id": 11, "brand_id": 309, "enabled": 1, "created_at": "2024-10-23T23:52:04.000Z", "updated_at": "2024-10-23T23:52:04.000Z"}, {"id": 345, "index": 0, "featured": 0, "category_id": 11, "brand_id": 310, "enabled": 1, "created_at": "2024-10-23T23:52:51.000Z", "updated_at": "2024-10-23T23:52:51.000Z"}, {"id": 346, "index": 0, "featured": 0, "category_id": 11, "brand_id": 311, "enabled": 1, "created_at": "2024-10-23T23:53:19.000Z", "updated_at": "2024-10-23T23:53:19.000Z"}, {"id": 347, "index": 0, "featured": 0, "category_id": 11, "brand_id": 312, "enabled": 1, "created_at": "2024-10-23T23:53:41.000Z", "updated_at": "2024-10-23T23:53:41.000Z"}, {"id": 349, "index": 0, "featured": 0, "category_id": 11, "brand_id": 314, "enabled": 1, "created_at": "2024-10-23T23:57:15.000Z", "updated_at": "2024-10-23T23:57:15.000Z"}, {"id": 350, "index": 0, "featured": 0, "category_id": 11, "brand_id": 315, "enabled": 1, "created_at": "2024-10-23T23:57:52.000Z", "updated_at": "2024-10-23T23:57:52.000Z"}, {"id": 351, "index": 0, "featured": 0, "category_id": 11, "brand_id": 316, "enabled": 1, "created_at": "2024-10-23T23:58:22.000Z", "updated_at": "2024-10-23T23:58:22.000Z"}, {"id": 352, "index": 0, "featured": 0, "category_id": 11, "brand_id": 317, "enabled": 1, "created_at": "2024-10-24T00:00:05.000Z", "updated_at": "2024-10-24T00:00:05.000Z"}, {"id": 353, "index": 0, "featured": 0, "category_id": 11, "brand_id": 318, "enabled": 1, "created_at": "2024-10-24T00:00:41.000Z", "updated_at": "2024-10-24T00:00:41.000Z"}, {"id": 354, "index": 0, "featured": 0, "category_id": 11, "brand_id": 319, "enabled": 1, "created_at": "2024-10-24T00:01:13.000Z", "updated_at": "2024-10-24T00:01:13.000Z"}, {"id": 355, "index": 0, "featured": 0, "category_id": 11, "brand_id": 320, "enabled": 1, "created_at": "2024-10-24T00:01:32.000Z", "updated_at": "2024-10-24T00:01:32.000Z"}, {"id": 356, "index": 0, "featured": 0, "category_id": 11, "brand_id": 321, "enabled": 1, "created_at": "2024-10-24T00:01:54.000Z", "updated_at": "2024-10-24T00:01:54.000Z"}, {"id": 357, "index": 0, "featured": 0, "category_id": 11, "brand_id": 322, "enabled": 1, "created_at": "2024-10-24T00:02:22.000Z", "updated_at": "2024-10-24T00:02:22.000Z"}, {"id": 358, "index": 93, "featured": 0, "category_id": 11, "brand_id": 323, "enabled": 1, "created_at": "2024-10-24T00:02:44.000Z", "updated_at": "2024-10-24T00:02:44.000Z"}, {"id": 359, "index": 0, "featured": 0, "category_id": 11, "brand_id": 324, "enabled": 1, "created_at": "2024-10-24T00:03:03.000Z", "updated_at": "2024-10-24T00:03:03.000Z"}, {"id": 360, "index": 0, "featured": 0, "category_id": 11, "brand_id": 325, "enabled": 1, "created_at": "2024-10-24T00:03:40.000Z", "updated_at": "2024-10-24T00:03:40.000Z"}, {"id": 361, "index": 0, "featured": 0, "category_id": 11, "brand_id": 326, "enabled": 1, "created_at": "2024-10-24T00:04:00.000Z", "updated_at": "2024-10-24T00:04:00.000Z"}, {"id": 362, "index": 0, "featured": 0, "category_id": 11, "brand_id": 327, "enabled": 1, "created_at": "2024-10-24T00:04:30.000Z", "updated_at": "2024-10-24T00:04:30.000Z"}, {"id": 363, "index": 0, "featured": 0, "category_id": 11, "brand_id": 328, "enabled": 1, "created_at": "2024-10-24T00:04:58.000Z", "updated_at": "2024-10-24T00:04:58.000Z"}, {"id": 364, "index": 0, "featured": 0, "category_id": 11, "brand_id": 329, "enabled": 1, "created_at": "2024-10-24T00:05:22.000Z", "updated_at": "2024-10-24T00:05:22.000Z"}, {"id": 365, "index": 0, "featured": 0, "category_id": 11, "brand_id": 330, "enabled": 1, "created_at": "2024-10-24T00:05:46.000Z", "updated_at": "2024-10-24T00:05:46.000Z"}, {"id": 366, "index": 0, "featured": 0, "category_id": 11, "brand_id": 331, "enabled": 1, "created_at": "2024-10-24T00:06:08.000Z", "updated_at": "2024-10-24T00:06:08.000Z"}, {"id": 367, "index": 97, "featured": 0, "category_id": 11, "brand_id": 332, "enabled": 1, "created_at": "2024-10-24T00:06:37.000Z", "updated_at": "2024-10-24T00:06:37.000Z"}, {"id": 368, "index": 91, "featured": 0, "category_id": 11, "brand_id": 333, "enabled": 1, "created_at": "2024-10-24T00:07:31.000Z", "updated_at": "2024-10-24T00:07:31.000Z"}, {"id": 369, "index": 0, "featured": 0, "category_id": 11, "brand_id": 334, "enabled": 1, "created_at": "2024-10-24T00:07:52.000Z", "updated_at": "2024-10-24T00:07:52.000Z"}, {"id": 370, "index": 92, "featured": 0, "category_id": 11, "brand_id": 335, "enabled": 1, "created_at": "2024-10-24T00:08:17.000Z", "updated_at": "2024-10-24T00:08:17.000Z"}, {"id": 371, "index": 0, "featured": 0, "category_id": 11, "brand_id": 336, "enabled": 1, "created_at": "2024-10-24T00:08:50.000Z", "updated_at": "2024-10-24T00:08:50.000Z"}, {"id": 372, "index": 0, "featured": 0, "category_id": 11, "brand_id": 337, "enabled": 1, "created_at": "2024-10-24T00:09:15.000Z", "updated_at": "2024-10-24T00:09:15.000Z"}, {"id": 373, "index": 0, "featured": 0, "category_id": 11, "brand_id": 338, "enabled": 1, "created_at": "2024-10-24T00:09:38.000Z", "updated_at": "2024-10-24T00:09:38.000Z"}, {"id": 374, "index": 0, "featured": 0, "category_id": 11, "brand_id": 339, "enabled": 1, "created_at": "2024-10-24T00:10:01.000Z", "updated_at": "2024-10-24T00:10:01.000Z"}, {"id": 375, "index": 0, "featured": 0, "category_id": 11, "brand_id": 340, "enabled": 1, "created_at": "2024-10-24T00:10:25.000Z", "updated_at": "2024-10-24T00:10:25.000Z"}, {"id": 376, "index": 0, "featured": 0, "category_id": 11, "brand_id": 341, "enabled": 1, "created_at": "2024-10-24T00:10:43.000Z", "updated_at": "2024-10-24T00:10:43.000Z"}, {"id": 377, "index": 0, "featured": 0, "category_id": 11, "brand_id": 342, "enabled": 1, "created_at": "2024-10-24T00:11:20.000Z", "updated_at": "2024-10-24T00:11:20.000Z"}, {"id": 378, "index": 0, "featured": 0, "category_id": 11, "brand_id": 343, "enabled": 1, "created_at": "2024-10-24T00:11:40.000Z", "updated_at": "2024-10-24T00:11:40.000Z"}, {"id": 379, "index": 0, "featured": 0, "category_id": 11, "brand_id": 344, "enabled": 1, "created_at": "2024-10-24T00:12:02.000Z", "updated_at": "2024-10-24T00:12:02.000Z"}, {"id": 380, "index": 95, "featured": 0, "category_id": 11, "brand_id": 345, "enabled": 1, "created_at": "2024-10-24T00:12:29.000Z", "updated_at": "2024-10-24T00:12:29.000Z"}, {"id": 381, "index": 0, "featured": 0, "category_id": 11, "brand_id": 346, "enabled": 1, "created_at": "2024-10-24T00:12:51.000Z", "updated_at": "2024-10-24T00:12:51.000Z"}, {"id": 382, "index": 0, "featured": 0, "category_id": 11, "brand_id": 347, "enabled": 1, "created_at": "2024-10-24T00:13:15.000Z", "updated_at": "2024-10-24T00:13:15.000Z"}, {"id": 383, "index": 0, "featured": 0, "category_id": 11, "brand_id": 348, "enabled": 1, "created_at": "2024-10-24T00:13:43.000Z", "updated_at": "2024-10-24T00:13:43.000Z"}, {"id": 406, "index": 100, "featured": 0, "category_id": 11, "brand_id": 349, "enabled": 1, "created_at": "2024-10-24T03:35:49.000Z", "updated_at": "2024-10-24T03:35:49.000Z"}, {"id": 409, "index": 0, "featured": 0, "category_id": 3, "brand_id": 350, "enabled": 1, "created_at": "2025-02-10T11:08:50.000Z", "updated_at": "2025-02-10T11:08:50.000Z"}, {"id": 410, "index": 0, "featured": 0, "category_id": 3, "brand_id": 351, "enabled": 1, "created_at": "2025-02-10T11:09:14.000Z", "updated_at": "2025-02-10T11:09:14.000Z"}]}
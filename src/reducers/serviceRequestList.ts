import {
  SERVICEREQUESTLIST_ENTER_RESET,
  SERVICEREQUESTLIST_FETCHITEMS_LOAD,
  SERVICEREQUESTLIST_FETCHITEMS_SUCCEED,
  SERVICEREQUESTLIST_FETCHITEMS_FAIL,
} from '../constants/actionTypes'

const ServiceRequestList = (state: any = {}, action: any) => {
  switch (action.type) {
    case SERVICEREQUESTLIST_ENTER_RESET:
      return {}
    case SERVICEREQUESTLIST_FETCHITEMS_LOAD:
      return { ...state, isFetchItemsLoading: true, fetchItemsErrors: null }
    case SERVICEREQUESTLIST_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        pagination: { ...state.pagination, total: action.total },
      }
    case SERVICEREQUESTLIST_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors ? [].concat(action.fetchItemsErrors) : null,
      }
    default:
      return state
  }
}

export default ServiceRequestList
import {
  CREDIT_PLAN_RESET,
  CREDIT_PLAN_FETCHITEMS_LOAD,
  CREDIT_PLAN_FETCHITEMS_SUCCEED,
  CREDIT_PLAN_FETCHITEMS_FAIL,
} from "../constants/actionTypes";

const CreditPlan = (state: any = {}, action: any) => {
  switch (action.type) {
    case CREDIT_PLAN_RESET:
      return {};
    case CREDIT_PLAN_FETCHITEMS_LOAD:
      return {
        ...state,
        isFetchItemsLoading: true,
        fetchItemsErrors: null,
      };
    case CREDIT_PLAN_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        currency_rate: action.currency_rate,
        pagination: { ...state.pagination, total: action.total },
      };
    case CREDIT_PLAN_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors
          ? [].concat(action.fetchItemsErrors)
          : null,
      };
    default:
      return state;
  }
};

export default CreditPlan;

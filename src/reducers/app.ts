import {
  APP_ACCESS_TOKEN_SET,
  APP_ACCESS_TOKEN_CLEAR,
  USER_PROFILE_REQUEST_LOAD,
  USER_PROFILE_REQUEST_SUCCEED,
  USER_PROFILE_REQUEST_FAIL,
  APP_LOGIN_REDIRECT_PATH_CLEAR,
  APP_LOGIN_REDIRECT_PATH_SET,
} from 'constants/actionTypes'

const reducers = (state: any = {}, action: any) => {
  switch (action.type) {
    case APP_ACCESS_TOKEN_SET:
      return {
        ...state,
        accessToken: action.accessToken,
      }
    case APP_ACCESS_TOKEN_CLEAR:
      return {
        ...state,
        accessToken: undefined,
        user: undefined,
      }
    case APP_LOGIN_REDIRECT_PATH_SET:
      return {
        ...state,
        loginRedirectPath: action.loginRedirectPath,
      }
    case APP_LOGIN_REDIRECT_PATH_CLEAR:
      return {
        ...state,
        loginRedirectPath: undefined,
      }
    case USER_PROFILE_REQUEST_LOAD:
      return {
        ...state,
        isFetchUserLoading: true,
        fetchUserErrors: null,
      }
    case USER_PROFILE_REQUEST_SUCCEED: {
      const {
        user,
      } = action
      return {
        ...state,
        isFetchUserLoading: false,
        user: user,
      }
    }
    case USER_PROFILE_REQUEST_FAIL:
      return {
        ...state,
        isFetchUserLoading: false,
        user: null,
        fetchUserErrors: action.fetchUserErrors ? [].concat(action.fetchUserErrors) : null,
      }
    default:
      return state
  }
}

export default reducers

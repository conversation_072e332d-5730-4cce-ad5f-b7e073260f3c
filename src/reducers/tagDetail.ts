import {
    TAG<PERSON><PERSON>IL_ENTER_RESET,
    TAGDE<PERSON>IL_FETCHITEM_LOAD,
    TAGDETAIL_FETCHITEM_SUCCEED,
    TAGDE<PERSON>IL_FETCHITEM_FAIL,
} from '../constants/actionTypes'

const TagDetail = (state: any = {}, action: any) => {
    switch (action.type) {
        case TAGDETAIL_ENTER_RESET:
            return {}
        case TAGDETAIL_FETCHITEM_LOAD:
            return { ...state, isFetchItemSuccess: false, isFetchItemLoading: true, fetchItemErrors: null }
        case TAGDETAIL_FETCHITEM_SUCCEED:
            return {
                ...state,
                isFetchItemSuccess: true,
                isFetchItemLoading: false,
                item: action.item,
            }
        case TAGDETAIL_FETCHITEM_FAIL:
            return {
                ...state,
                isFetchItemSuccess: false,
                isFetchItemLoading: false,
                item: null,
                fetchItemErrors: action.fetchItemErrors ? [].concat(action.fetchItemErrors) : null,
            }
        default:
            return state
    }
}

export default TagDetail

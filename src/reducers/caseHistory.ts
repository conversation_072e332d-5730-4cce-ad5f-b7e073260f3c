import {
  CASE_HISTORY_RESET,
  CASE_HISTORY_FETCHITEMS_LOAD,
  CASE_HISTORY_FETCHITEMS_SUCCEED,
  CASE_HISTORY_FETCHITEMS_FAIL,
} from '../constants/actionTypes'

const CaseHistory = (state: any = {}, action: any) => {
  switch (action.type) {
    case CASE_HISTORY_RESET:
      return {}
    case CASE_HISTORY_FETCHITEMS_LOAD:
      return { 
        ...state, 
        isFetchItemsLoading: true, 
        fetchItemsErrors: null 
      }
    case CASE_HISTORY_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        pagination: { ...state.pagination, total: action.total },
      }
    case CASE_HISTORY_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors ? [].concat(action.fetchItemsErrors) : null,
      }
    default:
      return state
  }
}

export default CaseHistory
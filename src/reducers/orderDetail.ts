import { IOrderDetail } from 'types/orders';
import {
  ORDER_DETAIL_ENTER_RESET,
  ORDER_DETAIL_FETCHITEM_LOAD,
  ORDER_DETAIL_FETCHITEM_SUCCEED,
  ORDER_DETAIL_FETCHITEM_FAIL,
} from '../constants/actionTypes'

export interface OrderDetailState {
  isFetchItemLoading?: boolean;
  orderDetail?: IOrderDetail | null;
  fetchItemErrors?: any[] | null;
}

interface OrderDetailResetAction {
  type: typeof ORDER_DETAIL_ENTER_RESET;
}

interface OrderDetailLoadAction {
  type: typeof ORDER_DETAIL_FETCHITEM_LOAD;
}

interface OrderDetailSucceedAction {
  type: typeof ORDER_DETAIL_FETCHITEM_SUCCEED;
  orderDetail: IOrderDetail;
}

interface OrderDetailFailAction {
  type: typeof ORDER_DETAIL_FETCHITEM_FAIL;
  fetchItemErrors?: any;
}

type OrderDetailAction =
  | OrderDetailResetAction
  | OrderDetailLoadAction
  | OrderDetailSucceedAction
  | OrderDetailFailAction;

const initialState: OrderDetailState = {};

const OrderDetail = (state: OrderDetailState = initialState, action: OrderDetailAction): OrderDetailState => {
  switch (action.type) {
    case ORDER_DETAIL_ENTER_RESET:
      return {}
    case ORDER_DETAIL_FETCHITEM_LOAD:
      return {
        ...state,
        isFetchItemLoading: true,
        fetchItemErrors: null
      }
    case ORDER_DETAIL_FETCHITEM_SUCCEED:
      return {
        ...state,
        isFetchItemLoading: false,
        orderDetail: action.orderDetail,
      }
    case ORDER_DETAIL_FETCHITEM_FAIL:
      return {
        ...state,
        isFetchItemLoading: false,
        orderDetail: null,
        fetchItemErrors: action.fetchItemErrors ? [].concat(action.fetchItemErrors) : null,
      }
    default:
      return state
  }
}

export default OrderDetail

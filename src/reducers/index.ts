import { combineReducers } from 'redux'
import app from './app'
import articleDetail from './articleDetail'
import articleList from './articleList'
import caseHistory from './caseHistory'
import creditPlan from './creditPlan'
import currencyRate from './currencyRate'
import newAuthenticationForm from './newAuthenticationForm'
import orderDetail from './orderDetail'
import orderList from './orderList'
import productBrand from './productBrand'
import productCategory from './productCategory'
import productModel from './productModel'
import serviceRequestDetail from './serviceRequestDetail'
import serviceRequestList from './serviceRequestList'
import serviceRequestMessage from "./serviceRequestMessage";
import startAuthenticationHome from './startAuthenticationHome'
import tagDetail from './tagDetail'
import verifiedBusinessDetail from './verifiedBusinessDetail'
import verifiedBusinessList from './verifiedBusinessList'

export const rootReducer = combineReducers({
  app,
  articleList,
  articleDetail,
  caseHistory,
  creditPlan,
  currencyRate,
  newAuthenticationForm,
  orderDetail,
  orderList,
  productBrand,
  productCategory,
  productModel,
  serviceRequestMessage,
  serviceRequestList,
  serviceRequestDetail,
  startAuthenticationHome,
  tagDetail,
  verifiedBusinessList,
  verifiedBusinessDetail,
})

export type RootState = ReturnType<typeof rootReducer>
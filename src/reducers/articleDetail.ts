import {
  ARTICL<PERSON><PERSON><PERSON><PERSON>_ENTER_RESET,
  AR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_FETCHITEM_LOAD,
  ARTICL<PERSON><PERSON><PERSON><PERSON>_FETCHITEM_SUCCEED,
  ARTICLEDETA<PERSON>_FETCHITEM_FAIL,
} from '../constants/actionTypes'

const ArticleDetail = (state: any = {}, action: any) => {
  switch (action.type) {
    case ARTICLEDETAIL_ENTER_RESET:
      return {}
    case ARTICLEDETAIL_FETCHITEM_LOAD:
      return { ...state, isFetchItemLoading: true, fetchItemErrors: null }
    case ARTICLEDETAIL_FETCHITEM_SUCCEED:
      return {
        ...state,
        isFetchItemLoading: false,
        item: action.item,
      }
    case ARTICLEDETAIL_FETCHITEM_FAIL:
      return {
        ...state,
        isFetchItemLoading: false,
        item: null,
        fetchItemErrors: action.fetchItemErrors ? [].concat(action.fetchItemErrors) : null,
      }
    default:
      return state
  }
}

export default ArticleDetail

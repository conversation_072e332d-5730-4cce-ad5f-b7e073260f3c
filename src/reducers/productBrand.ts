import {
  PRODUCT_BRAND_ENTER_RESET,
  PRODUCT_BRAND_FETCHITEMS_LOAD,
  PRODUCT_BRAND_FETCHITEMS_SUCCEED,
  PRODUCT_BRAND_FETCHITEMS_FAIL,
} from "../constants/actionTypes";

const ProductBrand = (state: any = {}, action: any) => {
  switch (action.type) {
    case PRODUCT_BRAND_ENTER_RESET:
      return {};
    case PRODUCT_BRAND_FETCHITEMS_LOAD:
      return {
        ...state,
        isFetchItemsLoading: true,
        fetchItemsErrors: null,
      };
    case PRODUCT_BRAND_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        pagination: { ...state.pagination, total: action.total },
        categoryId: action.categoryId,
      };
    case PRODUCT_BRAND_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors
          ? [].concat(action.fetchItemsErrors)
          : null,
      };
    default:
      return state;
  }
};

export default ProductBrand;

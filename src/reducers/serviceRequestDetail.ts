import {
  SERVICEREQUESTDE<PERSON>IL_ENTER_RESET,
  SERVICEREQUESTDETAIL_FETCHITEM_LOAD,
  SERVICEREQUESTDE<PERSON><PERSON>_FETCHITEM_SUCCEED,
  SERVICEREQUESTDE<PERSON>IL_FETCHITEM_FAIL,
} from '../constants/actionTypes'

const ServiceRequestDetail = (state: any = {}, action: any) => {
  switch (action.type) {
    case SERVICEREQUESTDETAIL_ENTER_RESET:
      return {}
    case SERVICEREQUESTDETAIL_FETCHITEM_LOAD:
      return { ...state, isFetchItemLoading: true, fetchItemErrors: null }
    case SERVICEREQUESTDETAIL_FETCHITEM_SUCCEED:
      return {
        ...state,
        isFetchItemLoading: false,
        item: action.item,
      }
    case SERVICEREQUESTDETAIL_FETCHITEM_FAIL:
      return {
        ...state,
        isFetchItemLoading: false,
        item: null,
        fetchItemErrors: action.fetchItemErrors ? [].concat(action.fetchItemErrors) : null,
      }
    default:
      return state
  }
}

export default ServiceRequestDetail

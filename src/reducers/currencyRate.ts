import {
  CURRENCY_RATE_RESET,
  CURRENCY_RATE_FETCHITEMS_LOAD,
  CURRENCY_RATE_FETCHITEMS_SUCCEED,
  CURRENCY_RATE_FETCHITEMS_FAIL,
} from '../constants/actionTypes'

const CurrencyRate = (state: any = {}, action: any) => {
  switch (action.type) {
    case CURRENCY_RATE_RESET:
      return {}
    case CURRENCY_RATE_FETCHITEMS_LOAD:
      return { 
        ...state, 
        isFetchItemsLoading: true, 
        fetchItemsErrors: null 
      }
    case CURRENCY_RATE_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        pagination: { ...state.pagination, total: action.total },
      }
    case CURRENCY_RATE_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors ? [].concat(action.fetchItemsErrors) : null,
      }
    default:
      return state
  }
}

export default CurrencyRate

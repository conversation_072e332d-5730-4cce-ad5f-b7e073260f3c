import {
  PRODUCT_M<PERSON>EL_ENTER_RESET,
  PRODUCT_MODEL_FETCHITEMS_LOAD,
  PRODUCT_MODEL_FETCHITEMS_SUCCEED,
  PRODUCT_MODEL_FETCHITEMS_FAIL,
} from '../constants/actionTypes'

const ProductModel = (state: any = {}, action: any) => {
  switch (action.type) {
    case PRODUCT_MODEL_ENTER_RESET:
      return {}
    case PRODUCT_MODEL_FETCHITEMS_LOAD:
      return { 
        ...state, 
        isFetchItemsLoading: true, 
        fetchItemsErrors: null 
      }
    case PRODUCT_MODEL_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        pagination: { ...state.pagination, total: action.total },
        categoryId: action.categoryId,
        brandId: action.brandId,
      }
    case PRODUCT_MODEL_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors ? [].concat(action.fetchItemsErrors) : null,
      }
    default:
      return state
  }
}

export default ProductModel

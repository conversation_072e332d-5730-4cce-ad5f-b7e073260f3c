import {
    VERIFIED_BUSINESS_DETAIL_ENTER_RESET,
    VERIFIED_BUSINESS_DETAIL_REQUEST_FAIL,
    VERIFIED_BUSINESS_DETAIL_REQUEST_LOAD,
    VERIFIED_BUSINESS_DETAIL_REQUEST_SUCCEED,
  } from '../constants/actionTypes'
  
  const eventList = (state: any = {}, action: any) => {
    switch (action.type) {
      case VERIFIED_BUSINESS_DETAIL_ENTER_RESET:
        return {}
      case VERIFIED_BUSINESS_DETAIL_REQUEST_LOAD:
        return {
          ...state,
          requestLoading: true,
          requestErrors: null
        }
      case VERIFIED_BUSINESS_DETAIL_REQUEST_SUCCEED:
        return {
          ...state,
          requestLoading: false,
          requestResult: action.requestResult,
        }
      case VERIFIED_BUSINESS_DETAIL_REQUEST_FAIL:
        return {
          ...state,
          requestLoading: false,
          requestResult: null,
          requestErrors: action.requestErrors ? [].concat(action.requestErrors) : null,
        }
      default:
        return state
    }
  }
  
  export default eventList
  
import {
  SERVICEREQUESTMESSAGE_ENTER_RESET,
  SERVICEREQUESTMESSAGE_FETCHITEMS_LOAD,
  SERVICEREQUESTMESSAGE_FETCHITEMS_SUCCEED,
  SERVICEREQUESTMESSAGE_FETCHITEMS_FAIL,
} from '../constants/actionTypes'

const ServiceRequestMessage = (state: any = {}, action: any) => {
  switch (action.type) {
    case SERVICEREQUESTMESSAGE_ENTER_RESET:
      return {}
    case SERVICEREQUESTMESSAGE_FETCHITEMS_LOAD:
      return { 
        ...state, 
        isFetchItemsLoading: true, 
        fetchItemsErrors: null 
      }
    case SERVICEREQUESTMESSAGE_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        messages: action.messages,
        pagination: { ...state.pagination, total: action.total },
      }
    case SERVICEREQUESTMESSAGE_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        messages: null,
        fetchItemsErrors: action.fetchItemsErrors ? [].concat(action.fetchItemsErrors) : null,
      }
    default:
      return state
  }
}

export default ServiceRequestMessage

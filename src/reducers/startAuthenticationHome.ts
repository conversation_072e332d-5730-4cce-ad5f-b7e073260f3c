import {
  START_AUTHENTICATION_HOME_RESET,
  START_AUTHENTICATION_HOME_FETCH_LOAD,
  START_AUTHENTICATION_HOME_FETCH_SUCCEED,
  START_AUTHENTICATION_HOME_FETCH_FAIL,
} from '../constants/actionTypes'

const StartAuthenticationHome = (state: any = {}, action: any) => {
  switch (action.type) {
    case START_AUTHENTICATION_HOME_RESET:
      return {}
    case START_AUTHENTICATION_HOME_FETCH_LOAD:
      return { 
        ...state, 
        isLoading: true, 
        errors: null 
      }
    case START_AUTHENTICATION_HOME_FETCH_SUCCEED:
      return {
        ...state,
        isLoading: false,
        data: action.data,
        pagination: { ...state.pagination, total: action.total },
      }
    case START_AUTHENTICATION_HOME_FETCH_FAIL:
      return {
        ...state,
        isLoading: false,
        data: null,
        errors: action.errors ? [].concat(action.errors) : null,
      }
    default:
      return state
  }
}

export default StartAuthenticationHome

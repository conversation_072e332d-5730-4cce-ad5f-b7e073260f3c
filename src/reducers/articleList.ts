import {
  ARTICLELIST_ENTER_RESET,
  ARTICLELIST_FETCHITEMS_LOAD,
  ARTICLELIST_FETCHITEMS_SUCCEED,
  ARTICLELIST_FETCHITEMS_FAIL,
} from '../constants/actionTypes'

const ArticleList = (state: any = {}, action: any) => {
  switch (action.type) {
    case ARTICLELIST_ENTER_RESET:
      return {}
    case ARTICLELIST_FETCHITEMS_LOAD:
      return { ...state, isFetchItemsLoading: true, fetchItemsErrors: null }
    case ARTICLELIST_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        pagination: { ...state.pagination, total: action.total },
      }
    case ARTICLELIST_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors ? [].concat(action.fetchItemsErrors) : null,
      }
    default:
      return state
  }
}

export default ArticleList

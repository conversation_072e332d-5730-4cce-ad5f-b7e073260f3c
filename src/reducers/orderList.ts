import { OrderItem } from "types/orders";
import {
  ORDER_LIST_ENTER_RESET,
  ORDER_LIST_FETCHITEMS_LOAD,
  ORDER_LIST_FETCHITEMS_SUCCEED,
  ORDER_LIST_FETCHITEMS_FAIL,
  ORDER_LIST_FETCH_PHOTOS_REQUIRED_LOAD,
  ORDER_LIST_FETCH_PHOTOS_REQUIRED_SUCCEED,
  ORDER_LIST_FETCH_PHOTOS_REQUIRED_FAIL,
} from "../constants/actionTypes";

interface OrderListState {
  isFetchItemsLoading?: boolean;
  items?: OrderItem[];
  pagination?: {
    total?: number;
  };
  fetchItemsErrors?: any[];
  isFetchPhotosRequiredLoading?: boolean;
  photosRequiredItems?: OrderItem[];
  fetchPhotosRequiredErrors?: any[];
}

const initialState: OrderListState = {};

const OrderList = (state: OrderListState = initialState, action: any) => {
  switch (action.type) {
    case ORDER_LIST_ENTER_RESET:
      return {};
    case ORDER_LIST_FETCHITEMS_LOAD:
      return {
        ...state,
        isFetchItemsLoading: true,
        fetchItemsErrors: null,
      };
    case ORDER_LIST_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        pagination: { ...state.pagination, total: action.total },
      };
    case ORDER_LIST_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors
          ? [].concat(action.fetchItemsErrors)
          : null,
      };
    case ORDER_LIST_FETCH_PHOTOS_REQUIRED_LOAD:
      return {
        ...state,
        isFetchPhotosRequiredLoading: true,
        fetchPhotosRequiredErrors: null,
      };
    case ORDER_LIST_FETCH_PHOTOS_REQUIRED_SUCCEED:
      return {
        ...state,
        isFetchPhotosRequiredLoading: false,
        photosRequiredItems: action.photosRequiredItems,
      };
    case ORDER_LIST_FETCH_PHOTOS_REQUIRED_FAIL:
      return {
        ...state,
        isFetchPhotosRequiredLoading: false,
        photosRequiredItems: null,
        fetchPhotosRequiredErrors: action.fetchPhotosRequiredErrors
          ? [].concat(action.fetchPhotosRequiredErrors)
          : null,
      };
    default:
      return state;
  }
};

export default OrderList;

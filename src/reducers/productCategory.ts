import {
  PRODUCT_CATEGORY_ENTER_RESET,
  PRODUCT_CATEGORY_FETCHITEMS_LOAD,
  PRODUCT_CATEGORY_FETCHITEMS_SUCCEED,
  PRODUCT_CATEGORY_FETCHITEMS_FAIL,
} from "../constants/actionTypes";

const ProductCategory = (state: any = {}, action: any) => {
  switch (action.type) {
    case PRODUCT_CATEGORY_ENTER_RESET:
      return {};
    case PRODUCT_CATEGORY_FETCHITEMS_LOAD:
      return { ...state, isFetchItemsLoading: true, fetchItemsErrors: null };
    case PRODUCT_CATEGORY_FETCHITEMS_SUCCEED:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: action.items,
        pagination: { ...state.pagination, total: action.total },
      };
    case PRODUCT_CATEGORY_FETCHITEMS_FAIL:
      return {
        ...state,
        isFetchItemsLoading: false,
        items: null,
        fetchItemsErrors: action.fetchItemsErrors
          ? [].concat(action.fetchItemsErrors)
          : null,
      };
    default:
      return state;
  }
};

export default ProductCategory;

import { configureStore } from "@reduxjs/toolkit";
import { rootReducer } from "reducers";

const isServer = typeof window === "undefined";

const getInitialState = () => ({
  app: {
    accessToken: isServer ? null : localStorage.getItem("accessToken"),
  },
});

// const bindMiddleware = (middleware: any) => {
//   if (process.env.NODE_ENV !== 'production') {
//     const { composeWithDevTools } = require('redux-devtools-extension')
//     return composeWithDevTools(applyMiddleware(...middleware))
//   }
//   return applyMiddleware(...middleware)
// }

// const reducer = (state: any = getInitialState(), action: any) => {
//   if (action.type === HYDRATE) {
//     const nextState = {
//       ...state, // use previous state
//       ...action.payload, // apply delta from hydration
//     }
//     return nextState
//   }
//   return rootReducer(state, action)
// }

// const initStore = () => createStore(reducer, bindMiddleware([thunkMiddleware]))

// const wrapper = createWrapper(initStore)

// const storeWrapper = { wrapper }

// export default storeWrapper

// const bindMiddleware = (middleware: any) => {
//   if (process.env.NODE_ENV !== 'production') {
//     const { composeWithDevTools } = require('redux-devtools-extension')
//     return composeWithDevTools(applyMiddleware(...middleware))
//   }
//   return applyMiddleware(...middleware)
// }

const reducer = (state: any = getInitialState(), action: any) => {
  // if (action.type === HYDRATE) {
  //   const nextState = {
  //     ...state, // use previous state
  //     ...action.payload, // apply delta from hydration
  //   }
  //   return nextState
  // }
  return rootReducer(state, action);
};

export const store = configureStore({ reducer });

// const wrapper = createWrapper(initStore)

// const storeWrapper = { wrapper }

// export default storeWrapper

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;

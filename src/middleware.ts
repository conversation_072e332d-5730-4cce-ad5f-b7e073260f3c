import { NextRequest, NextResponse } from 'next/server'

const PUBLIC_FILE = /\.(.*)$/
const APP_LOCALES = [
  'en',
  'zh-<PERSON>',
  'zh-<PERSON><PERSON>',
  // 'pl',
]

// https://nextjs.org/docs/pages/building-your-application/routing/internationalization

export async function middleware(req: NextRequest) {
  if (
    req.nextUrl.pathname.startsWith('/_next') ||
    req.nextUrl.pathname.includes('/api/') ||
    PUBLIC_FILE.test(req.nextUrl.pathname)
  ) {
    return
  }

  if (req.nextUrl.locale === 'zh') {
    return NextResponse.redirect(
      new URL(`/zh-Hant/${req.nextUrl.pathname}${req.nextUrl.search}`, req.url)
    )
  }

  if (APP_LOCALES.indexOf(req.nextUrl.locale) === -1) {
    // const locale = req.cookies.get('NEXT_LOCALE')?.value || 'en'
    const locale = 'en'
    return NextResponse.redirect(
      new URL(`/${locale}${req.nextUrl.pathname}${req.nextUrl.search}`, req.url)
      // new URL(`/${req.nextUrl.pathname}${req.nextUrl.search}`, req.url)
    )
  }
}
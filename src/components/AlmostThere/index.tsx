import React from "react";
import { useRouter } from "next/router";

import { PATH_ROUTE } from "constants/app";
import { setLoginRedirectPath } from "actions/app";
import useAppDispatch from "hooks/useAppDispatch";

const AlmostThere = ({ desc1, desc2 }: { desc1: string; desc2: string }) => {
  const router = useRouter();
  const { asPath } = router;
  const dispatch = useAppDispatch();
  const handleClick = (path: string) => {
    dispatch(setLoginRedirectPath({ loginRedirectPath: asPath }));
    router.push(path);
  };
  return (
    <div className="bg-dark-100 rounded-md space-y-2 md:py-8 py-2 gap-4 md:gap-6 px-3">
      <div className="text-xl md:text-2xl font-bold text-center">
        Almost There
      </div>
      <div className="md:text-base text-sm md:text-center">
        You are just a few steps away {desc1}
      </div>
      <div className="md:text-base text-sm md:text-center">
        Please create a
        <div
          onClick={() => {
            handleClick(PATH_ROUTE.REGISTER);
          }}
          className="underline text-red-100 inline mx-1 cursor-pointer"
        >
          new account
        </div>
        or
        <div
          onClick={() => {
            handleClick(PATH_ROUTE.LOGIN);
          }}
          className="underline text-red-100 inline mx-1 cursor-pointer"
        >
          log in
        </div>
        to {desc2}
      </div>
    </div>
  );
};

export default AlmostThere;

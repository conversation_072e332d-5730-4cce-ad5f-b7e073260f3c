import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useState } from 'react'
import { useIntl } from 'react-intl'
import css from './NFTCertificateContent.module.scss'
import resizeImageUrl from 'utils/resizeImageUrl'
import { ArrowRightOutlined } from '@ant-design/icons'

const NFTCertificateContent = () => {

    const intl = useIntl()
    const [faqCardOpenStatusMap, setFaqCardOpenStatusMap] = useState({} as any)

    const FAQ_ITEM_LIST = [
        {
            question: intl.formatMessage({ id: 'nft_certificate_page_faq_section_item_1_question' }),
            answer: `${intl.formatMessage({ id: 'nft_certificate_page_faq_section_item_1_answer' })}`,
        },
        {
            question: intl.formatMessage({ id: 'nft_certificate_page_faq_section_item_2_question' }),
            answer: intl.formatMessage({ id: 'nft_certificate_page_faq_section_item_2_answer' }),
        },
    ]

    const faqCardQuestionOnClick = (question: string) => {
        setFaqCardOpenStatusMap({
            ...faqCardOpenStatusMap,
            [question]: !faqCardOpenStatusMap[question],
        })
    }

    return (
        <AppContainer className={css.NFTCertificateContent}>
            <HomePageSectionHeader
                className={css.sectionHeader}
                subtitle={intl.formatMessage({ id: 'nft_certificate_page_subtitle' })}
                title={intl.formatMessage({ id: 'nft_certificate_page_title' })}
                description={<>
                    {intl.formatMessage({ id: 'nft_certificate_page_description' })}
                </>}
            />
            <div className={css.coverImage}>
                <img src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-nft-certificate.png`, { width: 500 })} />
            </div>
            <div className={css.faqCardGrid}>
                {
                    FAQ_ITEM_LIST.map((faqItem: any) => (
                        <div
                            className={clsx(css.faqCard, css.open)}
                            key={`faq-page-faq-card-${faqItem.question}`}
                        >
                            <h2
                                className={css.questionPart}
                                onClick={() => faqCardQuestionOnClick(faqItem.question)}
                            >
                                {faqItem.question}
                            </h2>
                            <div className={css.answerContainer}>
                                <div className={css.answer}>
                                    {faqItem.answer}
                                </div>
                            </div>
                        </div>
                    ))
                }
                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <h2
                        className={css.questionPart}
                    // onClick={() => faqCardQuestionOnClick(faqItem.question)}
                    >
                        {intl.formatMessage({ id: 'nft_certificate_page_faq_section_item_3_question' })}
                    </h2>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            {intl.formatMessage({ id: 'nft_certificate_page_faq_section_item_3_answer_1' })}
                            <br />
                            <br />
                            <a href={`https://legitapp.com/cert/6809939146413702`} target='_blank' rel='noopener noreferrer'>
                                View Certificate of Authenticity example
                            </a>
                            <br />
                            <br />
                            {intl.formatMessage({ id: 'nft_certificate_page_faq_section_item_3_answer_2' })}
                            <br />
                            <br />
                            <a href={`https://opensea.io/assets/matic/0x3bb83e704de6c1c6370232e75b3da15f3398d75d/6809939146413702`} target='_blank' rel='noopener noreferrer'>
                                View NFT certificate example on OpenSea
                            </a>
                            <br />
                            <br />
                            {intl.formatMessage({ id: 'nft_certificate_page_faq_section_item_3_answer_3' })}
                            <br />
                            <br />
                            <a href={`https://polygonscan.com/address/0x3bb83e704de6c1c6370232e75b3da15f3398d75d#nfttransfers`} target='_blank' rel='noopener noreferrer'>
                                View NFT metadata example on Polygonscan
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </AppContainer>
    )
}

export default NFTCertificateContent

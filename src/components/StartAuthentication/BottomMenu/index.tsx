import clsx from "clsx";
import React from "react";
import { menuItems } from "../constant";
import Image from "next/image";
import { useRouter } from "next/router";
import Link from "next/link";
import { useIntl } from "react-intl";

const BottomMenu = ({ className }: { className: string }) => {
  const intl = useIntl();
  const router = useRouter();
  const { route } = router;

  return (
    <div
      className={clsx("fixed bottom-0 left-0 right-0 h-16 bg-black", className)}
    >
      <div className="flex justify-center items-center w-full h-full">
        {menuItems.map((menu) => (
          <Link
            className="w-20 flex flex-col justify-center items-center gap-1 cursor-pointer"
            key={menu.href}
            href={menu.href}
          >
            <div>
              <Image
                src={menu.icon}
                alt={menu.title}
                width={20}
                height={20}
                className="w-5 h-5"
              />
            </div>
            <div
              className={clsx("text-sm text-white", {
                "font-semibold": route === menu.href,
              })}
            >
              {intl.formatMessage({
                id: `start_authentication_page_menu_${menu.title}`,
              })}
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default BottomMenu;

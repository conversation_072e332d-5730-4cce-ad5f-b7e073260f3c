import clsx from "clsx";
import SlideMenu from "components/SlideMenu";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const LeftPart = ({ className }: { className?: string }) => {
  return (
    <div
      className={clsx(
        "w-[280px] h-screen border-r border-[#29282D]",
        className
      )}
    >
      <div className="pl-12">
        <div className="h-32 flex items-center mb-3">
          <Link className="cursor-pointer" href="/">
            <Image
              src="/legit_app_logo.svg"
              alt="LEGIT APP LOGO"
              width={176}
              height={48}
            />
          </Link>
        </div>
        <SlideMenu />
      </div>
    </div>
  );
};

export default LeftPart;

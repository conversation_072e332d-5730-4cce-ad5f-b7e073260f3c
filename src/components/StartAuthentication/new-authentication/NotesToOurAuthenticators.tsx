import React from "react";
import HeaderTitle from "./HeaderTitle";
import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";

const NotesToOurAuthenticators = () => {
  const { setUserRemark, user_remark } = useNewAuthenticationForm();
  return (
    <div>
      <HeaderTitle title={"Notes to our Authenticators"} optional={true} />
      <div className="flex flex-col gap-5">
        <textarea
          rows={5}
          placeholder="Tell us more about your item e.g. condition, model code, purchase date."
          className="md:text-base text-xs resize-none outline-none bg-transparent border-gray-200 border w-full rounded-md px-2 py-2 placeholder-gray-100"
          value={user_remark}
          onChange={(e) => {
            setUserRemark(e.target.value);
          }}
        />
      </div>
    </div>
  );
};

export default NotesToOurAuthenticators;

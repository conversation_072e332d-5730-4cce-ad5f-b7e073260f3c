import React, { useRef, useState, useEffect, useMemo } from "react";
import Image from "next/image";
import { RootState } from "reducers";

import { IServicePlaceholder, IUploadPhotoItem } from "types/orders";
import { getLocalisedField } from "utils/locale";
import apiCore from "utils/apiCore";
import { showErrorPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import useAppSelector from "hooks/useAppSelector";
import AppSpin from "components/AppSpin";
import HeaderTitle from "components/StartAuthentication/new-authentication/HeaderTitle";

const PhotoCard = ({
  item,
  locale,
  setUploadPhotoList,
  allPlaceholders,
  uploadedImages,
  setUploadedImages,
  loadingStates,
  setLoadingStates,
}: {
  item: IServicePlaceholder;
  locale: string;
  setUploadPhotoList: (
    listOrUpdater:
      | IUploadPhotoItem[]
      | ((prevList: IUploadPhotoItem[]) => IUploadPhotoItem[])
  ) => void;
  allPlaceholders: IServicePlaceholder[];
  uploadedImages: { [key: number]: { url: string } };
  setUploadedImages: React.Dispatch<
    React.SetStateAction<{ [key: number]: { url: string } }>
  >;
  loadingStates: { [key: number]: boolean };
  setLoadingStates: React.Dispatch<
    React.SetStateAction<{ [key: number]: boolean }>
  >;
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { accessToken } = useAppSelector((state: RootState) => state.app);
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      try {
        const fileArray = Array.from(files);

        const currentIndex = allPlaceholders.findIndex((p) => p.id === item.id); // Find the index of the current item
        const availablePlaceholders = allPlaceholders.slice(currentIndex); // Get the placeholders after the current item

        const filesToProcess = fileArray.slice(0, availablePlaceholders.length); // Get the files to process

        const affectedPlaceholderIds = availablePlaceholders
          .slice(0, filesToProcess.length)
          .map((p) => p.id);

        // Update the loading states for the affected placeholders to true
        setLoadingStates((prev) => {
          const newStates = { ...prev };
          affectedPlaceholderIds.forEach((id) => {
            newStates[id] = true;
          });
          return newStates;
        });

        const uploadPromises = filesToProcess.map(async (file, index) => {
          const res = await apiCore.post(
            null,
            "v1/asset_image",
            file,
            accessToken
          );

          if (res && res.url) {
            const targetPlaceholder = availablePlaceholders[index];

            setUploadedImages((prev) => ({
              ...prev,
              [targetPlaceholder.id]: { url: res.url },
            }));

            return {
              image_url: res.url,
              source: "album",
              system_image_remark: {
                index: targetPlaceholder.index,
                service_placeholder_id: targetPlaceholder.id,
              },
            } as IUploadPhotoItem;
          }
          return null;
        });

        const uploadedPhotos = await Promise.all(uploadPromises);
        const validPhotos = uploadedPhotos.filter(
          (photo) => photo !== null
        ) as IUploadPhotoItem[];

        if (validPhotos.length > 0) {
          setUploadPhotoList((prevList: IUploadPhotoItem[]) => {
            let newList = [...prevList];

            validPhotos.forEach((newPhoto) => {
              const existingIndex = newList.findIndex(
                (photo: IUploadPhotoItem) =>
                  photo.system_image_remark.service_placeholder_id ===
                  newPhoto.system_image_remark.service_placeholder_id
              );

              if (existingIndex !== -1) {
                newList[existingIndex] = newPhoto;
              } else {
                newList.push(newPhoto);
              }
            });

            return newList;
          });
        }
      } catch (error) {
        showErrorPopupMessage(parseError(error).message);
      } finally {
        // Clear loading state for all affected placeholders
        const currentIndex = allPlaceholders.findIndex((p) => p.id === item.id);
        const availablePlaceholders = allPlaceholders.slice(currentIndex);
        const filesToProcess = Array.from(event.target.files || []).slice(
          0,
          availablePlaceholders.length
        );
        const affectedPlaceholderIds = availablePlaceholders
          .slice(0, filesToProcess.length)
          .map((p) => p.id);

        setLoadingStates((prev) => {
          const newStates = { ...prev };
          affectedPlaceholderIds.forEach((id) => {
            newStates[id] = false;
          });
          return newStates;
        });
      }
    }
  };

  const handleClick = () => {
    if (fileInputRef.current && !loadingStates[item.id]) {
      fileInputRef.current.click();
    }
  };

  const currentUploadedImage = uploadedImages[item.id];
  const isCurrentlyUploading = loadingStates[item.id];

  return (
    <div
      className={`${
        isCurrentlyUploading ? "cursor-not-allowed" : "cursor-pointer"
      } flex flex-col items-center md:gap-4 gap-1`}
      onClick={handleClick}
    >
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileUpload}
        accept="image/png, image/jpeg, image/jpg"
        multiple
        style={{ display: "none" }}
      />
      <div
        className="w-full border border-gray-200 rounded-xl flex items-center justify-center relative"
        style={
          currentUploadedImage
            ? {
                backgroundImage: `url(${currentUploadedImage.url})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
              }
            : {}
        }
      >
        <img src={item.image_url} className="w-32 h-32" alt={item.title} />

        {isCurrentlyUploading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-xl flex items-center justify-center">
            <AppSpin />
          </div>
        )}

        {/* right top corner icon */}
        {currentUploadedImage && (
          <div className="absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <svg
              className="w-4 h-4 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>
      <div className="md:text-base text-[10px] text-center">
        {getLocalisedField(item, "title", locale)}
      </div>
    </div>
  );
};

const PhotosList = ({
  title,
  list = [],
  locale,
  isOptional = false,
  setUploadPhotoList,
  onUploadingStateChange,
}: {
  title: string;
  list: IServicePlaceholder[] | undefined;
  locale: string;
  isOptional?: boolean;
  setUploadPhotoList: (
    listOrUpdater:
      | IUploadPhotoItem[]
      | ((prevList: IUploadPhotoItem[]) => IUploadPhotoItem[])
  ) => void;
  onUploadingStateChange?: (isUploading: boolean) => void;
}) => {
  const [uploadedImages, setUploadedImages] = useState<{
    [key: number]: { url: string };
  }>({});

  const [loadingStates, setLoadingStates] = useState<{
    [key: number]: boolean;
  }>({});

  useEffect(() => {
    const isAnyUploading = Object.values(loadingStates).some(
      (isLoading) => isLoading
    );
    console.log("isAnyUploading", isAnyUploading, loadingStates);
    if (isAnyUploading) {
      debugger;
    }
    onUploadingStateChange?.(isAnyUploading);
  }, [loadingStates, onUploadingStateChange]);

  const finialList = useMemo(() => {
    if (!isOptional) {
      return list;
    }
    return list.concat({
      id: -1,
      index: -1,
      title: "Additional",
      title_tc: "額外",
      title_sc: "额外",
      image_url: "/additional.png",
    } as IServicePlaceholder);
  }, [list, isOptional]);

  return (
    <div>
      <HeaderTitle title={title} />
      <div className="grid grid-cols-4 md:gap-4 gap-2">
        {finialList.map((item) => (
          <PhotoCard
            key={item.id}
            item={item}
            locale={locale}
            setUploadPhotoList={setUploadPhotoList}
            allPlaceholders={finialList}
            uploadedImages={uploadedImages}
            setUploadedImages={setUploadedImages}
            loadingStates={loadingStates}
            setLoadingStates={setLoadingStates}
          />
        ))}
      </div>
    </div>
  );
};

export default PhotosList;

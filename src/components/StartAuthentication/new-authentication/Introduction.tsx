import Image from "next/image";
import React, { useEffect } from "react";
import { RootState } from "reducers";

import { getLocalisedField } from "utils/locale";
import { ICategoryBrandModel } from "types/app";
import useAppSelector from "hooks/useAppSelector";
import { fetchModelList } from "actions/productModel";
import useAppDispatch from "hooks/useAppDispatch";
import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";

const Introduction = ({
  locale,
  categoryId,
  brandId,
  modelId,
}: {
  locale: string;
  categoryId: string;
  brandId: string;
  modelId: string;
}) => {
  const dispatch = useAppDispatch();
  const { setProductTitle } = useNewAuthenticationForm();

  useEffect(() => {
    const fetchModelInfo = () => {
      dispatch(
        fetchModelList({
          categoryId,
          brandId,
        })
      );
    };
    if (categoryId && brandId) {
      fetchModelInfo();
    }
  }, [dispatch, categoryId, brandId]);

  const productModel: any = useAppSelector(
    (state: RootState) => state.productModel
  );
  const { items: modelItems, isFetchItemsLoading } = productModel;

  const selectedModel =
    modelItems?.find(
      (item: ICategoryBrandModel) => item.id === Number(modelId)
    ) || {};

  const product_category = selectedModel?.product_category || {};
  const product_brand = selectedModel?.product_brand || {};

  useEffect(() => {
    setProductTitle({
      category_title: getLocalisedField(product_category, "title", locale),
      brand_title: product_brand.title,
      model_title: selectedModel.title,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product_brand.title, selectedModel.title]);

  return (
    <div className="mt-16 md:mt-24 bg-dark-100 rounded-xl flex flex-col justify-center items-center relative md:pt-24 pt-20 md:pb-9 pb-4">
      <div className="flex justify-between gap-1 absolute top-0 transform -translate-y-1/2">
        {product_brand.icon_image_url && (
          <div className="w-36 h-36">
            <Image
              src={product_brand.icon_image_url}
              width={150}
              height={150}
              alt={product_brand.title}
            />
          </div>
        )}

        {selectedModel.icon_image_url && (
          <div className="w-36 h-36">
            <Image
              src={selectedModel.icon_image_url}
              width={150}
              height={150}
              alt={product_brand.title}
            />
          </div>
        )}
      </div>
      <div className="text-center flex flex-col gap-1">
        <div className="text-gray-500">
          {getLocalisedField(product_category, "title", locale) || "-"}
        </div>
        <div className="md:text-xl text-base font-bold">
          {product_brand.title || "-"}
        </div>
        <div className="md:text-xl text-base font-bold">
          {selectedModel.title || "-"}
        </div>
      </div>
    </div>
  );
};

export default Introduction;

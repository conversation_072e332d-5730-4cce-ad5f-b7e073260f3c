import React from "react";
import clsx from "clsx";
import Image from "next/image";
import moment from "moment-timezone";

import { getLocalisedField } from "utils/locale";
import HeaderTitle from "./HeaderTitle";
import { IServiceLevel } from "types/orders";
import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";

const getServiceLevelStatus = (
  started_at: any,
  ended_at: any
): { isAvailable: boolean; message?: string } => {
  if (!started_at && !ended_at) {
    return { isAvailable: true };
  }

  try {
    const now = moment();
    const userTimezone = moment.tz.guess();

    const parseTimeString = (timeStr: string) => {
      const timeMatch = timeStr.match(/^(\d{1,2}):(\d{2})(?::(\d{2}))?$/);
      if (!timeMatch) return null;

      const [, hours, minutes, seconds = "00"] = timeMatch;
      return {
        hours: parseInt(hours),
        minutes: parseInt(minutes),
        seconds: parseInt(seconds),
      };
    };

    const getGMTFormat = () => {
      const timezoneOffset = now.format("Z");
      const gmtOffset = timezoneOffset.split(":")[0];
      return `GMT${gmtOffset}`;
    };

    const createTodayTimeFromGMT = (timeObj: {
      hours: number;
      minutes: number;
      seconds: number;
    }) => {
      const gmtTime = moment
        .utc()
        .hour(timeObj.hours)
        .minute(timeObj.minutes)
        .second(timeObj.seconds)
        .millisecond(0);

      return gmtTime.tz(userTimezone);
    };

    let startTime = null;
    let endTime = null;

    if (started_at) {
      const parsedStart = parseTimeString(started_at);
      if (!parsedStart) {
        return { isAvailable: true };
      }
      startTime = createTodayTimeFromGMT(parsedStart);
    }

    if (ended_at) {
      const parsedEnd = parseTimeString(ended_at);
      if (!parsedEnd) {
        return { isAvailable: true };
      }
      endTime = createTodayTimeFromGMT(parsedEnd);
    }

    if (startTime && endTime) {
      const crossesMidnight = startTime.isAfter(endTime);

      if (crossesMidnight) {
        const isInRange =
          now.isSameOrAfter(startTime) || now.isSameOrBefore(endTime);
        if (!isInRange) {
          return {
            isAvailable: false,
            message: `Available: ${startTime.format(
              "h:mmA"
            )} - ${endTime.format("h:mmA")} ${getGMTFormat()}`,
          };
        }
      } else {
        if (now.isBefore(startTime)) {
          return {
            isAvailable: false,
            message: `Available: ${startTime.format(
              "h:mmA"
            )} - ${endTime.format("h:mmA")} ${getGMTFormat()}`,
          };
        }
        if (now.isAfter(endTime)) {
          return {
            isAvailable: false,
            message: `Available: ${startTime.format(
              "h:mmA"
            )} - ${endTime.format("h:mmA")} ${getGMTFormat()}`,
          };
        }
      }
    } else if (startTime) {
      if (now.isBefore(startTime)) {
        return {
          isAvailable: false,
          message: `Available from ${startTime.format(
            "h:mmA"
          )} ${getGMTFormat()}`,
        };
      }
    } else if (endTime) {
      if (now.isAfter(endTime)) {
        return {
          isAvailable: false,
          message: `Available until ${endTime.format(
            "h:mmA"
          )} ${getGMTFormat()}`,
        };
      }
    }

    return { isAvailable: true };
  } catch (error) {
    return { isAvailable: true };
  }
};

const AuthenticationService = ({
  service_level,
  locale,
}: {
  service_level: IServiceLevel[] | undefined;
  locale: string;
}) => {
  const { currentServiceLevel, setCurrentServiceLevel } =
    useNewAuthenticationForm();
  return (
    <div>
      <HeaderTitle title="Authentication Service" />
      <div className="grid grid-cols-2 gap-4">
        {service_level?.map((level) => {
          const { started_at, ended_at } = level;
          const { isAvailable, message } = getServiceLevelStatus(
            started_at,
            ended_at
          );
          const isSelected = currentServiceLevel?.id === level.id;

          return (
            <div
              key={level.id}
              className={clsx(
                "border md:h-40 h-24 rounded-md flex flex-col items-center justify-center md:gap-2 gap-1 md:text-base text-sm",
                isAvailable ? "cursor-pointer" : "cursor-not-allowed",
                isSelected && isAvailable
                  ? "border-red-100 opacity-100"
                  : isAvailable
                  ? "border-gray-100 opacity-60"
                  : "border-gray-200 opacity-30 bg-gray-100"
              )}
              onClick={() => {
                if (isAvailable) {
                  setCurrentServiceLevel(level);
                }
              }}
            >
              <div>{getLocalisedField(level, "title", locale)}</div>
              <div className="flex gap-2 items-center">
                <div className="md:w-6 md:h-6 w-4 h-4">
                  <Image
                    src="/token.svg"
                    width={24}
                    height={24}
                    alt="token"
                    className="w-full h-full"
                  />
                </div>
                <div>{getLocalisedField(level, "subtitle", locale)}</div>
              </div>
              {!isAvailable && (
                <div className="md:text-sm text-xs text-gray-400 mt-1 text-center">
                  ({message || "Currently unavailable"})
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AuthenticationService;

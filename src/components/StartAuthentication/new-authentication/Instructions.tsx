import React, { useState } from "react";
import { IServiceGuideline } from "types/orders";
import ViewPhotoInstructionModal from "./ViewPhotoInstructionModal";

const Instructions = ({
  guidelineList,
  locale,
}: {
  guidelineList: IServiceGuideline[];
  locale: string;
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <div className="flex flex-col md:gap-5 gap-3">
        <div
          onClick={() => {
            setIsModalOpen(true);
          }}
          className="cursor-pointer bg-gradient-red md:py-4 py-2 rounded-md text-center md:text-xl text-sm"
        >
          VIEW PHOTO INSTRUCTION
        </div>
      </div>
      <ViewPhotoInstructionModal
        setIsModalOpen={setIsModalOpen}
        isModalOpen={isModalOpen}
        guidelineList={guidelineList}
        locale={locale}
      />
    </>
  );
};

export default Instructions;

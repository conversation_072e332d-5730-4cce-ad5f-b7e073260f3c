import React from "react";
import HeaderTitle from "./HeaderTitle";
import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";

const CaseCustomCode = () => {
  const { user_custom_code, setUserCustomCode } = useNewAuthenticationForm();
  return (
    <div>
      <HeaderTitle title={"Case Custom Code"} optional={true} />
      <div className="flex flex-col md:gap-5 gap-3">
        <div className="text-gray-500 md:text-base text-xs">
          You can input your own unique order number for better tracing in case
          history
        </div>
        <input
          placeholder="Enter case custom code"
          className="md:text-base text-xs outline-none bg-transparent border-gray-200 border w-full rounded-md px-2 py-2 placeholder-gray-100"
          value={user_custom_code}
          onChange={(e) => {
            setUserCustomCode(e.target.value);
          }}
        />
      </div>
    </div>
  );
};

export default CaseCustomCode;

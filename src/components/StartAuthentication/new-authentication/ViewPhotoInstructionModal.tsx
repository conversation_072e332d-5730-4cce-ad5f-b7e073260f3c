import React from "react";
import { IServiceGuideline } from "types/orders";
import InstructionCard from "./InstructionCard";
import { getLocalisedField } from "utils/locale";
import AppModal from "components/AppModal";
import AppSpin from "components/AppSpin";

const ViewPhotoInstructionModal = ({
  isModalOpen,
  setIsModalOpen,
  guidelineList,
  locale,
  loading = false,
}: {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  guidelineList: IServiceGuideline[];
  locale: string;
  loading?: boolean;
}) => {
  return (
    <AppModal
      title="Instructions"
      isModalOpen={isModalOpen}
      setIsModalOpen={setIsModalOpen}
    >
      {loading ? (
        <AppSpin />
      ) : (
        <>
          <div className="flex justify-between items-center gap-2 text-white mb-8">
            <div className="w-10 h-10">
              <img
                src="/icon-guideline-light.png"
                alt="icon-guideline-light"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 text-gray-100 md:text-base text-xs">
              <div>
                You can pinch to zoom and tap to focus within the camera view.
              </div>
              <div>
                Please use natural light as much as possible.Use flash light
                only for inner parts of the item.
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-12 text-white">
            {guidelineList.map((guide) => (
              <div key={guide.id}>
                <InstructionCard
                  title={getLocalisedField(guide, "title", locale)}
                  subtitle={getLocalisedField(guide, "subtitle", locale)}
                  imageUrls={guide.image_urls}
                />
              </div>
            ))}
          </div>
        </>
      )}
    </AppModal>
  );
};

export default ViewPhotoInstructionModal;

import Image from "next/image";
import React from "react";

const InstructionCard = ({
  title,
  subtitle,
  imageUrls,
}: {
  title: string;
  subtitle: string;
  imageUrls: string;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <div className="md:text-xl text-base font-bold">{title}</div>
      <div className="md:text-sm text-xs text-gray-100">{subtitle}</div>
      <div className="w-full rounded-xl overflow-hidden">
        <Image
          src={imageUrls}
          width={800}
          height={800}
          alt={title}
          className="w-full object-cover"
        />
      </div>
    </div>
  );
};

export default InstructionCard;

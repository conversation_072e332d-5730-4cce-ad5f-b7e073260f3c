import { GlobalOutlined } from "@ant-design/icons";
import { Dropdown } from "antd";
import type { MenuProps } from "antd";
import { APP_LOCALES } from "constants/app";
import { useRouter } from "next/router";
import React from "react";
import { getLocaleTitle } from "utils/locale";

const LanguageSwitcher = () => {
  const router = useRouter();
  const { route, asPath } = router;

  const handleLanguageMenuOnClick = (languageKey: string) => {
    router.push(route, asPath, { locale: languageKey, scroll: true });
  };

  const items: MenuProps["items"] = APP_LOCALES.map((key) => ({
    key: `app-language-menu-item-language-${key}`,
    label: (
      <div
        onClick={() => handleLanguageMenuOnClick(key)}
        role="presentation"
        className="hover:text-white text-gray-400 py-1 px-2"
      >
        {getLocaleTitle(key)}
      </div>
    ),
  }));

  return (
    <div className="p-2 cursor-pointer">
      <Dropdown
        menu={{
          items,
          style: {
            backgroundColor: "#1c1d23",
            marginTop: "0.6rem",
          },
        }}
        placement="bottomRight"
        trigger={["click"]}
      >
        <GlobalOutlined />
      </Dropdown>
    </div>
  );
};

export default LanguageSwitcher;

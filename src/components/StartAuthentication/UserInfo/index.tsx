import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";

import { setLoginRedirectPath } from "actions/app";
import { profilePath } from "../constant";
import useAppDispatch from "hooks/useAppDispatch";
import { PATH_ROUTE } from "constants/app";

const UserInfo = ({ user }: { user: any }) => {
  const router = useRouter();
  const { asPath } = router;
  const dispatch = useAppDispatch();
  return (
    <>
      {user ? (
        <Link
          href={profilePath}
          className="text-white flex gap-2 items-center cursor-pointer hover:bg-dark-100 rounded-full px-2"
        >
          <div className="rounded-full md:w-12 md:h-12 w-8 h-8 bg-dark-100 flex items-center justify-center">
            {user.name.charAt(0)}
          </div>
          <div className="hidden md:block">{user.name}</div>
        </Link>
      ) : (
        <div className="flex items-center gap-4 border border-gray-200 rounded-full md:px-4 py-1 px-1">
          <div
            onClick={() => {
              dispatch(setLoginRedirectPath({ loginRedirectPath: asPath }));
              router.push(PATH_ROUTE.LOGIN);
            }}
            className="cursor-pointer text-white font-medium md:px-2"
          >
            Log In
          </div>
          <div
            onClick={() => {
              dispatch(setLoginRedirectPath({ loginRedirectPath: asPath }));
              router.push(PATH_ROUTE.REGISTER);
            }}
            className="cursor-pointer bg-purple-600 text-white font-medium px-2 py-1 rounded-full hidden md:flex"
          >
            Sign Up
          </div>
        </div>
      )}
    </>
  );
};

export default UserInfo;

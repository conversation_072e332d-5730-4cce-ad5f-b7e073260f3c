import React, { useEffect, useCallback } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import Image from "next/image";

import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import { fetchItems } from "actions/caseHistory";
import { LIST_PAGESIZE_24 } from "constants/app";
import { RootState } from "reducers";
import { ICaseHistory } from "types/app";
import resizeImageUrl from "utils/resizeImageUrl";
import getImageUrl from "utils/imageUrl";
import AppListLoadMoreCard from "components/AppListLoadMoreCard";
import AppPlaceholder from "components/AppPlaceholder";
import { getErrorMessage } from "utils/error";
import { formatDate } from "utils/authHelper";
import AuthOrReplicaCard from "components/StartAuthentication/AuthOrReplicaCard";

const CaseHistory = () => {
  const intl = useIntl();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { locale = "", defaultLocale = "" } = router;

  const caseHistoryState = useAppSelector(
    (state: RootState) => state.caseHistory
  );

  const {
    items,
    isFetchItemsLoading,
    pagination = {},
    fetchItemsErrors,
  } = caseHistoryState || {};

  const { total } = pagination || {};
  const hasMore = items && total ? items.length < total : false;

  const handleLoadMore = useCallback(() => {
    if (isFetchItemsLoading || !hasMore) return;

    const currentItems = items || [];
    dispatch(
      fetchItems(
        {
          $offset: currentItems.length,
          $limit: LIST_PAGESIZE_24,
        },
        items
      )
    );
  }, [dispatch, items, isFetchItemsLoading, hasMore]);

  useEffect(() => {
    if (!items) {
      dispatch(
        fetchItems({
          $limit: LIST_PAGESIZE_24,
        })
      );
    }
  }, [dispatch, items]);

  return (
    <div className="flex flex-col gap-4">
      {/* header */}
      <div className="flex justify-between items-center">
        <div className="font-bold md:text-2xl text-xl">
          {intl.formatMessage({ id: "start_authentication_page_case_history" })}
        </div>
        <div className="text-gray-100 bg-gray-300 rounded-lg md:px-4 md:py-2 py-1 px-2 cursor-pointer md:text-base text-sm">
          {intl.formatMessage({
            id: "start_authentication_page_by_categories",
          })}
        </div>
      </div>
      {/* content */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {items &&
          items.map((item: ICaseHistory) => (
            <div
              key={item.id}
              className="border mb-4 md:rounded-xl rounded-md border-gray-200 overflow-hidden relative cursor-pointer"
            >
              <div className="w-full aspect-square">
                <Image
                  className="w-full h-full object-cover"
                  alt={item.product_title}
                  src={resizeImageUrl(getImageUrl(item.cover_image_url), {
                    width: 300,
                  })}
                  width={366}
                  height={366}
                />
              </div>
              <div className="flex items-center md:my-3 my-2 md:gap-4 gap-2 md:px-2 px-2">
                <div className="md:w-14 md:h-14 w-10 h-10">
                  <Image
                    alt={item.product_brand?.icon_image_url}
                    src={item.product_brand?.icon_image_url}
                    width={64}
                    height={64}
                  />
                </div>
                <div className="flex flex-col gap-0.5">
                  <div className="text-[10px] font-medium">
                    {item.product_title.split("-")[0]}
                  </div>
                  <div className="font-bold text-[10px]">
                    {item.product_title.split("-")[1]}
                  </div>
                  <div className="text-gray-100 text-[10px]">
                    {formatDate(locale, defaultLocale, item.completed_at)}
                  </div>
                </div>
              </div>
              <div className="absolute sm:top-3 sm:left-3 top-2 left-2">
                <AuthOrReplicaCard result={item.result} />
              </div>
            </div>
          ))}
      </div>
      {fetchItemsErrors && fetchItemsErrors.length > 0 && (
        <div>
          <AppPlaceholder
            iconType="exclamation-circle"
            title={getErrorMessage(fetchItemsErrors)}
          />
        </div>
      )}
      {items && items.length < total && (
        <div>
          <AppListLoadMoreCard
            onClick={handleLoadMore}
            loading={isFetchItemsLoading}
          />
        </div>
      )}
      {isFetchItemsLoading && !items && (
        <div className="flex flex-col items-center justify-center py-4 gap-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-100"></div>
          <div>loading...</div>
        </div>
      )}
    </div>
  );
};

export default CaseHistory;

import Image from "next/image";
import React from "react";
import { useIntl } from "react-intl";

const AdsBanner = () => {
  const intl = useIntl();

  return (
    <div className="grid grid-cols-2 gap-5">
      {/* AuthClass */}
      <div
        className="flex-1 relative cursor-pointer"
        onClick={() => {
          window.open("https://authclass.com", "_blank");
        }}
      >
        <Image
          className="w-full"
          src="/authClass_banner.png"
          alt="AuthClass"
          width={1038}
          height={834}
        />
        <div className="absolute left-2 top-6 xs:top-8 sm:left-5 sm:top-12 bg-white rounded-full sm:w-10 sm:h-10 w-6 h-6 flex justify-center items-center cursor-pointer">
          <Image
            src="/arrow-right-top.svg"
            alt="Arrow"
            className="sm:w-3 sm:h-3 w-2 h-2"
            width={12}
            height={12}
          />
        </div>
        <div className="absolute sm:left-5 sm:bottom-5 left-2 bottom-2 pr-2">
          <div className="text-xs sm:text-xl font-[800]">
            {intl.formatMessage({
              id: "start_authentication_page_learn_authentication",
            })}
          </div>
          <div className="text-[10px] sm:text-base font-[500]">
            {intl.formatMessage({
              id: "start_authentication_page_learn_authentication_desc",
            })}
          </div>
        </div>
      </div>
      {/* Business Plan */}
      <div className="flex-1 relative cursor-pointer">
        <Image
          className="w-full"
          src="/business_banner.png"
          alt="business_banner"
          width={1038}
          height={834}
        />
        <div className="absolute left-2 top-6 xs:top-8 sm:left-5 sm:top-12 bg-white rounded-full sm:w-10 sm:h-10 w-6 h-6 flex justify-center items-center cursor-pointer">
          <Image
            src="/arrow-right-top.svg"
            alt="Arrow"
            className="sm:w-3 sm:h-3 w-2 h-2"
            width={12}
            height={12}
          />
        </div>
        <div className="absolute sm:left-5 sm:bottom-5 left-2 bottom-2 pr-2">
          <div className="text-xs sm:text-xl font-[800]">
            {intl.formatMessage({
              id: "start_authentication_page_business_plan",
            })}
          </div>
          <div className="text-[10px] sm:text-base font-[500]">
            {intl.formatMessage({
              id: "start_authentication_page_business_plan_desc",
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdsBanner;

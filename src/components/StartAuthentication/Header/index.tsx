import Image from "next/image";
import React from "react";
import { useRouter } from "next/router";

import LeftPart from "../LeftPart";
import LeftPartDrawer from "../LeftPartDrawer";
import BottomMenu from "../BottomMenu";
import UserInfo from "../UserInfo";
import LanguageSwitcher from "../LanguageSwitcher";
import { chooseCategoryPath } from "../constant";
import useUserHook from "hooks/useUser";

const StartAuthenticationHeader = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const router = useRouter();
  const { user } = useUserHook();

  return (
    <div className="flex max-w-screen-lg m-auto text-white">
      {/* Left part */}
      <LeftPart className="hidden lg:flex" />

      {/* Right part */}
      <div className="flex-1 max-h-screen overflow-auto hide-scrollbar md:pb-0 pb-16">
        {/* right header */}
        <div className="flex md:justify-between lg:justify-end justify-end md:h-32 h-16 pr-4 md:pr-12 items-center">
          <LeftPartDrawer className="hidden md:flex lg:hidden" />
          <div className="flex items-center gap-4">
            <button
              className="flex items-center md:gap-4 gap-2 py-2 bg-btn-gradient rounded-full md:px-6 md:text-base text-xs px-2"
              onClick={() => {
                router.push(chooseCategoryPath);
              }}
            >
              <Image
                src="/OX.svg"
                alt="OX"
                width={30}
                height={13}
                className="w-[20px] h-[8px] md:w-[30px] md:h-[13px]"
              />
              <span className="text-white font-bold">Create New Order</span>
            </button>
            {/* log in out / user */}
            <UserInfo user={user} />
            {/* Language */}
            <LanguageSwitcher />
          </div>
        </div>
        {/* right content */}
        <div className="md:px-[44px] px-4">{children}</div>
      </div>

      {/* Bottom part */}
      <BottomMenu className="flex md:hidden" />
    </div>
  );
};

export default StartAuthenticationHeader;

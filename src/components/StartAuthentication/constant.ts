export const authenticationBasePath = "/portal";

export const homePath = `${authenticationBasePath}`;
export const walletPath = `${authenticationBasePath}/wallet`;
export const ordersPath = `${authenticationBasePath}/orders`;
export const profilePath = `${authenticationBasePath}/profile`;

export const chooseCategoryPath = `${authenticationBasePath}/choose-category`;
export const chooseBrandPath = `${authenticationBasePath}/choose-brand`;
export const chooseModalPath = `${authenticationBasePath}/choose-model`;
export const newAuthenticationPath = `${authenticationBasePath}/new-authentication`;
export const checkoutPath = `${authenticationBasePath}/checkout`;
export const orderDetailPath = `${walletPath}/buy-token-plan`;
export const authenticatorProfilePath = `${authenticationBasePath}/authenticator`;
export const getOrdersConversationPath = (id: string) =>
  `${ordersPath}/${id}/conversation`;

export const menuItems = [
  {
    title: "Home",
    href: homePath,
    icon: "/menu/home.svg",
  },
  {
    title: "Wallet",
    href: walletPath,
    icon: "/menu/wallet.svg",
  },
  {
    title: "Orders",
    href: ordersPath,
    icon: "/menu/orders.svg",
  },
  {
    title: "Profile",
    href: profilePath,
    icon: "/menu/profile.svg",
  },
];

export const DISCOUNT_TYPE = {
  ABSOLUTE: "absolute",
  RELATIVE: "relative",
};

export const ORDER_STATUS = {
  IN_PROGRESS: "checker_pending",
  COMPLETED: "completed",
  CANCELLED: "cancelled",
  USER_PENDING: "user_pending",
};

export const RISK_LEVELS = {
  VERY_LOW: "AI_SCORE_Very_Low_Risk",
  LOW: "AI_SCORE_Low_Risk",
  MODERATE: "AI_SCORE_Moderate_Risk",
  ELEVATED: "AI_SCORE_Elevated_Risk",
  HIGH: "AI_SCORE_High_Risk",
  VERY_HIGH: "AI_SCORE_Very_High_Risk",
  SIGNIFICANT: "AI_SCORE_Significant_Risk",
  MAJOR: "AI_SCORE_Major_Risk",
  SEVERE: "AI_SCORE_Severe_Risk",
  CRITICAL: "AI_SCORE_Critical_Risk",
  EXTREME: "AI_SCORE_Extreme_Risk",
};

export const RESULT_TYPE = {
  PASS: "pass",
  NOT_PASS: "not_pass",
  UNABLE_TO_VERIFY: "unable_to_verify",
};

export const SERVICE_REQUEST_IMAGE_TYPE = {
  INITIAL: "initial",
  ADDITIONAL: "additional",
};

export const SERVICE_REQUEST_ADDITIONAL_STATUS = {
  PENDING: "pending",
  COMPLETED: "completed",
  CANCELLED: "cancelled",
};

export const CATEGORY = {
  CODE_CHECKING: 8,
};

export const CONVERSATION_FORMAT_TYPE = {
  IMAGE: ["image/jpeg", "image/jpg", "image/png", "image/webp", "image/*"],
};

import React from "react";
import { useRouter } from "next/router";
import moment from "moment";
import clsx from "clsx";

import { IServiceRequestMessage } from "types/orders";
import { CONVERSATION_FORMAT_TYPE, ORDER_STATUS } from "../constant";

interface ConversationMessagesProps {
  messages: Partial<IServiceRequestMessage>[];
  locale: string;
  defaultLocale: string;
  user: any;
  id: string;
}

const ADMIN_ID = 28;
const ConversationMessages = ({
  messages,
  locale,
  defaultLocale,
  user,
  id,
}: ConversationMessagesProps) => {
  const router = useRouter();

  if (!messages || messages.length === 0) {
    return;
  }

  const groupedMessages = [...messages]
    .reverse()
    .reduce(
      (
        groups: { [key: string]: Partial<IServiceRequestMessage>[] },
        message
      ) => {
        const date = moment(message.created_at).format("YYYY-MM-DD");
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(message);
        return groups;
      },
      {}
    );

  const renderNameInfo = ({
    isUserMessage,
    message,
    user,
  }: {
    isUserMessage: boolean;
    message: Partial<IServiceRequestMessage>;
    user: any;
  }) => {
    if (isUserMessage) return `${user?.name}`;

    if (message?.checker_id === ADMIN_ID) {
      return `${message.checker?.name} (#${message.checker_id}) `;
    } else {
      return "LEGIT APP";
    }
  };
  return (
    <div className="space-y-4">
      {Object.entries(groupedMessages).map(([date, dayMessages]) => (
        <div key={date}>
          <div className="flex items-center justify-center mb-4">
            <div className="px-3 py-1 text-gray-500 font-semibold text-sm">
              {moment(date).format(
                locale === defaultLocale ? "D MMMM YYYY" : "YYYY年MM月DD日"
              )}
            </div>
          </div>

          <div className="space-y-4">
            {dayMessages.map((message) => {
              const isUserMessage = message.user_id === user?.id;
              const isDisabled =
                message.service_request_additional?.status !==
                ORDER_STATUS.USER_PENDING;

              return (
                <div
                  key={message.id}
                  className={clsx(
                    "flex items-start gap-3",
                    isUserMessage && "flex-row-reverse"
                  )}
                >
                  <div className="w-8 h-8 md:w-10 md:h-10 flex-shrink-0">
                    <img
                      src={
                        (isUserMessage
                          ? user.profile_image_url
                          : message?.checker?.profile_image_url) ||
                        "/order/icon_detail_result_overlay.png"
                      }
                      width={40}
                      height={40}
                      alt="avatar"
                      className="w-full h-full rounded-full object-cover"
                    />
                  </div>

                  {/* Message content */}
                  <div className="min-w-0 max-w-[80%] flex flex-col">
                    <div
                      className={clsx(
                        "flex items-center gap-2 mb-1",
                        isUserMessage ? "justify-end" : "justify-start"
                      )}
                    >
                      <div className="text-gray-500 font-semibold text-sm">
                        {renderNameInfo({ isUserMessage, message, user })}
                      </div>
                      <div className="text-gray-100 text-[10px]">
                        {moment(message.created_at).format("h:mm A")}
                      </div>
                    </div>

                    {message.type === "additional" ? (
                      <div
                        className={clsx(
                          "text-sm bg-dark-100 text-white rounded-2xl px-4 py-3 inline-block w-fit",
                          isUserMessage ? "self-end" : "self-start"
                        )}
                      >
                        <div className="flex gap-2 items-center">
                          <div>
                            <img
                              src="/order/icon_msg_image.png"
                              className="w-6 h-6"
                            />
                          </div>
                          <div>
                            <div>{message.content}</div>
                            <div className="text-gray-100 text-xs">
                              {message.service_request_additional?.status}
                            </div>
                          </div>
                        </div>
                        <button
                          disabled={isDisabled}
                          className={clsx(
                            "mt-2 w-full bg-gradient-red text-white px-4 rounded-sm font-medium transition-colors flex items-center justify-center gap-2",
                            isDisabled && "opacity-50 cursor-not-allowed"
                          )}
                          onClick={() => {
                            router.push(
                              `/portal/orders/${id}/additional-photos/${message.service_request_additional_id}`
                            );
                          }}
                        >
                          <img
                            src="/order/icon-upload.png"
                            className="w-8 h-8"
                            alt="upload icon"
                          />
                          UPLOAD PHOTOS
                        </button>
                      </div>
                    ) : CONVERSATION_FORMAT_TYPE.IMAGE.includes(
                        message.format || ""
                      ) ? (
                      <img
                        src={message.image_url!}
                        alt=""
                        className="w-full object-cover max-w-xs rounded-lg"
                      />
                    ) : (
                      <div
                        className={clsx(
                          "text-sm bg-dark-100 text-white rounded-2xl px-4 py-3 inline-block w-fit",
                          isUserMessage ? "self-end" : "self-start"
                        )}
                      >
                        {message.content}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ConversationMessages;

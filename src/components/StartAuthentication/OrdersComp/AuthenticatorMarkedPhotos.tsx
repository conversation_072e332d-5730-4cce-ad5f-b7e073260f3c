import React from "react";
import { Gallery, Item } from "react-photoswipe-gallery";

const AuthenticatorMarkedPhotos = ({
  markerImageUrl,
}: {
  markerImageUrl: string[];
}) => {
  return (
    <Gallery>
      <div className="grid grid-cols-4 gap-2">
        {markerImageUrl.map((url) => (
          <Item
            key={url}
            original={url}
            thumbnail={url}
            width={100}
            height={100}
          >
            {({ ref, open }) => (
              <div
                ref={ref}
                onClick={open}
                className="relative aspect-square cursor-pointer hover:opacity-90 transition-opacity"
              >
                <img
                  key={url}
                  src={url}
                  alt="marker"
                  className="w-full h-full object-cover"
                />
              </div>
            )}
          </Item>
        ))}
      </div>
    </Gallery>
  );
};

export default AuthenticatorMarkedPhotos;

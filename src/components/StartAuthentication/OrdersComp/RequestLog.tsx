import React from "react";
import { Timeline } from "antd";
import moment from "moment";

import { IServiceRequestLog } from "types/orders";

const RequestLog = ({
  serviceRequestLog,
}: {
  serviceRequestLog: IServiceRequestLog[];
}) => {
  return (
    <Timeline
      items={serviceRequestLog.map((log) => {
        const isEndAction = log.action_type === "request_end";

        return {
          dot: (
            <div
              className={`w-3 h-3 rounded-full border-2 ${
                isEndAction
                  ? "bg-red-500 border-red-500"
                  : "bg-gray-400 border-gray-400"
              }`}
            />
          ),
          children: (
            <div className="pb-4">
              <div className="flex gap-2">
                <div className="text-gray-100 w-12 text-xs">
                  {moment(log.updated_at).format("DD MMM HH:mm")}
                </div>
                <div className="text-white text-xs">
                  <div>{log.action_title}</div>
                  <div>{log.action_subtitle}</div>
                </div>
              </div>
            </div>
          ),
        };
      })}
    />
  );
};

export default RequestLog;

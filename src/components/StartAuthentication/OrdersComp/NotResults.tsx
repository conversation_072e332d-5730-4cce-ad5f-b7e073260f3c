import React from "react";
import { useIntl } from "react-intl";
import clsx from "clsx";

import { getAuthStatus, getResultTextColor } from "utils/authHelper";
import { ORDER_STATUS } from "../constant";

interface NotResultsProps {
  status?: string;
  uuid?: string;
  imgUrl?: string;
}

const NotResults = ({ status, uuid, imgUrl }: NotResultsProps) => {
  const intl = useIntl();

  return (
    <div className="flex flex-col items-center justify-center md:gap-2 gap-1 text-gray-100 md:text-base text-sm">
      <div className={clsx("font-bold", getResultTextColor("", status))}>
        {intl.formatMessage({ id: getAuthStatus({ status }) })}
      </div>
      <div>#{uuid}</div>

      {/* desc */}
      {status === ORDER_STATUS.IN_PROGRESS && (
        <div>Your results are on the way</div>
      )}
      {status === ORDER_STATUS.USER_PENDING && (
        <div className="text-center">
          Please upload additional photos to complete the authentication.
        </div>
      )}
      {/* image */}
      {imgUrl && (
        <div className="relative">
          <div className="w-24 h-32">
            <img src={imgUrl} className="w-full h-full object-contain" />
          </div>
          <div className="w-8 h-8 absolute bottom-1/4 left-1.5">
            <img src="/order/icon_detail_result_overlay.png" />
          </div>
        </div>
      )}
    </div>
  );
};

export default NotResults;

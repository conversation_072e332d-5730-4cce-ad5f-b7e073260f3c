import React from "react";
import { useIntl } from "react-intl";
import Image from "next/image";
import { useRouter } from "next/router";
import clsx from "clsx";

import {
  formatServiceTime,
  formatDate,
  getAuthStatus,
  getResultBgColor,
} from "utils/authHelper";
import { OrderItem } from "types/orders";
import { ORDER_STATUS, ordersPath } from "../constant";

const OrderCard = ({ order }: { order: OrderItem }) => {
  const intl = useIntl();
  const router = useRouter();
  const { locale = "", defaultLocale = "" } = router;

  return (
    <div
      className="border-b border-gray-200"
      onClick={() => {
        router.push(`${ordersPath}/${order.id}`);
      }}
    >
      <div
        className={clsx(
          "border-l",
          order.status === ORDER_STATUS.USER_PENDING
            ? "border-red-500"
            : "border-transparent"
        )}
      >
        <div className="flex gap-4 w-full md:py-4 py-2 cursor-pointer hover:bg-gray-300 px-3 rounded-lg">
          <div className="md:w-24 md:h-24 w-14 h-14">
            <Image
              src={order.cover_image_url}
              width={120}
              height={120}
              alt={order.product_title}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1 flex flex-col gap-2">
            <div className="flex justify-between items-center">
              <div className="text-gray-100 md:text-sm text-xs">
                #{order.uuid}
              </div>
              <div
                className={clsx(
                  "px-2 py-0.5 rounded-sm text-[10px] font-bold",
                  order.result && getResultBgColor(order.result),
                  order.status === ORDER_STATUS.USER_PENDING && "bg-yellow-500"
                )}
              >
                {intl.formatMessage({
                  id: getAuthStatus({
                    status: order.status,
                    categoryId: order.category_id,
                    result: order.result,
                  }),
                })}
              </div>
            </div>
            <div className="flex justify-between">
              <div className="font-bold md:text-base text-sm">
                {order.product_title.split("-")[0]}
              </div>
              <div className="md:text-base text-sm font-bold">
                &lt;{" "}
                {formatServiceTime(
                  order.service_level_minute,
                  intl.formatMessage({
                    id: "order_authentication_unit_minute",
                  }),
                  intl.formatMessage({
                    id: "order_authentication_unit_hour",
                  }),
                  locale === "en"
                )}
              </div>
            </div>
            <div className="font-bold md:text-base text-xs">
              {order.product_title.split("-")[1]}
            </div>
            <div className="text-gray-100 text-[10px]">
              <div>
                {intl.formatMessage({
                  id: "start_authentication_page_order_create_at",
                })}{" "}
                {formatDate(locale, defaultLocale, order.created_at)}
              </div>
              {order.result ? (
                <div>
                  {intl.formatMessage({
                    id: "start_authentication_page_order_complate_at",
                  })}{" "}
                  {formatDate(locale, defaultLocale, order.completed_at)}
                </div>
              ) : (
                <div>
                  {intl.formatMessage({
                    id: "start_authentication_page_order_update_at",
                  })}{" "}
                  {formatDate(locale, defaultLocale, order.updated_at)}
                </div>
              )}
            </div>
            {order.user_custom_code && (
              <div>
                <div className="bg-blue-200 text-[10px] p-1 rounded-md inline-block">
                  Custom Code {order.user_custom_code}
                </div>
              </div>
            )}
            {order.legit_tag_uuid && (
              <div>
                {order.category_id === 1 ? (
                  <div
                    className="text-[10px] p-1 rounded-md inline-block text-white"
                    style={{
                      backgroundImage: "url(/order/kicks_tag_label-bg.png)",
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                      backgroundRepeat: "no-repeat",
                    }}
                  >
                    KICKS Tag {order.legit_tag_uuid}
                  </div>
                ) : (
                  <div
                    className="text-[10px] p-1 rounded-md inline-block"
                    style={{
                      backgroundImage: "url(/order/luxe_tag_label-bg.png)",
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                      backgroundRepeat: "no-repeat",
                    }}
                  >
                    LUXE Tag {order.legit_tag_uuid}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderCard;

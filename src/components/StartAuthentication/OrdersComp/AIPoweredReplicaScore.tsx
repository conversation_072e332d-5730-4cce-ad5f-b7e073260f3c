import React from "react";
import { useIntl } from "react-intl";
import {
  getAIScoreImgFromNumber,
  getAIScoreKeyFromNumber,
} from "utils/authHelper";

interface AIPoweredReplicaScoreProps {
  fake_rating: number;
}

const AIPoweredReplicaScore = ({ fake_rating }: AIPoweredReplicaScoreProps) => {
  const intl = useIntl();

  return (
    <div className="bg-dark-100 rounded-md p-4">
      <div className="flex gap-6">
        <div className="md:space-y-4 space-y-2 flex flex-col items-center">
          <img src="/order/robot6.png" className="md:w-16 md:h-16 w-10 h-10" />
          <div className="text-xs">AI Analysis</div>
        </div>
        <div className="md:space-y-4 space-y-2 flex-1">
          <div className="flex gap-4 items-center md:text-4xl font-bold">
            <div className="bg-red-500 rounded-md flex justify-center items-center md:w-16 md:h-16 w-10 h-10">
              {fake_rating}
            </div>
            <div className="md:text-2xl">
              {intl.formatMessage({ id: getAIScoreKeyFromNumber(fake_rating) })}
            </div>
          </div>
          <div className="h-8">
            <img
              src={getAIScoreImgFromNumber(fake_rating)}
              className="h-full object-contain"
            />
          </div>
        </div>
      </div>
      <div className="text-gray-100 space-y-4 md:text-sm text-xs">
        <div>
          This replica score indicates the difficulty of checking your item
          based on risk levels. The higher the number, the harder it was to
          determine its authenticity. Our AI analyzes the submitted photos and
          compares them to a massive database of similar items.
        </div>
        <div>
          Beyond the AI analysis, every case goes through authentication process
          by at least 2 professional authenticators for final guaranteed
          results.
        </div>
      </div>
    </div>
  );
};

export default AIPoweredReplicaScore;

import clsx from "clsx";
import React from "react";
import { IServiceExtraService } from "types/orders";
import { getLocalisedField } from "utils/locale";

const NFTCertificate = ({
  uuid,
  locale,
  NFTCertificateSnapshot,
  isCertReady,
}: {
  uuid: string | undefined;
  locale: string;
  NFTCertificateSnapshot: IServiceExtraService;
  isCertReady: boolean;
}) => {
  return (
    <div>
      <div className="text-gray-100 md:text-sm text-xs">
        {getLocalisedField(NFTCertificateSnapshot, "description", locale)}
      </div>
      <div
        className={clsx(
          "md:max-w-md m-auto mt-4 flex items-center justify-center gap-2 bg-gradient-blue md:h-12 h-10 rounded-lg",
          isCertReady
            ? "opacity-100 cursor-pointer hover:opacity-90 transition-opacity"
            : "opacity-50 cursor-not-allowed"
        )}
        onClick={() => {
          if (!isCertReady || !uuid) return;

          window.open(
            `https://opensea.io/item/matic/0x3bb83e704de6c1c6370232e75b3da15f3398d75d/${uuid}`
          );
        }}
      >
        <div className="md:w-6 md:h-6 w-4 h-4">
          <img src="/order/nft-cert.png" className="w-full h-full" />
        </div>
        <button className="text-white font-semibold text-xs sm:text-sm">
          View
        </button>
      </div>
    </div>
  );
};

export default NFTCertificate;

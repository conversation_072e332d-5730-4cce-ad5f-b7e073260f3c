import clsx from "clsx";

const OrderDetailSectionContainer = ({
  children,
  title,
  isNeedBorder = true,
  rightContent,
  isShowOptional = false,
}: {
  children: React.ReactNode;
  title?: string;
  isNeedBorder?: boolean;
  rightContent?: React.ReactNode;
  isShowOptional?: boolean;
}) => (
  <div
    className={clsx(
      " border-dark-100 md:pb-8 pb-4",
      isNeedBorder && "border-b"
    )}
  >
    <div className="md:mx-[44px] px-4">
      {title && (
        <div className="font-bold my-4 flex justify-between items-center">
          <div>
            {title}
            {isShowOptional && (
              <span className="text-gray-100 ml-2">(optional)</span>
            )}
          </div>
          {rightContent && (
            <div className="flex justify-end">{rightContent}</div>
          )}
        </div>
      )}
      <div>{children}</div>
    </div>
  </div>
);

export default OrderDetailSectionContainer;

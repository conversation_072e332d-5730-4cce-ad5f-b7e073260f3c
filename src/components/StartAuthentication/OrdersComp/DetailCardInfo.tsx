import React from "react";
import QRCode from "react-qr-code";
import Image from "next/image";
import { useRouter } from "next/router";

import AuthOrReplicaCard from "../AuthOrReplicaCard";
import { formatDate, formatServiceTime } from "utils/authHelper";
import { useIntl } from "react-intl";

const DetailCardInfo = ({
  brandIconUrl,
  modelIconUrl,
  brandTitle,
  modelTitle,
  status,
  result,
  categoryId,
  createdAt,
  serviceLevelMinute = 0,
  completedAt,
  uuid,
}: Partial<{
  brandIconUrl: string;
  modelIconUrl: string;
  brandTitle: string;
  modelTitle: string;
  status: string;
  result: "pass" | "not_pass";
  categoryId: number;
  createdAt: string;
  serviceLevelMinute: number;
  completedAt: string;
  uuid: string;
}>) => {
  const intl = useIntl();
  const router = useRouter();
  const { locale = "", defaultLocale = "" } = router;
  return (
    <div className="w-full mt-16 md:mt-24 bg-gradient-dark-blue rounded-xl flex flex-col justify-center items-center relative md:pt-24 pt-14 md:pb-9 pb-4">
      <div className="flex justify-between gap-1 absolute top-0 transform -translate-y-1/2">
        {brandIconUrl && (
          <div className="md:w-36 md:h-36 w-24 h-24">
            <Image
              src={brandIconUrl}
              width={150}
              height={150}
              alt={brandTitle || "Brand"}
            />
          </div>
        )}

        {modelIconUrl && (
          <div className="md:w-36 md:h-36 w-24 h-24">
            <Image
              src={modelIconUrl}
              width={150}
              height={150}
              alt={modelTitle || "Model"}
            />
          </div>
        )}
      </div>
      <div className="text-center flex flex-col gap-1">
        <div className="md:text-xl text-base font-bold">
          {brandTitle || "-"}
        </div>
        <div className="md:text-xl text-base font-bold">
          {modelTitle || "-"}
        </div>
      </div>
      {result ? (
        <div className="w-full flex flex-col gap-2 md:gap-4 text-center">
          <div className="relative h-12 md:h-20">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10/12 m-auto border-b border-dashed h-2"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-28 md:w-40">
              <Image
                src="/legit_app_logo.svg"
                alt="LEGIT APP LOGO"
                width={176}
                height={48}
                className="object-contain"
              />
            </div>
          </div>
          <div className="w-1/4 m-auto">
            <AuthOrReplicaCard
              status={status}
              result={result}
              categoryId={categoryId}
              isShowIcon={false}
              className="flex justify-center rounded-md py-2"
            />
          </div>
          <div>
            <div className="md:text-base text-sm">#{uuid}</div>
            {completedAt && (
              <div className="md:text-base text-sm">
                Completed on {formatDate(locale, defaultLocale, completedAt)}
              </div>
            )}
          </div>
          {categoryId !== 8 && (
            <div className="flex justify-center items-center flex-col gap-2">
              <div className="p-1 bg-white rounded-lg">
                <QRCode value={`https://legitapp.com/cert/${uuid}`} size={70} />
              </div>
              <div className="text-xs text-gray-100">
                Scan QR code to view the certificate.
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="w-full flex flex-col md:gap-4 gap-2 text-center">
          <div className="flex justify-center">-</div>
          <div className="md:mx-20 mx-4 flex justify-between text-sm md:text-base">
            <div className="md:space-y-1">
              <div className="text-gray-100 md:text-sm text-[10px]">
                Created At
              </div>
              {createdAt && (
                <div className="md:text-base text-xs">
                  {formatDate(locale, defaultLocale, createdAt)}
                </div>
              )}
            </div>
            <div className="md:space-y-1">
              <div className="text-gray-100 md:text-sm text-[10px]">
                Service Type
              </div>
              <div className="md:text-base text-xs">
                &lt;{" "}
                {formatServiceTime(
                  serviceLevelMinute,
                  intl.formatMessage({
                    id: "order_authentication_unit_minute",
                  }),
                  intl.formatMessage({
                    id: "order_authentication_unit_hour",
                  }),
                  locale === "en"
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DetailCardInfo;

import React, { useState } from "react";
import router, { useRouter } from "next/router";
import moment from "moment";

import { IServiceRequestAdditional } from "types/orders";
import apiCore from "utils/apiCore";
import { ordersPath } from "../constant";
import { showErrorPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import { AppConfirmModal } from "components/AppModal";

const AdditionalPhotos = ({
  id,
  accessToken,
  serviceRequestAdditionalPending,
}: {
  id: string;
  accessToken: string;
  serviceRequestAdditionalPending: IServiceRequestAdditional[];
}) => {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleCancel = (additionalId: number) => {
    setIsModalOpen(true);
  };

  const handleConfirmCancel = async () => {
    if (!id) return;

    setLoading(true);
    try {
      await apiCore.patch(
        null,
        `v1/service_request/${id}/cancel`,
        {},
        accessToken
      );
      router.push(ordersPath);
    } catch (error) {
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitAdditionalPhotos = (additionalId: number) => {
    router.push(`/portal/orders/${id}/additional-photos/${additionalId}`);
  };
  return (
    <>
      <div className="space-y-6">
        {serviceRequestAdditionalPending.map((additional) => (
          <div key={additional.id} className="rounded-lg p-4 space-y-2">
            <div className="flex justify-between items-start">
              <div className="text-red-500 font-bold">Additional Photos</div>
              <div className="text-right text-xs">
                <div className="capitalize">{additional.status}</div>
                <div>
                  {moment(additional.created_at).format("DD MMM YYYY, h:mm A")}
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="text-gray-100 text-sm">
              Please follow the instructions to continue the authentication
              process.
            </div>

            {/* Action buttons */}
            <div className="space-y-3">
              <div className="md:max-w-md m-auto mt-6">
                <button
                  onClick={() => handleSubmitAdditionalPhotos(additional.id)}
                  className="w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                >
                  <img
                    src="/order/icon-upload.png"
                    className="w-8 h-8"
                    alt="upload icon"
                  />
                  SUBMIT ADDITIONAL PHOTOS
                </button>
              </div>

              <div className="text-gray-100 text-sm">
                Cancel this authentication request and get a $LEGIT refund.
              </div>

              <div className="md:max-w-md m-auto mt-6">
                <button
                  onClick={() => handleCancel(additional.id)}
                  className="w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                >
                  <img
                    src="/order/icon-clear.png"
                    className="w-8 h-8"
                    alt="cancel request icon"
                  />
                  CANCEL REQUEST
                </button>
              </div>
            </div>

            {/* Additional remark */}
            {additional.checker_additional_remark && (
              <div className="bg-dark-100 rounded-lg p-3">
                <div className="text-gray-400 text-sm font-medium mb-2">
                  Additional Instructions:
                </div>
                <div className="text-gray-300 text-sm">
                  {additional.checker_additional_remark}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      <AppConfirmModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        title="Confirm to cancel"
        content="Confirm to cancel the authentication request"
        onConfirm={handleConfirmCancel}
        loading={loading}
      />
    </>
  );
};

export default AdditionalPhotos;

import Image from "next/image";
import React from "react";
import { ICurrencyRate } from "types/app";
import { getLocalisedField } from "utils/locale";

const CurrencyRateCard = ({
  currency,
  locale,
  onSelected,
  currentSelectedCurrency,
}: {
  currency: ICurrencyRate;
  locale: string;
  onSelected: (selectedCurrency: ICurrencyRate) => void;
  currentSelectedCurrency: ICurrencyRate | null;
}) => {
  return (
    <div
      className="border-b border-gray-300 text-white"
      onClick={() => onSelected(currency)}
    >
      <div className="cursor-pointer py-4 px-4 flex justify-between items-center rounded-xl hover:bg-gray-300">
        <div className="flex gap-4">
          <div>
            <Image
              src={currency.icon_image_url}
              alt={currency.currency_title}
              width={48}
              height={48}
            />
          </div>
          <div>
            <div className="text-gray-100">{currency.currency_code}</div>
            <div>{getLocalisedField(currency, "currency_title", locale)}</div>
          </div>
        </div>
        <div>
          {currentSelectedCurrency?.id === currency.id && (
            <Image
              src={"/icon_green_tick.png"}
              alt={"check"}
              width={24}
              height={24}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default CurrencyRateCard;

import { Drawer } from "antd";
import React, { useState } from "react";
import clsx from "clsx";
import { MenuOutlined } from "@ant-design/icons";
import LeftPart from "../LeftPart";

const LeftPartDrawer = ({ className }: { className?: string }) => {
  const [open, setOpen] = useState(false);
  return (
    <div className={clsx("h-32 flex items-center pl-12", className)}>
      <div
        className="flex p-3 text-white bg-dark-100 rounded-full cursor-pointer"
        onClick={() => setOpen(true)}
      >
        <MenuOutlined />
      </div>
      <Drawer
        title={null}
        placement={"left"}
        closable={false}
        onClose={() => setOpen(false)}
        open={open}
        key={"123"}
        className="!bg-black"
        width={280}
        styles={{
          body: { padding: 0 },
        }}
      >
        <LeftPart />
      </Drawer>
    </div>
  );
};

export default LeftPartDrawer;

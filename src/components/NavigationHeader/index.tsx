import React from "react";
import clsx from "clsx";
import { useRouter } from "next/router";
import Image from "next/image";

import { APP_NAME } from "constants/app";
import { homePath } from "components/StartAuthentication/constant";

interface NavigationHeaderProps {
  className?: string;
  onBackClick?: () => void;
  onCloseClick?: () => void;
  backText?: string;
  closeText?: string;
  hideBackButton?: boolean;
  hideCloseButton?: boolean;
  progress?: string;
}

const NavigationHeader: React.FC<NavigationHeaderProps> = ({
  className,
  onBackClick,
  onCloseClick,
  backText = "Back",
  closeText = "Close",
  hideBackButton = false,
  hideCloseButton = false,
  progress = "0%",
}) => {
  const router = useRouter();
  return (
    <>
      <header
        className={clsx(
          "bg-black md:h-32 h-20 w-full flex justify-center items-center relative",
          className
        )}
      >
        <div className="w-full flex items-center justify-between md:px-12 px-4">
          <div className="flex items-center">
            {!hideBackButton && (
              <button
                className="bg-dark-100 cursor-pointer rounded-[65px] font-bold flex items-center md:py-3 py-2 md:px-10 px-4 md:gap-4 gap-2 hover:opacity-80"
                onClick={onBackClick ? onBackClick : () => router.back()}
                aria-label="Go back"
              >
                <Image
                  src="/arrow-back.svg"
                  alt="Back"
                  width={24}
                  height={24}
                  className="md:w-6 md:h-6 w-4 h-4"
                />
                <div className="md:text-base text-sm">{backText}</div>
              </button>
            )}
          </div>

          <div className="absolute left-1/2 transform -translate-x-1/2 flex justify-center items-center">
            <Image
              className="md:h-12 h-8"
              src="/legit_app_logo.svg"
              alt={APP_NAME}
              width={176}
              height={48}
            />
          </div>

          <div className="flex items-center">
            {!hideCloseButton && (
              <button
                className="border border-white bg-dark-100 cursor-pointer rounded-[65px] font-bold flex items-center md:py-3 py-2 md:px-10 px-4 md:gap-4 gap-2 hover:opacity-80"
                onClick={
                  onCloseClick ? onCloseClick : () => router.push(homePath)
                }
                aria-label="Close"
              >
                <Image
                  src="/close.svg"
                  alt="Close"
                  width={24}
                  height={24}
                  className="md:w-6 md:h-6 w-4 h-4"
                />
                <div className="md:text-base text-sm">{closeText}</div>
              </button>
            )}
          </div>
        </div>
      </header>
      <div className="w-full bg-gray-200 h-2">
        <div className="bg-red-100 h-2" style={{ width: progress }}></div>
      </div>
    </>
  );
};

export default NavigationHeader;

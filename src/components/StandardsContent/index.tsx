import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useIntl } from 'react-intl'
import css from './StandardsContent.module.scss'

const StandardsContent = () => {
    const intl = useIntl()

    return (
        <AppContainer className={css.StandardsContent}>
            <HomePageSectionHeader
                className={css.sectionHeader}
                title={intl.formatMessage({ id: 'standards_page_title' })}
                subtitle={intl.formatMessage({ id: 'standards_page_subtitle' })}
                description={<>
                    {intl.formatMessage({ id: 'standards_page_description' })}
                </>}
            />
            <div className={css.storyCardGrid}>
                <div className={css.informationCard}>
                    <div className={clsx(css.resultHeader, css.authentic)}>
                        <h2 className={css.resultTitle}>
                            {intl.formatMessage({ id: 'app_meta_data_authentication_result_authentic_title' })}
                        </h2>
                    </div>
                    <div className={css.informationPart}>
                        <h3 className={css.title}>
                            {intl.formatMessage({ id: 'standards_page_authentic_title' })}
                        </h3>
                        <h4 className={css.subtitle}>
                            {intl.formatMessage({ id: 'standards_page_authentic_subtitle' })}
                        </h4>
                        <div className={css.description}>
                            {intl.formatMessage({ id: 'standards_page_authentic_description' })}
                        </div>
                    </div>
                </div>
                <div className={css.informationCard}>
                    <div className={clsx(css.resultHeader, css.replica)}>
                        <h2 className={css.resultTitle}>
                            {intl.formatMessage({ id: 'app_meta_data_authentication_result_replica_title' })}
                        </h2>
                    </div>
                    <div className={css.informationPart}>
                        <h3 className={css.title}>
                            {intl.formatMessage({ id: 'standards_page_replica_title' })}
                        </h3>
                        <h4 className={css.subtitle}>
                            {intl.formatMessage({ id: 'standards_page_replica_subtitle' })}
                        </h4>
                        <div className={css.description}>
                            {intl.formatMessage({ id: 'standards_page_replica_description' })}
                        </div>
                    </div>
                </div>
            </div>
            <div className={css.informationCard}>
                <div className={clsx(css.resultHeader, css.inconclusive)}>
                    <h2 className={css.resultTitle}>
                        {intl.formatMessage({ id: 'app_meta_data_authentication_result_inconclusive_title' })}
                    </h2>
                </div>
                <div className={css.informationPart}>
                    <div className={css.description}>
                        {intl.formatMessage({ id: 'standards_page_inconclusive_description' })}
                    </div>
                </div>
            </div>
        </AppContainer>
    )
}

export default StandardsContent

.StandardsContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 48px;

    @include responsive('md') {
        margin-bottom: 96px;
    }

    .sectionHeader {
        padding: 24px 0 48px 0;

        @include responsive('md') {
            padding: 48px 0 48px 0;
        }
    }

    .coverImage {
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        border-radius: $border-radius-theme-2;
        width: 100%;
        padding-bottom: 50%;
        margin-bottom: 28px;

        @include responsive('md') {
            padding-bottom: 30%;
            margin-bottom: 48px;
        }
    }


    .storyCardGrid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        column-gap: 24px;
        row-gap: 24px;
        margin-bottom: 24px;

        @include responsive('md') {
            grid-template-columns: repeat(2, 1fr);
            column-gap: 48px;
            row-gap: 48px;
            margin-bottom: 48px;
        }
    }

    .informationCard {
        display: flex;
        flex-direction: column;
        border: 1px solid $color-separator-white-1;
        color: $color-app-white;
        border-radius: $border-radius-theme-2;
        overflow: hidden;
        background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

        .resultHeader {
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;

            &.authentic {
                background: linear-gradient(121deg, #5977FF 0%, #31FFD7 100%);
            }

            &.replica {
                background: linear-gradient(121deg, #FF5F46 0%, #FF1E60 100%);
            }

            &.inconclusive {
                background: $color-app-dark;
            }

            .resultTitle {
                padding: 6px 12px;
                border-radius: 3px;
                color: #fff;
                text-transform: uppercase;
                font-size: 24px;
                line-height: 28px;
                font-weight: 900;
                margin-right: 16px;
                margin-left: 16px;
            }
        }

        .informationPart {
            padding: 24px;
            display: flex;
            flex-direction: column;
            // align-items: center;
            row-gap: 12px;
            min-height: 130px;

            @include responsive('md') {
                padding: 40px;
            }

            .title {
                font-weight: bold;
                font-size: 16px;
                line-height: 26px;

                @include responsive('md') {
                    font-size: 20px;
                    line-height: 30px;
                }
            }

            .subtitle {
                width: 100%;
                opacity: 0.7;
                font-weight: bold;
                margin-bottom: 3px;
                font-size: 14px;
                line-height: 26px;

                @include responsive('md') {
                    font-size: 16px;
                    line-height: 26px;
                }
            }

            .description {
                opacity: 0.6;
                font-size: 14px;
                line-height: 24px;
                white-space: pre-line;

                @include responsive('md') {
                    font-size: 16px;
                    line-height: 26px;
                }
            }

            .linkedinButton {
                margin-top: 12px;
                cursor: pointer;
                padding: 10px 12px;
                background-color: $color-linkedin;
                border-radius: 4px;
                font-weight: bold;
                text-align: center;
                color: $color-app-white;
                display: flex;
                align-items: center;
                justify-content: center;
                width: fit-content;
                column-gap: 6px;

                img {
                    width: 20px;
                }

                column-gap: 12px;
                width: 100%;
                font-size: 14px;

                @include responsive('md') {
                    width: fit-content;
                    font-size: 16px;
                }
            }
        }

        .coverImage {
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
            width: 100%;
            padding-bottom: 50%;
        }
    }
}
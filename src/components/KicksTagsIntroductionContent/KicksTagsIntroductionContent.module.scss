.videoPlayerContainer {
  margin: 24px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.videoPlayerPart {
  background-color: #000;
  width: 100%;
  max-width: 800px;
  border: 1px solid $color-separator-white-1;
  border-radius: 20px;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  border-radius: $border-radius-theme-1;
  overflow: hidden;
  margin: 24px;
  // box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 1);
  // margin-bottom: 48px;

  @include responsive('md') {
    // padding: unset;
    // min-width: 600px;
    // width: 100%;
    // max-width: 1000px;
    // margin-bottom: 48px;
  }


  .playerWrapper {
    width: 100%;
    // height: 200px;
    padding-top: 56.25%;
    position: relative;
    // align-items: center;
    // justify-content: center;
    // display: none;

    @include responsive('md') {
      // display: flex;
      // width: 100%;
    }

    .videoPlayer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      >iframe {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.KicksTagsIntroductionContent {

  :global {
    .tutorial-section {
      background: rgb(0, 0, 0);
      background: linear-gradient(356deg, rgba(0, 0, 0, 1) 0%, rgba(72, 148, 230, 1) 33%, rgba(0, 41, 85, 1) 66%, rgba(0, 0, 0, 1) 100%);
      // padding: 24px;
      padding: 0;
      overflow-y: hidden;
      display: flex;
      align-items: center;
      justify-content: center;

      // border-top: 1px solid #000;
      .container {
        display: flex;
        width: 100%;
        flex-direction: column;
        justify-content: center;
        padding: 0;

        .section-header {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-items: center;
          padding: 12px 0px;
          margin-bottom: 12px;

          .section-header-title-container {
            width: 100%;
            padding: 24px 36px;
            max-width: 500px;
            background-image: url('/illustration-tags-title-underline.png');
            background-position: 10px 32px;
            background-repeat: no-repeat;
            background-size: 295px 30px;
            height: 91px;

            img {
              width: 100%;
            }
          }


          .section-header-subtitle {
            width: 100%;
            color: #fff;
            font-size: 16px;
            text-align: center;
            opacity: 0.7;
            // text-transform: uppercase;
          }
        }

        .section-content {
          display: flex;
          align-items: center;
          justify-content: center;

          .tutorial-card-part {
            width: 100%;
            max-width: 1000px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .tutorial-card-seperator {
              width: 100%;
              text-align: center;
              margin: 50px 0;
              display: flex;
              flex-direction: column;
              align-items: center;

              @include responsive('md') {
                margin: 0;
              }

              .tag-separator-image {
                width: 20px;

                @include responsive('md') {
                  width: 30px;
                }
              }

              .tag-separator-image-2 {
                width: 50px;
              }
            }

            .tutorial-card {
              display: flex;
              flex-direction: column;
              align-items: center;
              border-radius: 5px;
              padding: 12px 24px;
              transition: all 0.25s ease-in;
              text-align: center;
              margin-bottom: 12px;
              max-width: 500px;

              @include responsive('md') {
                margin-bottom: 24px;
                padding: 24px;
                height: 100%;

                &:hover {
                  transform: translate(0px, -5px);
                }
              }

              img.download-button {
                margin: 0 6px 24px 6px;
                height: 40px;
                border: 1px solid #fff;
                border-radius: 7px;
                overflow: hidden;

                @include responsive('md') {
                  height: 50px;
                }
              }

              .card-image,
              .card-image-3 {
                max-width: 100%;
                max-height: 500px;
                // margin-bottom: 24px;
              }

              .card-image-1 {
                z-index: 999;
                // max-width: 130%;
              }

              .card-image-2 {
                padding: 0 36px;
                width: 100%;
                margin-bottom: 36px;
              }

              .card-image-4 {
                width: 100%;
                margin-bottom: 24px;
              }

              .card-content-1 {
                max-width: 500px;
                border-radius: 12px;
                background-image: url('/illustration-tags-1-content-background.png');
                padding: 24px;
                background-position: center;
                background-repeat: no-repeat;
                background-size: auto;
                margin: -48px 24px 0 24px;
              }

              .card-content {
                text-align: left;

                .card-title {
                  font-size: 18px;
                  line-height: 22px;
                  font-weight: 600;
                  margin-bottom: 12px;
                  color: #fff;
                }

                .card-title-1 {
                  font-size: 34px;
                  margin-bottom: 24px;
                  opacity: 1;
                  line-height: 35px;
                }

                .card-section-2-title {
                  font-size: 34px;
                  line-height: 35px;
                  font-weight: 600;
                  margin-bottom: 12px;
                  color: #fff;
                }

                .card-subtitle {
                  font-weight: 300;
                  font-size: 14px;
                  color: #fff;
                  opacity: 0.7;
                  margin-bottom: 24px;

                  @include responsive('md') {
                    font-size: 16px;
                    max-width: 500px;
                  }
                }

                .card-subtitle-1 {
                  margin-bottom: 0;
                  opacity: 0.9;
                }
              }
            }

            .tutorial-card-no-padding {
              padding: 0;
            }

            .card-5 {
              padding-top: 40px;
              background-image: url('/illustration-tags-orderlist-1.png');
              background-repeat: no-repeat;
              background-size: 100px;
            }

            .card-6 {
              padding-top: 50px;
              background-image: url('/illustration-tags-orderlist-2.png');
              background-repeat: no-repeat;
              background-size: 140px;
            }


            .card-7 {
              padding-top: 60px;
              background-image: url('/illustration-tags-orderlist-3.png');
              background-repeat: no-repeat;
              background-size: 160px;
            }


            .card-8 {
              padding-top: 50px;
              background-image: url('/illustration-tags-orderlist-4.png');
              background-repeat: no-repeat;
              background-size: 170px;
            }


            .card-9 {
              padding-top: 40px;
              background-image: url('/illustration-tags-orderlist-5.png');
              background-repeat: no-repeat;
              background-size: 160px;
            }

            .card-with-background {
              margin: 0 24px;
            }

            .tutorial-card-two-columns {
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              width: 100%;
              max-width: 500px;

              .tutorial-card {
                display: flex;
                flex-direction: column;
                flex-basis: 100%;
                flex: 1;
              }
            }
          }

        }
      }
    }
  }
}
import css from './AppVideoBackground.module.scss'

const AppVideoBackground = (props: any) => {
    const src = props?.src
    if (!src) {
        return null
    }
    return (
        <video
            className={css.AppVideoBackground}
            autoPlay
            loop
            muted
            poster={props?.poster}
            playsInline
        >
            <source src={src} type="video/mp4" />
        </video>
    )
}

export default AppVideoBackground
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import { APP_DISCORD_URL, APP_FACEBOOK_URL, APP_INSTAGRAM_URL, APP_LINKEDIN_URL, APP_NAME, APP_TIKTOK_URL, APP_TWITTER_URL } from 'constants/app'
import _ from 'lodash'
import moment from 'moment'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import css from './AppFooter.module.scss'

type AppFooterProps = {
    className?: string
}

const AppFooter = ({
    className,
}: AppFooterProps) => {
    const router = useRouter()
    const intl = useIntl()
    const { locale, asPath, route } = router

    return (
        <footer className={clsx(css.AppFooter, className)}>
            <AppContainer>
                <div className={css.sitemapSection}>
                    <div className={css.sitemapSectionContainer}>
                        <div className={css.sitemapCategoryContainer}>
                            <div className={css.sitemapCategoryHeader}>
                                <Link href='/'>
                                    <img
                                        title={APP_NAME}
                                        className={css.appLogo}
                                        src='/logo-app-full.svg'
                                        alt={APP_NAME}
                                    />
                                </Link>
                            </div>
                            <div className={css.sitemapCategoryLinkContainer}>
                                <div className={css.appDescription}>
                                    {_.startCase(intl.formatMessage({ id: 'app_footer_app_tagline' }))}
                                </div>
                            </div>
                        </div>
                        <div className={css.sitemapCategoryContainer}>
                            <div className={css.sitemapCategoryHeader}>
                                {intl.formatMessage({ id: 'app_footer_authentication_section_title' })}
                            </div>
                            <div className={css.sitemapCategoryLinkContainer}>
                                <Link href='/products'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'how_it_works_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/products/app-authentication'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_section_title' })}
                                    </div>
                                </Link>
                                <Link href='/products/api-authentication'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_section_title' })}
                                    </div>
                                </Link>
                                <Link href='/products/authclass-courses'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'authclass_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/what-we-authenticate'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'what_we_authenticate_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/search-certificate'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'search_certificate_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/pricing'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'pricing_page_title' })}
                                    </div>
                                </Link>
                                <Link href={'/standards'} className={clsx(css.sitemapLink)}>
                                    {intl.formatMessage({ id: 'standards_page_link' })}
                                </Link>
                                <Link href='/financial-guarantee'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'financial_guarantee_page_title_footer' })}
                                    </div>
                                </Link>
                                <Link href='/faq'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'faq_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/customers'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'customers_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/luxe-tags'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'lux_tags_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/kicks-tags'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'tags_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/legit-token'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'legit_token_page_title' })}
                                    </div>
                                </Link>
                            </div>
                        </div>
                        <div className={css.sitemapCategoryContainer}>
                            <div className={css.sitemapCategoryHeader}>
                                {intl.formatMessage({ id: 'app_footer_company_section_title' })}
                            </div>
                            <div className={css.sitemapCategoryLinkContainer}>
                                <Link href='/about'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'about_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/blog'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'blog_page_title' })}
                                    </div>
                                </Link>
                                <Link href='/contact'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'contact_page_subtitle' })}
                                    </div>
                                </Link>
                                <Link href='/terms'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_about_section_item_company_term' })}
                                    </div>
                                </Link>
                                <Link href='/privacy'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_about_section_item_company_privacy' })}
                                    </div>
                                </Link>
                            </div>
                        </div>
                        <div className={css.sitemapCategoryContainer}>
                            <div className={css.sitemapCategoryHeader}>
                                {intl.formatMessage({ id: 'app_footer_resources_section_title' })}
                            </div>
                            <div className={css.sitemapCategoryLinkContainer}>
                                {/* <a href={'mailto:<EMAIL>'} target='_top'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_resources_section_item_email' })}
                                    </div>
                                </a> */}
                                <a href={APP_LINKEDIN_URL} target='_blank' rel='noopener noreferrer'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_social_section_item_linkedin' })}
                                    </div>
                                </a>
                                <a href={APP_INSTAGRAM_URL} target='_blank' rel='noopener noreferrer'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_social_section_item_instagram' })}
                                    </div>
                                </a>
                                <a href={APP_DISCORD_URL} target='_blank' rel='noopener noreferrer'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_social_section_item_discord' })}
                                    </div>
                                </a>
                                <a href={APP_TWITTER_URL} target='_blank' rel='noopener noreferrer'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_social_section_item_twitter' })}
                                    </div>
                                </a>
                                <a href={APP_FACEBOOK_URL} target='_blank' rel='noopener noreferrer'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_social_section_item_facebook' })}
                                    </div>
                                </a>
                                <a href={APP_TIKTOK_URL} target='_blank' rel='noopener noreferrer'>
                                    <div className={css.sitemapLink}>
                                        {intl.formatMessage({ id: 'app_footer_social_section_item_tiktok' })}
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </AppContainer>
            <AppContainer>
                <div className={css.companyInformationSection}>
                    <div className={css.leftPart}>
                        <img
                            className={css.companyLogo}
                            src='/logo-legit-app.png'
                            alt={intl.formatMessage({ id: `app_title` })}
                        />
                        <div className={css.companyInformation}>
                            <span className={css.copyright}>
                                {intl.formatMessage({ id: 'app_footer_website_info_copyright' }, { year: moment().utc().format('YYYY') })}
                            </span>
                        </div>
                    </div>
                    <div className={css.rightPart}>
                        <div className={css.companyInformation}>
                            <span className={css.disclaimer}>
                                {intl.formatMessage({ id: 'app_footer_website_info_disclaimer' })}
                            </span>
                        </div>
                        {/* <Link href='/terms'>
                            <div className={css.sitemapLink}>
                                Terms of Service
                            </div>
                        </Link>
                        <Link href='/privacy'>
                            <div className={css.sitemapLink}>
                                Privacy Policy
                            </div>
                        </Link> */}
                    </div>
                </div>
            </AppContainer>
        </footer>
    )
}
export default AppFooter

import clsx from "clsx";
import React from "react";
import { Gallery, Item } from "react-photoswipe-gallery";

import { IServiceRequestImage } from "types/orders";

const GalleryImage = ({
  imageList,
  className,
}: {
  imageList: IServiceRequestImage[];
  className?: string;
}) => {
  return (
    <Gallery>
      <div className={clsx("grid grid-cols-4 gap-2", className ?? "")}>
        {imageList.map((image) => (
          <Item
            key={image.id}
            original={image.image_url}
            thumbnail={image.image_url}
            width={image.width}
            height={image.height}
          >
            {({ ref, open }) => (
              <div
                ref={ref}
                onClick={open}
                className="relative aspect-square cursor-pointer hover:opacity-90 transition-opacity"
              >
                <img
                  src={image.image_url}
                  className="w-full h-full rounded-lg object-cover"
                />
                <img
                  src="/OX.svg"
                  alt="OX icon"
                  className="absolute inset-0 object-contain w-full h-full"
                />
              </div>
            )}
          </Item>
        ))}
      </div>
    </Gallery>
  );
};

export default GalleryImage;

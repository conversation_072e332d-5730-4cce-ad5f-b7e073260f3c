import { LoadingOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import css from "./ListLoadMoreCard.module.scss"
import { useIntl } from 'react-intl'

const ListLoadMoreCard = (props: any) => {
  const {
    onClick,
    loading,
    className
  } = props
  const intl = useIntl()
  return (
    <div className={css.listLoadMoreCardContainer}>
      <div
        className={clsx(css.ListLoadMoreCard, className, loading ? 'loading' : '')}
        role='presentation'
        onClick={loading ? null : onClick}
      >
        {
          loading
            ? <LoadingOutlined />
            : intl.formatMessage({ id: 'list_load_more_card_title' })
        }
      </div>
    </div>
  )
}

export default ListLoadMoreCard

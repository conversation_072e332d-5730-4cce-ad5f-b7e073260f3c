.listLoadMoreCardContainer {
  display: flex;
  align-items: center;
  justify-content: center;

  .ListLoadMoreCard {
		transition: all 0.25s ease-in;
    cursor: pointer;
    font-size: 16px;
    color: $color-app-white;
    // border: 1px solid $color-separator;
    padding: 12px 24px;
    font-size: 12px;
    border-radius: 25px;
    transition: 0.3s;
    text-align: center;
    font-weight: bold;
    text-transform: uppercase;
    flex-shrink: 0;
    letter-spacing: 2px;
    margin-bottom: 48px;

    &:hover {
			transition: all 0.25s ease-in;
      background: $color-app-white;
      color: #000;
    }

    @include responsive('md') {
      min-width: 500px;
    }

    &:global {
      &.loading {
        cursor: default;
        // &:hover {
        // }
      }
    }
  }
}
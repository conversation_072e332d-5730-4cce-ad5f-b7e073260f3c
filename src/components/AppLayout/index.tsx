import clsx from 'clsx'
import { useRouter } from 'next/router'
import css from './AppLayout.module.scss'

const AppLayout = (props: any = {}) => {
    const {
        className,
        children,
        disableHeader = false,
    } = props

    const router = useRouter()

    // const dispatch = useAppDispatch()
    // const appState: any = ({} = useAppSelector((state: RootState) => state.app))
    // const {
    //   accessToken,
    //   user,
    // } = appState
    // const router = useRouter()

    // useEffect(() => {
    //   if (accessToken && !user) {
    //     dispatch(fetchUser({
    //       accessToken
    //     }))
    //   }
    // }, [])

    return (
        <div className={clsx(css.AppLayout, className, disableHeader ? '' : css.withHeader)}>
            {children}
        </div>
    )
}
export default AppLayout

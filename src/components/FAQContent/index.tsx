import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useState } from 'react'
import { useIntl } from 'react-intl'
import css from './FAQContent.module.scss'

const FAQContent = () => {
    const intl = useIntl()
    const [faqCardOpenStatusMap, setFaqCardOpenStatusMap] = useState({} as any)

    const FAQ_ITEM_LIST_APP = [
        // about legit app
        {
            question: `faq_item_1_question`,
            answer: `faq_item_1_answer`,
        },
        {
            question: `faq_item_2_question`,
            answer: `faq_item_2_answer`,
        },
        {
            question: `faq_item_3_question`,
            answer: `faq_item_3_answer`,
        },
        {
            question: `faq_item_4_question`,
            answer: `faq_item_4_answer`,
        },
        {
            question: `faq_item_5_question`,
            answer: `faq_item_5_answer`,
        },
        {
            question: `faq_item_6_question`,
            answer: `faq_item_6_answer`,
        },
    ]

    const FAQ_ITEM_LIST_PAYMENT = [
        // about token
        {
            question: `faq_item_7_question`,
            answer: `faq_item_7_answer`,
        },
        {
            question: `faq_item_8_question`,
            answer: `faq_item_8_answer`,
        },
        {
            question: `faq_item_9_question`,
            answer: `faq_item_9_answer`,
        },
        {
            question: `faq_item_10_question`,
            answer: `faq_item_10_answer`,
        },
        {
            question: `faq_item_11_question`,
            answer: `faq_item_11_answer`,
        },
        {
            question: `faq_item_12_question`,
            answer: `faq_item_12_answer`,
        },
        {
            question: `faq_item_13_question`,
            answer: `faq_item_13_answer`,
        },
        {
            question: `faq_item_14_question`,
            answer: `faq_item_14_answer`,
        },
    ]

    const FAQ_ITEM_LIST_AUTHENTICATION = [
        // about result
        {
            question: `faq_item_15_question`,
            answer: `faq_item_15_answer`,
        },
        {
            question: `faq_item_16_question`,
            answer: `faq_item_16_answer`,
        },
        {
            question: `faq_item_17_question`,
            answer: `faq_item_17_answer`,
        },
        {
            question: `faq_item_18_question`,
            answer: `faq_item_18_answer`,
        },
        {
            question: `faq_item_19_question`,
            answer: `faq_item_19_answer`,
        },
        {
            question: `faq_item_20_question`,
            answer: `faq_item_20_answer`,
        },
        {
            question: `faq_item_21_question`,
            answer: `faq_item_21_answer`,
        },


        // about submission
        {
            question: `faq_item_22_question`,
            answer: `faq_item_22_answer`,
        },
        {
            question: `faq_item_23_question`,
            answer: `faq_item_23_answer`,
        },
        {
            question: `faq_item_24_question`,
            answer: `faq_item_24_answer`,
        },
        {
            question: `faq_item_25_question`,
            answer: `faq_item_25_answer`,
        },
        {
            question: `faq_item_26_question`,
            answer: `faq_item_26_answer`,
        },

        // about additional photos
        {
            question: `faq_item_27_question`,
            answer: `faq_item_27_answer`,
        },
        {
            question: `faq_item_28_question`,
            answer: `faq_item_28_answer`,
        },
        {
            question: `faq_item_29_question`,
            answer: `faq_item_29_answer`,
        },
        {
            question: `faq_item_30_question`,
            answer: `faq_item_30_answer`,
        },
    ]

    const faqCardQuestionOnClick = (question: string) => {
        setFaqCardOpenStatusMap({
            ...faqCardOpenStatusMap,
            [question]: !faqCardOpenStatusMap[question],
        })
    }

    return (
        <AppContainer className={css.FAQContent}>
            <HomePageSectionHeader
                className={css.sectionHeader}
                subtitle={intl.formatMessage({ id: 'faq_page_faq_section_subtitle' })}
                title={intl.formatMessage({ id: 'faq_page_faq_section_title' })}
                description={<>
                    {intl.formatMessage({ id: 'faq_page_faq_section_description' })}
                </>}
            />
            <div className={css.faqCardGrid}>
                {
                    FAQ_ITEM_LIST_APP
                        .filter((faqItem: any) => !!faqItem.question)
                        .map((faqItem: any) => (
                            <div
                                className={clsx(css.faqCard, faqCardOpenStatusMap[faqItem.question] ? css.open : '')}
                                key={`faq-page-faq-card-${faqItem.question}`}
                            >
                                <div
                                    className={css.questionPart}
                                    onClick={() => faqCardQuestionOnClick(faqItem.question)}
                                >
                                    {intl.formatMessage({ id: faqItem.question })}
                                </div>
                                <div className={css.answerContainer}>
                                    <div className={css.answer}>
                                        {intl.formatMessage({ id: faqItem.answer })}
                                    </div>
                                </div>
                            </div>
                        ))
                }
            </div>
            <div className={css.faqSectionHeader}>
                {intl.formatMessage({ id: 'faq_page_faq_section_type_payment' })}
            </div>
            <div className={css.faqCardGrid}>
                {
                    FAQ_ITEM_LIST_PAYMENT
                        .filter((faqItem: any) => !!faqItem.question)
                        .map((faqItem: any) => (
                            <div
                                className={clsx(css.faqCard, faqCardOpenStatusMap[faqItem.question] ? css.open : '')}
                                key={`faq-page-faq-card-${faqItem.question}`}
                            >
                                <div
                                    className={css.questionPart}
                                    onClick={() => faqCardQuestionOnClick(faqItem.question)}
                                >
                                    {intl.formatMessage({ id: faqItem.question })}
                                </div>
                                <div className={css.answerContainer}>
                                    <div className={css.answer}>
                                        {intl.formatMessage({ id: faqItem.answer })}
                                    </div>
                                </div>
                            </div>
                        ))
                }
            </div>
            <div className={css.faqSectionHeader}>
                {intl.formatMessage({ id: 'faq_page_faq_section_type_authentication' })}
            </div>
            <div className={css.faqCardGrid}>
                {
                    FAQ_ITEM_LIST_AUTHENTICATION
                        .filter((faqItem: any) => !!faqItem.question)
                        .map((faqItem: any) => (
                            <div
                                className={clsx(css.faqCard, faqCardOpenStatusMap[faqItem.question] ? css.open : '')}
                                key={`faq-page-faq-card-${faqItem.question}`}
                            >
                                <div
                                    className={css.questionPart}
                                    onClick={() => faqCardQuestionOnClick(faqItem.question)}
                                >
                                    {intl.formatMessage({ id: faqItem.question })}
                                </div>
                                <div className={css.answerContainer}>
                                    <div className={css.answer}>
                                        {intl.formatMessage({ id: faqItem.answer })}
                                    </div>
                                </div>
                            </div>
                        ))
                }
            </div>
        </AppContainer>
    )
}

export default FAQContent

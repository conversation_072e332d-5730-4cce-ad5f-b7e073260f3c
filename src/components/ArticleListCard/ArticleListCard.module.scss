.ArticleListCard {
  display: grid;
  grid-template-columns: 1fr;
  align-items: flex-start;
  border-radius: 4px;
  overflow: hidden;
  column-gap: 24px;
  row-gap: 24px;
  height: 100%;

  @include responsive ('md') {
    grid-template-columns: 2fr 3fr;
  }

  .cardImage {
    width: 100%;
    border: 1px solid $color-separator-white-2;
    border-radius: $border-radius-theme-2;
    overflow: hidden;

    img {
      width: 100%;
    }
  }

  .cardContent {
    overflow: hidden;
    height: 100%;
    // @include responsive('md') {
    //   height: 200px;
    // }

    .subtitle {
      opacity: 0.7;
      color: #fff;
      font-weight: bold;
      margin-bottom: 6px;
      font-size: 14px;
    }

    .title {
      color: #fff;
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 6px;

      @include responsive('md') {
        margin-bottom: 12px;
        font-size: 20px;
        line-height: 28px;
      }
    }

    .highlight {
      color: #fff;
      opacity: 0.6;
      font-size: 12px;
      line-height: 18px;
      margin-bottom: 6px;

      @include responsive('md') {
        font-size: 16px;
        line-height: 26px;
        margin-bottom: 12px;
      }
    }

    .date {
      font-size: 10px;
      color: #fff;
      text-transform: uppercase;
      font-weight: 300;
      opacity: 0.8;

      @include responsive('md') {
        font-size: 12px;
      }
    }
  }
}
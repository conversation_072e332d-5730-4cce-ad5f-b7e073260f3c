import moment from 'moment'
import Link from 'next/link'
import { useRouter } from 'next/router'
import getImageUrl from 'utils/imageUrl'
import { getLocalisedField } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './ArticleListCard.module.scss'

const ArticleListCard = (props: any) => {
  const {
    item,
  } = props
  const router = useRouter()
  const { locale = '' } = router

  if (!item) {
    return null
  }

  const date = moment(item.published_at || item.created_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).local().format('YYYY-MM-DD HH:mm')
  const articleSlug = item.slug

  return (
    <div>
      <Link href={`/blog/${articleSlug}`}>
        <div className={css.ArticleListCard}>
          <div className={css.cardImage}>
            <img src={resizeImageUrl(getImageUrl(item.cover_image_url), { width: 600 })} alt={item.title} />
          </div>
          <div className={css.cardContent}>
            {
              !!item?.subtitle && (
                <div className={css.subtitle}>
                  {item.subtitle}
                </div>
              )
            }
            <div className={css.title}>
              {getLocalisedField(item, 'title', locale)}
            </div>
            {
              !!item?.highlight && (
                <div className={css.highlight}>
                  {/* {item.highlight.slice(0, 140)}... */}
                  {item.highlight}
                </div>
              )
            }
            <div className={css.date}>
              {date}
            </div>
          </div>
        </div>
      </Link>
    </div>
  )
}

export default ArticleListCard

.AppBreadcrumb {
  color: $color-app-white;

  :global {
    .ant-breadcrumb {
      a {
        color: $color-app-white;
        transition: all 0.25s ease-in;
        font-size: 12px;
        opacity: 0.7;

        @include responsive('md') {
          font-size: 14px;
        }

        &:hover {
          opacity: 1;
        }
      }

      // margin-bottom: 24px;
      color: $color-app-white;
      color: $color-app-white;

      .ant-breadcrumb-separator {
        color: $color-app-white;
        opacity: 0.7;
      }
    }

    .ant-breadcrumb-link {
      color: $color-app-white;
      font-size: 12px;

      @include responsive('md') {
        font-size: 14px;
      }

      a {
        color: $color-app-white;

        &:hover {
          color: $color-app-white;
        }
      }
    }
  }
}
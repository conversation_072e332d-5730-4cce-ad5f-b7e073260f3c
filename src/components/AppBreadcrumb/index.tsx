import { type ReactNode } from "react";
import Link from "next/link";
import { Breadcrumb } from "antd";
import clsx from "clsx";
import css from "./AppBreadcrumb.module.scss";

type AppBreadcrumbItem = {
  title: ReactNode;
};

type AppBreadcrumbProps = {
  className?: string;
  items?: AppBreadcrumbItem[];
};

const AppBreadcrumb = ({ items, className }: AppBreadcrumbProps) => {
  if (!items) {
    return null;
  }
  return (
    <div className={clsx(css.AppBreadcrumb, className)}>
      <Breadcrumb
        separator='/'
        className={css.itemBreadcrumb}
        items={[
          // {
          //   title: <Link href="/">Home</Link>,
          // },
          ...items,
        ]}
      />
    </div>
  );
};

export default AppBreadcrumb;

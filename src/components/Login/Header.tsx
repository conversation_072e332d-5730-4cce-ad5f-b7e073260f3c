import React from "react";
import Image from "next/image";
import Link from "next/link";
import { RootState } from "reducers";
import { CloseOutlined } from "@ant-design/icons";

import { homePath } from "components/StartAuthentication/constant";
import useAppSelector from "hooks/useAppSelector";
import { useRouter } from "next/router";

const Header = ({ toPath }: { toPath?: string }) => {
  const router = useRouter();
  const appState: any = ({} = useAppSelector((state: RootState) => state.app));

  const { loginRedirectPath = "" } = appState || {};
  return (
    <div className="max-w-screen-xl flex justify-between items-center w-full sm:px-12 sm:h-[105px] px-6 h-[60px] border-b border-gray-200">
      <Link
        href={homePath}
        className="cursor-pointer sm:w-[176px] sm:h-[48px] w-[117px] h-[24px]"
      >
        <Image src="/legit_app_logo.svg" alt="logo" width={176} height={48} />
      </Link>
      <div
        className="cursor-pointer"
        onClick={() => {
          router.push(toPath ? toPath : loginRedirectPath || homePath);
        }}
      >
        <CloseOutlined />
      </div>
    </div>
  );
};

export default Header;

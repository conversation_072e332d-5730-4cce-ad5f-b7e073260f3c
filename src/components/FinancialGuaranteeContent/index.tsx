import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useIntl } from 'react-intl'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './FinancialGuaranteeContent.module.scss'

const FinancialGuaranteeContent = () => {

    const intl = useIntl()
    const FAQ_ITEM_LIST = [
        {
            question: intl.formatMessage({ id: 'protection_plus_page_item_1_question' }),
            answer: `${intl.formatMessage({ id: 'protection_plus_page_item_1_answer' })}`,
        },
        {
            question: intl.formatMessage({ id: 'protection_plus_page_item_2_question' }),
            answer: intl.formatMessage({ id: 'protection_plus_page_item_2_answer' }),
        },
        {
            question: intl.formatMessage({ id: 'protection_plus_page_item_3_question' }),
            answer: intl.formatMessage({ id: 'protection_plus_page_item_3_answer' }),
        },
        {
            question: intl.formatMessage({ id: 'protection_plus_page_item_4_question' }),
            answer: intl.formatMessage({ id: 'protection_plus_page_item_4_answer' }),
        },
    ]

    return (
        <AppContainer className={css.FinancialGuaranteeContent}>
            <HomePageSectionHeader
                className={css.sectionHeader}
                subtitle={intl.formatMessage({ id: 'financial_guarantee_page_subtitle' })}
                title={intl.formatMessage({ id: 'financial_guarantee_page_title' })}
            description={<>
              {intl.formatMessage({ id: 'financial_guarantee_page_description' })}
            </>}
            />
            <div className={css.coverImage}>
                <img src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-protection-plus.png`, { width: 1000 })} />
            </div>
            <div className={css.faqCardGrid}>
                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <div
                        className={css.questionPart}
                    >
                        {intl.formatMessage({ id: 'financial_guarantee_page_s1_title' })}
                    </div>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            {intl.formatMessage({ id: 'financial_guarantee_page_s1_description' })}
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <div
                        className={css.questionPart}
                    >
                        {intl.formatMessage({ id: 'financial_guarantee_page_s2_title' })}
                    </div>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            {intl.formatMessage({ id: 'financial_guarantee_page_s2_description' })}
                            <img src={`https://legitapp-static.oss-accelerate.aliyuncs.com/illustration-financial-guarantee.png`} />
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <div
                        className={css.questionPart}
                    >
                        {intl.formatMessage({ id: 'financial_guarantee_page_s2_p1_title' })}
                    </div>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            {intl.formatMessage({ id: 'financial_guarantee_page_s2_p1_content' })}
                            <ol>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p1_p1' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p1_p2' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p1_p3' })}</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <div
                        className={css.questionPart}
                    >
                        {intl.formatMessage({ id: 'financial_guarantee_page_s2_p2_title' })}
                    </div>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            {intl.formatMessage({ id: 'financial_guarantee_page_s2_p2_content' })}
                            <img src={`https://legitapp-static.oss-accelerate.aliyuncs.com/illustration-financial-guarantee-2.png`} />
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <div
                        className={css.questionPart}
                    >
                        {intl.formatMessage({ id: 'financial_guarantee_page_s2_p3_title' })}
                    </div>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p3_p1_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p3_p1_p1' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p3_p1_p2' })}</li>
                            </ul>
                            <br />
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p3_p2_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p3_p2_p1' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p3_p2_p2' })}</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <div
                        className={css.questionPart}
                    >
                        {intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_title' })}
                    </div>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            <div className={css.table}>
                                <table className={css.refundExampleTable}>
                                    <tbody>
                                        <tr>
                                            <th></th>
                                            <th>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_p1_title' })}</th>
                                            <th>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_p2_title' })}</th>
                                        </tr>
                                        <tr>
                                            <td width={'16%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_r1_title' })}
                                            </td>
                                            <td width={'42%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_p1_p1' })}
                                            </td>
                                            <td width={'42%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_p2_p1' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width={'16%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_r2_title' })}
                                            </td>
                                            <td width={'42%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_p1_p2' })}
                                            </td>
                                            <td width={'42%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p4_p2_p2' })}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <div
                        className={css.questionPart}
                    >
                        {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_title' })}
                    </div>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            <div className={css.table}>
                                <table className={css.refundExampleTable}>
                                    <tbody>
                                        <tr>
                                            <th>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col1_title' })}
                                                <br />
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col1_subtitle' })}
                                            </th>
                                            <th>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col2_title' })}
                                                <br />
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col2_subtitle' })}
                                            </th>
                                        </tr>
                                        <tr>
                                            <td width={'50%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col1_r1' })}
                                            </td>
                                            <td width={'50%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col2_r1' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width={'50%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col1_r2' })}
                                            </td>
                                            <td width={'50%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col2_r2' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width={'50%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col1_r3' })}
                                            </td>
                                            <td width={'50%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col2_r3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width={'50%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col1_r4' })}
                                            </td>
                                            <td width={'50%'}>
                                                {intl.formatMessage({ id: 'financial_guarantee_page_s2_p5_col2_r4' })}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <div
                        className={css.questionPart}
                    >
                        {intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_title' })}
                    </div>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p1_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p1_p1' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p1_p2' })}</li>
                            </ul>
                            <br />
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p2_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p2_p1' })}</li>
                            </ul>
                            <br />
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p3_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p3_p1' })}</li>
                            </ul>
                            <br />
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p4_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p4_p1' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p4_p2' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p4_p3' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p4_p4' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p4_p5' })}</li>
                            </ul>
                            <br />
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p5_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p5_p1' })}</li>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p5_p2' })}</li>
                            </ul>
                            <br />
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p6_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p6_p1' })}</li>
                            </ul>
                            <br />
                            <b>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p7_title' })}</b>
                            <ul>
                                <li>{intl.formatMessage({ id: 'financial_guarantee_page_s2_p6_p7_p1' })}</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div
                    className={css.disclaimer}
                >
                    {intl.formatMessage({ id: `financial_guarantee_page_disclaimer` })}
                </div>

            </div>
        </AppContainer>
    )
}

export default FinancialGuaranteeContent

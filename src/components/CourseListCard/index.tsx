import moment from 'moment'
import Link from 'next/link'
import { useRouter } from 'next/router'
import getImageUrl from 'utils/imageUrl'
import { getLocalisedField } from 'utils/locale'
import css from './CourseListCard.module.scss'
import clsx from 'clsx'
import resizeImageUrl from 'utils/resizeImageUrl'

const CourseListCard = (props: any) => {
  const {
    item,
  } = props
  const router = useRouter()
  const { locale = '' } = router

  if (!item) {
    return null
  }

  const date = moment(item.published_at || item.created_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).local().format('ll')
  const courseSlug = item.slug

  // console.log('item', item)

  const getCourseStatusLabel = () => {
    if (item.status === 'live') {
      return (
        <div className={css.statusLabel}>
          <div className={css.greenLight} />Live Now
        </div>
      )
    }
    if (item.status === 'coming') {
      return (
        <div className={clsx(css.statusLabel, css.coming)}>
          Coming Soon
        </div>
      )
    }
    return null
  }

  const getCourseDescription = () => {
    if (!item.lesson_count) {
      return null
    }
    return (
      <div className={css.description}>
        {item.lesson_count} Sessions
        {/* &bull; {item.lesson_length} */}
      </div>
    )
  }

  return (
    <div>
      <a href={`https://authclass.com/courses/${item.slug}`} target='_blank' rel='noopener noreferrer'>
        <div className={css.CourseListCard}>
          <div
            className={css.cardImage}
            style={{
              backgroundImage: `url(${resizeImageUrl(getImageUrl(item.cell_cover_image_url), { width: 500 })})`,
            }}
          >
            <div className={css.overlayGradient} />
          </div>
          <div className={css.cardContent}>
            <div className={css.brandInformation}>
              <div
                className={css.brandLogo}
                style={{
                  backgroundImage: `url(${resizeImageUrl(getImageUrl(item.brand_logo_image_url), { width: 100 })})`,
                }}
              />
              {/* <div className={css.brandTitle}>
                {item.brand_name}
              </div> */}
            </div>
            <div className={css.courseInformation}>
              <div className={css.title}>
                {getLocalisedField(item, 'title', locale)}
                <br />
                {getLocalisedField(item, 'subtitle', locale)}
              </div>
              <div className={css.description}>
                {getCourseDescription()}
                {/* &bull; {item.lesson_length} */}
              </div>
              {getCourseStatusLabel()}
            </div>
            {/* {
              !!item?.highlight && (
                <div className={css.highlight}>
                  {item.highlight}
                </div>
              )
            } */}
            {/* <div className={css.description}>
              {item.description}
            </div> */}
          </div>
        </div>
      </a>
    </div>
  )
}

export default CourseListCard

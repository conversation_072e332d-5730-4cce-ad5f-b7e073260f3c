.CourseListCard {
  display: flex;
  flex-direction: column;
  width: 100%;
  // grid-template-columns: 1fr;
  // align-items: stretch;
  // justify-content: stretch;
  border-radius: 4px;
  overflow: hidden;
  column-gap: 24px;
  row-gap: 24px;
  height: 100%;
  border: 1px solid $color-separator-white-1;
  border-radius: $border-radius-theme-2;
  min-height: 350px;
  // padding: 12px;
  position: relative;
  // padding-bottom: 0%;

  @include responsive ('md') {
    // grid-template-columns: 2fr 3fr;
    min-height: 500px;

    &:hover {
      .cardImage {
        transform: scale(1.03);
      }
    }
  }

  .statusLabel {
    // position: absolute;
    // right: 0;
    color: $color-app-gray-900;
    margin: 12px;
    padding: 4px 12px;
    background-color: $color-app-white;
    font-size: 12px;
    border-radius: $border-radius-theme-2;
    display: flex;
    align-items: center;
    column-gap: 8px;

    .greenLight {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: $color-app-green;
    }

    &.coming {
      background-color: $color-app-gray-500;
      color: $color-app-gray-800;
    }
  }

  .cardImage {
    position: absolute;
    width: 100%;
    height: 100%;
    // padding-bottom: 60%;
    // border: 1px solid $color-separator-white-2;
    border-radius: $border-radius-theme-1;
    overflow: hidden;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    transition: all 0.25s ease-in;

    .overlayGradient {
      position: absolute;
      width: 100%;
      height: 100%;
      background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 1));
      opacity: 0.9;
    }
  }

  .cardContent {
    overflow: hidden;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: space-between;
    width: 100%;
    // @include responsive('md') {
    //   height: 200px;
    // }

    .brandInformation {
      display: flex;
      align-items: center;
      column-gap: 6px;
      // margin-bottom: 12px;
      padding: 12px;

      .brandLogo {
        width: 60px;
        height: 60px;
        // background-color: $color-separator-white-1;
        // border: 1px solid $color-separator-white-1;
        border-radius: $border-radius-theme-1;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        // background-color: $color-app-gray-900;

        @include responsive('md') {
          width: 100px;
          height: 100px;
        }
      }
    }

    .courseInformation {
      color: $color-app-white;
      padding: 24px;
      padding-bottom: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;

      .title {
        // color: #fff;
        font-weight: bold;
        font-size: 20px;
        line-height: 28px;
        margin-bottom: 6px;
        // font-family: Kalice;
        text-align: center;
        max-width: 300px;

        @include responsive('md') {
          margin-bottom: 12px;
          font-size: 20px;
          line-height: 28px;
        }
      }

      .description {
        font-size: 10px;
        font-weight: 300;
        color: $color-app-gray-400;

        @include responsive('md') {
          font-size: 12px;
        }
      }
    }

    .highlight {
      font-size: 10px;
      font-weight: 300;
      opacity: 0.8;

      @include responsive('md') {
        font-size: 12px;
      }
    }
  }
}
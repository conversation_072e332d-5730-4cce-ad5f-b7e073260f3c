import { useEffect, useState } from 'react'
import DesktopHeader from './DesktopHeader'
import ResponsiveHeader from './ResponsiveHeader'
// import ResponsiveHeader from './ResponsiveHeader'

type HeaderProps = {
  className?: string
  transparent?: boolean
}

const Header = ({
  className,
  transparent = true,
}: HeaderProps) => {

  const [scrollY, setScrollY] = useState(0)
  const logit = () => {
    setScrollY(window.scrollY);
  }

  useEffect(() => {
    const watchScroll = () => {
      window.addEventListener("scroll", logit);
    }
    watchScroll();
    // Remove listener (like componentWillUnmount)
    return () => {
      window.removeEventListener("scroll", logit)
    }
  }, [])

  const getTransparent = () => {
    if (transparent) {
      return scrollY > 100 ? false : transparent
    }
    return false
  }

  return (
    <>
      <DesktopHeader transparent={getTransparent()} />
      <ResponsiveHeader transparent={getTransparent()} />
    </>
  )
}
export default Header

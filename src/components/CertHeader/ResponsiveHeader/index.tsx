import { GlobalOutlined } from '@ant-design/icons'
import { Dropdown } from 'antd'
import clsx from 'clsx'
import { APP_LOCALES, APP_NAME } from 'constants/app'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { getLocaleTitle } from 'utils/locale'
import css from './ResponsiveHeader.module.scss'

type ResponsiveHeaderProps = {
    className?: string
    transparent?: boolean
}

const ResponsiveHeader = ({
    className,
    transparent = true,
}: ResponsiveHeaderProps) => {
    const router = useRouter()
    const { locale, asPath, route } = router
    const [menuHidden, setMenuHidden] = useState(true)

    const handleLanguageMenuOnClick = (languageKey: any) => {
        router.push(route, asPath, { locale: languageKey, scroll: true })
    }

    const aboutMenu = (
        <div className={css.dropdownRenderContent}>
            <div className={css.dropdownMenu}>
                <div className={clsx(css.dropdownMenuItem)}>
                    Testing
                </div>
                <div className={clsx(css.dropdownMenuItem)}>
                    Testing
                </div>
                <div className={clsx(css.dropdownMenuItem)}>
                    Testing
                </div>
            </div>
        </div >
    )

    const languageMenu = (
        <div className={css.dropdownRenderContent}>
            <div className={css.dropdownMenu}>
                {
                    APP_LOCALES.map((key) => (
                        <div
                            className={clsx(css.dropdownMenuItem)}
                            key={`app-language-menu-item-language-${key}`}
                            onClick={() => handleLanguageMenuOnClick(key)}
                            role='presentation'
                        >
                            {getLocaleTitle(key)}
                        </div>
                    ))
                }
            </div>
        </div >
    )

    return (
        <header className={clsx(css.ResponsiveHeader, className)}>
            <div className={css.headerContainer}>
                <div className={css.leftPart}>
                    <Link href='/'>
                        <img
                            title={APP_NAME}
                            className={css.appLogo}
                            src='/logo-app-full.svg'
                            alt={APP_NAME}
                        />
                    </Link>
                </div>
                <div className={css.rightPart}>
                    <Dropdown
                        dropdownRender={() => languageMenu}
                    >
                        <div className={clsx(css.languageMenuLink)}>
                            {/* {getLocaleTitle(locale)} */}
                            <GlobalOutlined />
                        </div>
                    </Dropdown>
                </div>
            </div>
        </header>
    )
}
export default ResponsiveHeader

import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useIntl } from 'react-intl'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './ProtectionPlusContent.module.scss'

const ProtectionPlusContent = () => {

    const intl = useIntl()
    const FAQ_ITEM_LIST = [
        {
            question: intl.formatMessage({ id: 'protection_plus_page_item_1_question' }),
            answer: `${intl.formatMessage({ id: 'protection_plus_page_item_1_answer' })}`,
        },
        {
            question: intl.formatMessage({ id: 'protection_plus_page_item_2_question' }),
            answer: intl.formatMessage({ id: 'protection_plus_page_item_2_answer' }),
        },
        {
            question: intl.formatMessage({ id: 'protection_plus_page_item_3_question' }),
            answer: intl.formatMessage({ id: 'protection_plus_page_item_3_answer' }),
        },
        {
            question: intl.formatMessage({ id: 'protection_plus_page_item_4_question' }),
            answer: intl.formatMessage({ id: 'protection_plus_page_item_4_answer' }),
        },
    ]

    return (
        <AppContainer className={css.ProtectionPlusContent}>
            <HomePageSectionHeader
                className={css.sectionHeader}
                subtitle={intl.formatMessage({ id: 'protection_plus_page_subtitle' })}
                title={intl.formatMessage({ id: 'protection_plus_page_title' })}
            // description={<>
            //   {intl.formatMessage({ id: 'protection_plus_page_description' })}
            // </>}
            />
            <div className={css.coverImage}>
                <img src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-protection-plus.png`, { width: 1000 })} />
            </div>
            <div className={css.faqCardGrid}>
                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <h2
                        className={css.questionPart}
                    >
                        {FAQ_ITEM_LIST[0].question}
                    </h2>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            {FAQ_ITEM_LIST[0].answer}
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <h2
                        className={css.questionPart}
                    >
                        {FAQ_ITEM_LIST[1].question}
                    </h2>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            {FAQ_ITEM_LIST[1].answer}
                            <br />
                            <div className={css.table}>
                                <table className={css.refundExampleTable}>
                                    <tbody>
                                        <tr>
                                            <th>{intl.formatMessage({ id: 'protection_plus_table_header_column_title_1' })}</th>
                                            <th>{intl.formatMessage({ id: 'protection_plus_table_header_column_title_2' })}</th>
                                            <th>{intl.formatMessage({ id: 'protection_plus_table_header_column_title_3' })}</th>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_1_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_1_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_1_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_2_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_2_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_2_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_3_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_3_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_3_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_4_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_4_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_4_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_5_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_5_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_5_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_6_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_6_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_6_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_7_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_7_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_7_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_8_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_8_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_8_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_9_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_9_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_9_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_10_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_10_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_10_column_content_3' })}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_11_column_content_1' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_11_column_content_2' })}
                                            </td>
                                            <td>
                                                {intl.formatMessage({ id: 'protection_plus_table_row_11_column_content_3' })}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <h2
                        className={css.questionPart}
                    >
                        {FAQ_ITEM_LIST[2].question}
                    </h2>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            {FAQ_ITEM_LIST[2].answer}
                        </div>
                    </div>
                </div>

                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <h2
                        className={css.questionPart}
                    >
                        {FAQ_ITEM_LIST[3].question}
                    </h2>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            <a href={'https://forms.gle/6eQ8nQqAcdyW8tru9'} target='_blank' rel='noopener noreferrer'>
                                {FAQ_ITEM_LIST[3].answer}
                            </a>
                        </div>
                    </div>
                </div>

                <div
                    className={css.disclaimer}
                >
                    {intl.formatMessage({ id: `protection_plus_page_item_5_question` })}
                </div>

            </div>
        </AppContainer>
    )
}

export default ProtectionPlusContent

.ProtectionPlusContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 48px;

    @include responsive('md') {
        margin-bottom: 96px;
    }

    .sectionHeader {
        padding: 24px 0 12px 0;

        @include responsive('md') {
            padding: 48px 0 12px 0;
        }
    }

    .coverImage {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-bottom: 12px;

        img {
            max-width: 400px;
        }
    }

    .faqCardGrid {
        display: grid;
        grid-template-columns: 1fr;
        row-gap: 24px;
        max-width: 800px;
    }

    .faqCard {
        color: $color-app-white;
        border: 1px solid $color-separator-white-2;
        background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
        border-radius: $border-radius-theme-2;
        overflow: hidden;
        transition: all 0.25s ease-in;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        &.open {
            .answerContainer {
                max-height: 1000px;
                // height: 100%;
            }
        }

        .questionPart {
            flex-grow: 1;
            flex-shrink: 0;
            padding: 24px;
            font-weight: bold;
            font-size: 14px;
            position: relative;
            cursor: pointer;

            @include responsive('md') {
                font-size: 16px;
            }
        }

        .answerContainer {
            background-color: $color-card-background;
            transition: all 0.25s ease-in;
            max-height: 0;
            // height: 0;
            overflow: hidden;
            font-size: 16px;

            .answer {
                opacity: 0.8;
                padding: 24px;
                font-size: 14px;
                line-height: 24px;
                white-space: pre-line;

                @include responsive('md') {
                    font-size: 16px;
                    line-height: 26px;
                }
            }

            .table {
                width: 100%;
                overflow: scroll;

                .refundExampleTable {
                    width: 100%;
                    text-align: left;
                    border: 1px solid $color-separator-white-2;

                    th {
                        padding: 12px;
                        font-size: 14px;
                        font-weight: 900;
                    }

                    tr {
                        font-size: 12px;
                    }

                    td {
                        padding: 12px;
                    }

                    tr:nth-of-type(odd) {
                        background-color: #000;
                    }
                }
            }
        }
    }


    .disclaimer {
        padding: 24px 0;
        color: $color-app-white;
        opacity: 0.6;
    }

}
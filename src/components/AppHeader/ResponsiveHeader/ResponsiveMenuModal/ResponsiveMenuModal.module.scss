.ResponsiveMenuModal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    color: $color-app-white;
    z-index: 1000;

    @include responsive('md') {
        // display: none;
    }

    &.closed {
        display: none;
    }

    .menuHeader {
        background-color: #000;
        height: $app-header-height;
        width: 100%;
        display: flex;
        // border-bottom: 1px solid $color-separator-white-1;
        align-items: center;
        flex-direction: row;
        margin-bottom: 24px;
    }

    .headerContainer {
        height: 100%;
        display: flex;
        align-items: stretch;
        justify-content: space-between;
        width: 100%;

        .leftPart {
            display: flex;
            align-items: center;
            column-gap: 12px;
        }

        .rightPart {
            display: flex;
            align-items: center;
            column-gap: 24px;
        }

        .appLogo {
            height: 20px;
        }

        .appLogoSeparator {
            width: 1px;
            height: 20px;
            background-color: $color-app-white;
        }

        .menuButton {
            padding: 12px 0;
            cursor: pointer;
            color: $color-app-white;
        }
    }

    .menuContainer {
        .menuGrid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            row-gap: 24px;

            .menuLink {
                display: flex;
                align-items: center;
                cursor: pointer;
                font-size: 16px;
                font-weight: bold;
                text-transform: uppercase;
                transition: $transition-theme-1;
                column-gap: 6px;
            }
        }
    }
}
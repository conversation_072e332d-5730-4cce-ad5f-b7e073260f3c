import { CloseOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import { APP_NAME } from 'constants/app'
import Link from 'next/link'
import { useIntl } from 'react-intl'
import css from './ResponsiveMenuModal.module.scss'

const ResponsiveMenuModal = (props: any = {}) => {
  const {
    visible,
    onCancel,
  } = props

  const intl = useIntl()

  return (
    <div className={clsx(css.ResponsiveMenuModal, !visible ? css.closed : '')}>
      <div className={css.menuHeader}>
        <AppContainer className={css.headerContainer}>
          <div className={css.leftPart}>
            <Link href='/'>
              <img
                title={APP_NAME}
                className={css.appLogo}
                src='/logo-app-full.svg'
                alt={APP_NAME}
              />
            </Link>
            {/* <div className={css.appLogoSeparator} />
                    <Dropdown
                        dropdownRender={() => languageMenu}
                    >
                        <div className={clsx(css.menuLink)}>
                            {getLocaleTitle(locale)}
                        </div>
                    </Dropdown> */}
          </div>
          <div className={css.rightPart}>
            <div
              className={css.menuButton}
              onClick={onCancel}
            >
              <CloseOutlined />
            </div>
          </div>
        </AppContainer>
      </div>
      <AppContainer className={css.menuContainer}>
        <div className={css.menuGrid}>
          <Link href={'/products'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'how_it_works_page_title' })}
          </Link>
          <Link href={'/what-we-authenticate'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'what_we_authenticate_page_title' })}
          </Link>
          <Link href={'/search-certificate'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'search_certificate_page_title' })}
          </Link>
          <Link href={'/pricing'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'pricing_page_title' })}
          </Link>
          <Link href={'/customers'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'customers_page_title' })}
          </Link>
          <Link href={'/about'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'about_page_title' })}
          </Link>
          <Link href={'/faq'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'faq_page_title' })}
          </Link>
          <Link href={'/blog'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'blog_page_title' })}
          </Link>
          <Link href={'/contact'} className={clsx(css.menuLink)}>
            {intl.formatMessage({ id: 'contact_page_subtitle' })}
          </Link>
        </div>
      </AppContainer>
    </div>
  );
}

export default ResponsiveMenuModal

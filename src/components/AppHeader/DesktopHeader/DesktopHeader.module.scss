.DesktopHeader {
    background-color: #000;
    height: $app-header-height;
    position: fixed;
    top: 0;
    z-index: 1000;
    width: 100vw;
    display: none;
    align-items: center;

    @media print {
        display: none;
    }

    @include responsive('lg') {
        display: flex;
    }

    // border-bottom: 1px solid $color-separator-white-1;

    .headerContainer {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: stretch;
        justify-content: space-between;
        padding: 0 24px;

        .leftPart {
            display: flex;
            align-items: center;
            column-gap: 12px;
        }

        .rightPart {
            display: flex;
            align-items: center;
            column-gap: 24px;
        }

        .appLogo {
            height: 20px;
        }

        .appLogoSeparator {
            width: 1px;
            height: 15px;
            background-color: $color-app-white;
        }

        .languageMenuLink {
            color: $color-app-gray-500;
            height: 80%;
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            transition: $transition-theme-1;
            padding: 0 12px;

            &:hover {
                color: $color-app-white;
            }
        }

        .menuLink {
            color: $color-app-gray-500;
            height: 80%;
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            transition: $transition-theme-1;

            &:hover {
                color: $color-app-white;
            }

            &.button {
                height: 34px;
                cursor: pointer;
                padding: 3px 12px;
                background-color: $color-app-white;
                border-radius: 4px;
                color: #000;
                font-weight: bold;
                display: flex;
                align-items: center;
                justify-content: center;
                // justify-content: space-between;
                column-gap: 12px;
            }
        }

        .socialLink {
            width: 20px;
        }
    }
}

.dropdownRenderContent {
    cursor: pointer;
    padding: 12px;
    background-color: $color-app-header-dropdown-background;
    min-width: 100px;
    border-radius: $border-radius-theme-1;
    overflow-x: hidden;
    overflow-y: scroll;


    .dropdownMenu {
        display: flex;
        flex-direction: column;
        row-gap: 12px;

        .dropdownMenuItem {
            color: $color-app-header-dropdown-menu-item-text;
            background-color: $color-app-header-dropdown-menu-item-background;
            font-family: $font-theme-1;
            cursor: pointer;


            &:hover {
                color: $color-app-header-dropdown-menu-item-hover-text;
                background-color: $color-app-header-dropdown-menu-item-hover-background;
            }
        }
    }
}

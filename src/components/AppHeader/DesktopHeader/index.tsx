import clsx from 'clsx'
import { APP_LOCALES, APP_NAME } from 'constants/app'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import { getLocaleTitle } from 'utils/locale'
import css from './DesktopHeader.module.scss'
import { Dropdown } from 'antd'
import { GlobalOutlined, SearchOutlined } from '@ant-design/icons'

type DesktopHeaderProps = {
    className?: string
    transparent?: boolean
}

const DesktopHeader = ({
    className,
    transparent = true,
}: DesktopHeaderProps) => {
    const router = useRouter()
    const intl = useIntl()
    const { locale, asPath, route } = router

    const handleLanguageMenuOnClick = (languageKey: any) => {
        router.push(route, asPath, { locale: languageKey, scroll: true })
    }

    const aboutMenu = (
        <div className={css.dropdownRenderContent}>
            <div className={css.dropdownMenu}>
                <Link href={'/about'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'about_page_title' })}
                </Link>
                <Link href={'/standards'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'standards_page_link' })}
                </Link>
                <Link href={'/financial-guarantee'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'financial_guarantee_page_title_footer' })}
                </Link>
                <Link href={'/customers'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'customers_page_title' })}
                </Link>
                <Link href={'/faq'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'faq_page_title' })}
                </Link>
                <Link href={'/blog'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'blog_page_title' })}
                </Link>
            </div>
        </div >
    )

    const productMenu = (
        <div className={css.dropdownRenderContent}>
            <div className={css.dropdownMenu}>
                <Link href={'/products/app-authentication'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'how_it_works_page_online_section_title' })}
                </Link>
                <Link href={'/products/api-authentication'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'how_it_works_page_offline_section_title' })}
                </Link>
                <Link href={'/products/authclass-courses'} className={clsx(css.dropdownMenuItem)}>
                    {intl.formatMessage({ id: 'authclass_page_title' })}
                </Link>
            </div>
        </div >
    )

    const languageMenu = (
        <div className={css.dropdownRenderContent}>
            <div className={css.dropdownMenu}>
                {
                    APP_LOCALES.map((key) => (
                        <div
                            className={clsx(css.dropdownMenuItem)}
                            key={`app-language-menu-item-language-${key}`}
                            onClick={() => handleLanguageMenuOnClick(key)}
                            role='presentation'
                        >
                            {getLocaleTitle(key)}
                        </div>
                    ))
                }
            </div>
        </div >
    )

    return (
        <header className={clsx(css.DesktopHeader, className)}>
            <div className={css.headerContainer}>
                <div className={css.leftPart}>
                    <Link href='/'>
                        <img
                            title={APP_NAME}
                            className={css.appLogo}
                            src='/logo-app-full.svg'
                            alt={APP_NAME}
                        />
                    </Link>
                    {/* <div className={css.appLogoSeparator} /> */}
                </div>
                <div className={css.rightPart}>
                    <Dropdown
                        dropdownRender={() => productMenu}
                    >
                        <Link href={'/products'} className={clsx(css.menuLink)}>
                            {intl.formatMessage({ id: 'how_it_works_page_title' })}
                        </Link>
                    </Dropdown>
                    <Link href={'/what-we-authenticate'} className={clsx(css.menuLink)}>
                        {intl.formatMessage({ id: 'what_we_authenticate_page_title' })}
                    </Link>
                    <Link href={'/pricing'} className={clsx(css.menuLink)}>
                        {intl.formatMessage({ id: 'pricing_page_title' })}
                    </Link>
                    <Link href={'/contact'} className={clsx(css.menuLink)}>
                        {intl.formatMessage({ id: 'contact_page_subtitle' })}
                    </Link>
                    <Dropdown
                        dropdownRender={() => aboutMenu}
                    >
                        <div className={clsx(css.menuLink)}>
                            {intl.formatMessage({ id: 'app_header_more' })}
                        </div>
                    </Dropdown>
                    <Link href={'/search-certificate'} className={clsx(css.menuLink, css.button)}>
                        {intl.formatMessage({ id: 'search_certificate_page_title' })}
                    </Link>
                    {/* <a href={APP_DISCORD_URL} target='_blank' rel='noopener noreferrer'>
                        <img className={css.socialLink} src='/social/icon-social-discord.svg' alt='Discord' />
                    </a>
                    <a href={APP_INSTAGRAM_URL} target='_blank' rel='noopener noreferrer'>
                        <img className={css.socialLink} src='/social/icon-social-instagram.svg' alt='Instagram' />
                    </a> */}
                    <Dropdown
                        dropdownRender={() => languageMenu}
                    >
                        <div className={clsx(css.languageMenuLink)}>
                            {/* {getLocaleTitle(locale)} */}
                            <GlobalOutlined />
                        </div>
                    </Dropdown>
                </div>
            </div>
        </header>
    )
}
export default DesktopHeader

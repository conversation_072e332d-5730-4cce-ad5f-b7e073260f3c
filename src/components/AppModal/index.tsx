import React, { useEffect } from "react";
import { Modal } from "antd";
import { CloseOutlined } from "@ant-design/icons";

const AppModal = ({
  isModalOpen,
  setIsModalOpen,
  title,
  children,
  onCancel,
}: {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
  children: React.ReactNode;
  onCancel?: () => void;
}) => {
  useEffect(() => {
    if (isModalOpen) {
      const scrollY = window.scrollY;
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";
      document.body.style.overflow = "hidden";

      return () => {
        document.body.style.position = "";
        document.body.style.top = "";
        document.body.style.width = "";
        document.body.style.overflow = "";
        window.scrollTo(0, scrollY);
      };
    }
  }, [isModalOpen]);
  return (
    <Modal
      title={title}
      open={isModalOpen}
      onCancel={() => {
        setIsModalOpen(false);
        onCancel && onCancel();
      }}
      okButtonProps={{ style: { display: "none" } }}
      cancelButtonProps={{ style: { display: "none" } }}
      closeIcon={<CloseOutlined style={{ color: "white", fontSize: "16px" }} />}
      centered
      style={{
        maxHeight: "90vh",
        maxWidth: "90vw",
      }}
      styles={{
        body: {
          maxHeight: "70vh",
          overflowY: "auto",
          padding: "10px",
        },
      }}
    >
      {children}
    </Modal>
  );
};

export const AppConfirmModal = ({
  isModalOpen,
  setIsModalOpen,
  title,
  content,
  cancelText = "Cancel",
  confirmText = "Confirm",
  onCancel,
  onConfirm,
  loading = false,
}: {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
  content: string;
  cancelText?: string;
  confirmText?: string;
  onCancel?: () => void;
  onConfirm?: () => void | Promise<void>;
  loading?: boolean;
}) => {
  useEffect(() => {
    if (isModalOpen) {
      const scrollY = window.scrollY;
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";
      document.body.style.overflow = "hidden";

      return () => {
        document.body.style.position = "";
        document.body.style.top = "";
        document.body.style.width = "";
        document.body.style.overflow = "";
        window.scrollTo(0, scrollY);
      };
    }
  }, [isModalOpen]);

  const handleCancel = () => {
    setIsModalOpen(false);
    onCancel && onCancel();
  };

  const handleConfirm = async () => {
    if (onConfirm) {
      await onConfirm();
    }
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={title}
      open={isModalOpen}
      onCancel={handleCancel}
      okButtonProps={{ style: { display: "none" } }}
      cancelButtonProps={{ style: { display: "none" } }}
      closeIcon={<CloseOutlined style={{ color: "white", fontSize: "16px" }} />}
      centered
      style={{
        maxHeight: "90vh",
        maxWidth: "90vw",
      }}
    >
      <div className="space-y-6">
        <div className="text-white text-base">{content}</div>
        <div className="flex gap-3 justify-end text-white">
          <button
            onClick={handleCancel}
            className="px-6 py-2 rounded-lg font-medium bg-gray-600"
            disabled={loading}
          >
            {cancelText}
          </button>
          <button
            onClick={handleConfirm}
            className="px-6 py-2 rounded-lg font-medium bg-red-600"
            disabled={loading}
          >
            {loading ? "Loading..." : confirmText}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default AppModal;

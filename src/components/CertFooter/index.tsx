import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import { APP_DISCORD_URL, APP_FACEBOOK_URL, APP_INSTAGRAM_URL, APP_LINKEDIN_URL, APP_NAME, APP_TWITTER_URL } from 'constants/app'
import _ from 'lodash'
import moment from 'moment'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import css from './AppFooter.module.scss'

type AppFooterProps = {
    className?: string
}

const AppFooter = ({
    className,
}: AppFooterProps) => {
    const router = useRouter()
    const intl = useIntl()
    const { locale, asPath, route } = router

    return (
        <footer className={clsx(css.AppFooter, className)}>
            <AppContainer>
                <div className={css.companyInformationSection}>
                    <div className={css.leftPart}>
                        <img
                            className={css.companyLogo}
                            src='/logo-legit-app.png'
                            alt={intl.formatMessage({ id: `app_title` })}
                        />
                        <div className={css.companyInformation}>
                            <span className={css.copyright}>
                                {intl.formatMessage({ id: 'app_footer_website_info_copyright' }, { year: moment().utc().format('YYYY') })}
                            </span>
                        </div>
                    </div>
                    <div className={css.rightPart}>
                        <div className={css.companyInformation}>
                            <span className={css.disclaimer}>
                                {intl.formatMessage({ id: 'app_footer_website_info_disclaimer' })}
                            </span>
                        </div>
                        {/* <Link href='/terms'>
                            <div className={css.sitemapLink}>
                                Terms of Service
                            </div>
                        </Link>
                        <Link href='/privacy'>
                            <div className={css.sitemapLink}>
                                Privacy Policy
                            </div>
                        </Link> */}
                    </div>
                </div>
            </AppContainer>
        </footer>
    )
}
export default AppFooter

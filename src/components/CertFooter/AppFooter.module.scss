.AppFooter {
    background-color: $color-app-dark;
    color: $color-app-white;

    @media print {
        display: none;
    }

    .sitemapSection {
        padding: 96px 0;

        .sitemapSectionContainer {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 24px;
            row-gap: 24px;

            @include responsive('md') {
                grid-template-columns: 3fr 1fr 1fr 1fr;
            }

            .sitemapCategoryContainer {
                display: flex;
                flex-direction: column;
                row-gap: 12px;

                .sitemapCategoryHeader {
                    font-weight: bold;
                    margin-bottom: 6px;
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    row-gap: 12px;

                    @include responsive('md') {
                        align-items: flex-start;
                    }

                    .appLogo {
                        max-width: 140px;
                        width: 100%;
                    }
                }

                .sitemapCategoryLinkContainer {
                    display: flex;
                    flex-direction: column;
                    row-gap: 12px;
                    // align-items: center;

                    @include responsive('md') {
                        align-items: flex-start;
                    }

                    .sitemapLink {
                        transition: all 0.25s ease-in;
                        opacity: 0.6;
                        font-size: 14px;

                        &:hover {
                            opacity: 1;
                        }
                    }

                    .appDescription {
                        // text-align: center;
                        font-weight: bold;
                        opacity: 0.8;
                        font-size: 20px;
                        max-width: 300px;

                        @include responsive('md') {
                            text-align: left;
                            font-size: 24px;
                            max-width: 500px;
                        }
                    }
                }
            }
        }
    }

    .companyInformationSection {
        display: flex;
        flex-direction: column;
        // align-items: center;
        column-gap: 12px;
        row-gap: 12px;
        padding: 24px 0;
        justify-content: space-between;
        font-size: 14px;

        @include responsive('md') {
            flex-direction: row;
        }

        .leftPart {
            display: flex;
            column-gap: 12px;
            align-items: center;
        }

        .rightPart {
            display: flex;
            column-gap: 12px;
            align-items: center;
        }

        img.companyLogo {
            width: 30px;
            height: 30px;
            border-radius: 3px;
        }

        .companyInformation {
            display: flex;
            column-gap: 12px;

            .copyright {
                font-size: 12px;
                text-align: center;
            }

            .disclaimer {
                font-size: 12px;
                opacity: 0.6;
                // text-align: center;
            }
        }
    }
}

import Vimeo from '@u-wave/react-vimeo'
import { useIntl } from 'react-intl'
import css from './LuxeTagsIntroductionContent.module.scss'

const LuxeTagsIntroductionContent = (props) => {
  const intl = useIntl()

  const getVimeoPlayer = () => {
    try {
      return (
        <Vimeo
          video={`1013057780`}
          className={css.videoPlayer}
          loop={false}
          showPortrait={false}
          showTitle={false}
          showByline={false}
        />
      )
    } catch {
      // Empty
    }
    return null
  }

  return (
    <div className={css.LuxeTagsIntroductionContent}>
      <div className='tutorial-section'>
        <div className='container'>
          <div className='section-content'>
            <div className='tutorial-card-part'>
              <div className='tutorial-card tutorial-card-no-padding'>
                <div className='tutorial-card-background'>
                  <div className='section-header'>
                    <div className='section-header-title-container'>
                      <div className='section-header-title'>
                        <img src='/logo-luxe-tag.png' alt='LUXE TAGS' />
                      </div>

                    </div>
                  </div>
                  <img
                    className='card-image card-image-1'
                    src='/illustration-luxe-tags-1.png'
                    alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                  />
                  <img
                    className='card-image card-image-1-2'
                    src='/illustration-luxe-tags-2.png'
                    alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                  />
                </div>
                <div className='card-content card-content-1'>
                  <h2 className='card-title card-title-1'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                  </h2>
                  <div className='card-subtitle card-subtitle-1'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_subtitle' })}
                  </div>
                </div>
              </div>
              <div className={css.videoPlayerContainer}>
                <div className={css.videoPlayerPart}>
                  <div className={css.playerWrapper}>
                    {getVimeoPlayer()}
                  </div>
                </div>
              </div>
              <div className='tutorial-card-seperator'>
                <img
                  className='tag-separator-image'
                  src='/illustration-luxe-tags-separator.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                />
              </div>
              <div className='tutorial-card-two-columns'>
                <div className='tutorial-card'>
                  <img
                    className='card-image card-image-2'
                    src='/illustration-luxe-tags-3.png'
                    alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_2_title' })}
                  />
                  <div className='card-content'>
                    <h3 className='card-title'>
                      {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_2_title' })}
                    </h3>
                    <div className='card-subtitle'>
                      {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_2_subtitle' })}
                    </div>
                  </div>
                </div>
                <div className='tutorial-card'>
                  <img
                    className='card-image card-image-2'
                    src='/illustration-luxe-tags-4.png'
                    alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_3_title' })}
                  />
                  <div className='card-content'>
                    <h3 className='card-title'>
                      {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_3_title' })}
                    </h3>
                    <div className='card-subtitle'>
                      {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_3_subtitle' })}
                    </div>
                  </div>
                </div>
              </div>
              <div className='tutorial-card'>
                <img
                  className='card-image-4'
                  src='/illustration-tags-4.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_4_title' })}
                />
                <div className='card-content'>
                  <h3 className='card-title'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_4_title' })}
                  </h3>
                  <div className='card-subtitle'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_4_subtitle' })}
                  </div>
                </div>
              </div>
              <div className='tutorial-card-seperator'>
                <img
                  className='tag-separator-image-2'
                  src='/illustration-tags-separator-2.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                />
              </div>
              <div className='tutorial-card'>
                <div className='card-content'>
                  <h2 className='card-section-2-title'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_section_2_title' })}
                  </h2>
                </div>
              </div>
              <div className='tutorial-card card-5 card-with-background'>
                <div className='card-content'>
                  <h3 className='card-title'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_5_title' })}
                  </h3>
                  <div className='card-subtitle'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_5_subtitle' })}
                  </div>
                </div>
                <img
                  className='card-image-3'
                  src='/illustration-luxe-tags-5.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_5_title' })}
                />
              </div>
              <div className='tutorial-card-seperator'>
                <img
                  className='tag-separator-image'
                  src='/illustration-luxe-tags-separator-2.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                />
              </div>
              <div className='tutorial-card card-6'>
                <div className='card-content'>
                  <h3 className='card-title'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_6_title' })}
                  </h3>
                  <div className='card-subtitle'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_6_subtitle' })}
                  </div>
                </div>
                <img
                  className='card-image-3'
                  src='/illustration-luxe-tags-7.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_6_title' })}
                />
              </div>
              <div className='tutorial-card-seperator'>
                <img
                  className='tag-separator-image'
                  src='/illustration-luxe-tags-separator-2.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                />
              </div>
              <div className='tutorial-card card-7'>
                <div className='card-content'>
                  <h3 className='card-title'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_7_title' })}
                  </h3>
                  <div className='card-subtitle'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_7_subtitle' })}
                  </div>
                </div>
                <img
                  className='card-image-3'
                  src='/illustration-luxe-tags-8.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_7_title' })}
                />
              </div>
              <div className='tutorial-card-seperator'>
                <img
                  className='tag-separator-image'
                  src='/illustration-luxe-tags-separator-2.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                />
              </div>
              <div className='tutorial-card card-8'>
                <div className='card-content'>
                  <h3 className='card-title'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_8_title' })}
                  </h3>
                  <div className='card-subtitle'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_8_subtitle' })}
                  </div>
                </div>
                <img
                  className='card-image-3'
                  src='/illustration-luxe-tags-9.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_8_title' })}
                />
              </div>
              <div className='tutorial-card-seperator'>
                <img
                  className='tag-separator-image'
                  src='/illustration-luxe-tags-separator.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_1_title' })}
                />
              </div>
              <div className='tutorial-card card-9'>
                <div className='card-content'>
                  <h3 className='card-title'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_9_title' })}
                  </h3>
                  <div className='card-subtitle'>
                    {intl.formatMessage({ id: 'lux_tags_page_tutorial_item_9_subtitle' })}
                  </div>
                </div>
                <img
                  className='card-image-3'
                  src='/illustration-luxe-tags-10.png'
                  alt={intl.formatMessage({ id: 'lux_tags_page_tutorial_item_9_title' })}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div >
  )
}

export default LuxeTagsIntroductionContent

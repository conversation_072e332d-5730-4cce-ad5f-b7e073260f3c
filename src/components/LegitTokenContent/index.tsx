import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useState } from 'react'
import { useIntl } from 'react-intl'
import css from './LegitTokenContent.module.scss'
import resizeImageUrl from 'utils/resizeImageUrl'

const LegitTokenContent = () => {

    const intl = useIntl()
    const [faqCardOpenStatusMap, setFaqCardOpenStatusMap] = useState({} as any)

    const FAQ_ITEM_LIST = [
        {
            question: intl.formatMessage({ id: 'legit_token_page_faq_section_item_1_question' }),
            answer: `${intl.formatMessage({ id: 'legit_token_page_faq_section_item_1_answer' })}`,
        },
        {
            question: intl.formatMessage({ id: 'legit_token_page_faq_section_item_2_question' }),
            answer: intl.formatMessage({ id: 'legit_token_page_faq_section_item_2_answer' }),
        },
        {
            question: intl.formatMessage({ id: 'legit_token_page_faq_section_item_3_question' }),
            answer: `${intl.formatMessage({ id: 'legit_token_page_faq_section_item_3_answer' })}`,
        }
    ]

    const faqCardQuestionOnClick = (question: string) => {
        setFaqCardOpenStatusMap({
            ...faqCardOpenStatusMap,
            [question]: !faqCardOpenStatusMap[question],
        })
    }

    return (
        <AppContainer className={css.LegitTokenContent}>
            <HomePageSectionHeader
                className={css.sectionHeader}
                subtitle={intl.formatMessage({ id: 'legit_token_page_faq_section_subtitle' })}
                title={intl.formatMessage({ id: 'legit_token_page_faq_section_title' })}
                description={<>
                    {intl.formatMessage({ id: 'legit_token_page_faq_section_description' })}
                </>}
            />
            <div className={css.coverImage}>
                <img src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-legit-token.jpg`, { width: 1000 })} />
            </div>
            <div className={css.faqCardGrid}>
                {
                    FAQ_ITEM_LIST.map((faqItem: any) => (
                        <div
                            className={clsx(css.faqCard, css.open)}
                            key={`faq-page-faq-card-${faqItem.question}`}
                        >
                            <h2
                                className={css.questionPart}
                                onClick={() => faqCardQuestionOnClick(faqItem.question)}
                            >
                                {faqItem.question}
                            </h2>
                            <div className={css.answerContainer}>
                                <div className={css.answer}>
                                    {faqItem.answer}
                                </div>
                            </div>
                        </div>
                    ))
                }
            </div>
        </AppContainer>
    )
}

export default LegitTokenContent

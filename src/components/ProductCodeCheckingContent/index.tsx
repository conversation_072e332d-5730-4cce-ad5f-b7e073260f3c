import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useState } from 'react'
import { useIntl } from 'react-intl'
import css from './ProductCodeCheckingContent.module.scss'
import resizeImageUrl from 'utils/resizeImageUrl'

const ProductCodeCheckingContent = () => {

    const intl = useIntl()
    const [faqCardOpenStatusMap, setFaqCardOpenStatusMap] = useState({} as any)

    const FAQ_ITEM_LIST = [
        {
            question: intl.formatMessage({ id: 'legit_token_page_faq_section_item_1_question' }),
            answer: `${intl.formatMessage({ id: 'legit_token_page_faq_section_item_1_answer' })}`,
        },
        {
            question: intl.formatMessage({ id: 'legit_token_page_faq_section_item_2_question' }),
            answer: intl.formatMessage({ id: 'legit_token_page_faq_section_item_2_answer' }),
        },
        {
            question: intl.formatMessage({ id: 'legit_token_page_faq_section_item_3_question' }),
            answer: `${intl.formatMessage({ id: 'legit_token_page_faq_section_item_3_answer' })}`,
        }
    ]

    return (
        <AppContainer className={css.ProductCodeCheckingContent}>
            <HomePageSectionHeader
                className={css.sectionHeader}
                subtitle={intl.formatMessage({ id: 'product_code_checking_page_subtitle' })}
                title={intl.formatMessage({ id: 'product_code_checking_page_title' })}
                description={<>
                    {intl.formatMessage({ id: 'product_code_checking_page_description' })}
                </>}
            />
            <div className={css.coverImage}>
                {/* <img src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-legit-token.jpg`, { width: 1000 })} /> */}
            </div>
            <HomePageSectionHeader
                className={css.sectionHeader}
                title={`What does Product Code Checking result look?`}
                description={<>
                    Here’re examples for the product code checking results.
                </>}
            />
                        <div className={css.coverImage}>
                {/* <img src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-legit-token.jpg`, { width: 1000 })} /> */}
            </div>
            <div className={css.faqCardGrid}>
                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <h3
                        className={css.questionPart}
                    >
                        Chanel Luxury Handbags
                    </h3>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                            Product code matched product photo
                            <br />
                            <br />
                            <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-product-code-item-1.png' />
                            <br />
                            <br />
                            Product code matched product related history
                            <br />
                            <br />
                            <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-product-code-item-2.png' />
                        </div>
                    </div>
                </div>
                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <h3
                        className={css.questionPart}
                    >
                        Cartier / Piaget / Van Cleef & Arpels
                    </h3>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                        Product code matched product photo
                            <br />
                            <br />
                            <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-product-code-item-3.png' />
                        </div>
                    </div>
                </div>
                <div
                    className={clsx(css.faqCard, css.open)}
                >
                    <h3
                        className={css.questionPart}
                    >
                        Bvlgari
                    </h3>
                    <div className={css.answerContainer}>
                        <div className={css.answer}>
                        Product code matched product photo
                            <br />
                            <br />
                            <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-product-code-item-4.png' />
                        </div>
                    </div>
                </div>
            </div>
        </AppContainer>
    )
}

export default ProductCodeCheckingContent

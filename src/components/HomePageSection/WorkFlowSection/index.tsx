import { ArrowRightOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppVideoBackground from 'components/AppVideoBackground'
import { useAnimation } from 'framer-motion'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import resizeImageUrl from 'utils/resizeImageUrl'
import HomePageSectionHeader from '../HomePageSectionHeader'
import css from './WorkFlowSection.module.scss'

const WorkFlowSection = () => {
    const router = useRouter()
    const intl = useIntl()
    const fadeInAnimation = useAnimation()
    // const { ref, inView } = useInView({
    //   threshold: 0.5,
    //   triggerOnce: true,
    // })

    // const startAnimation = async () => {
    //   await fadeInAnimation.start((i) => ({
    //     opacity: 1,
    //     y: 0,
    //     transition: { delay: i * 0.5, ease: 'easeInOut', duration: 0.5 },
    //   }))
    // }

    // useEffect(() => {
    //   if (!inView) {
    //     return
    //   }
    //   startAnimation()
    // }, [inView])

    return (
        <div className={css.WorkFlowSection}>
            <AppContainer className={css.sectionContainer}>
                <HomePageSectionHeader
                    className={clsx(css.sectionHeader)}
                    subtitle={intl.formatMessage({ id: 'home_page_workflow_section_subtitle' })}
                    title={intl.formatMessage({ id: 'home_page_workflow_section_title' })}
                    description={intl.formatMessage({ id: 'home_page_workflow_section_description' })}
                />
                <div className={css.tutorialCardGrid}>
                    <div className={clsx(css.tutorialCard, css.card1)}>
                        <AppVideoBackground
                            src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-workflow-1.mp4'
                            poster='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-workflow-1.jpg'
                        />
                        <div className={css.cardContent}>
                            <div className={css.step}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_step_title` })} 1
                            </div>
                            <div className={css.title}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_1_title` })}
                            </div>
                            <div className={css.instruction}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_1_subtitle` })}
                            </div>
                        </div>
                    </div>
                    <div className={clsx(css.tutorialCard, css.card2)}
                        style={{
                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-workflow-2.jpg`, { width: 400 })})`,
                        }}
                    >
                        <div className={css.cardContent}>
                            <div className={css.step}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_step_title` })} 2
                            </div>
                            <div className={css.title}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_2_title` })}
                            </div>
                            <div className={css.instruction}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_2_subtitle` })}
                            </div>
                        </div>
                    </div>
                    <div className={clsx(css.tutorialCard, css.card3)}>
                        <AppVideoBackground
                            src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-workflow-3.mp4'
                            poster='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-workflow-3.jpg'
                        />
                        <div className={css.cardContent}>
                            <div className={css.step}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_step_title` })} 3
                            </div>
                            <div className={css.title}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_3_title` })}
                            </div>
                            <div className={css.instruction}>
                                {intl.formatMessage({ id: `home_page_workflow_section_item_3_subtitle` })}
                            </div>
                        </div>
                    </div>
                </div>
                <Link
                    href={'/products'}
                    className={css.sectionActionButton}
                >
                    {intl.formatMessage({ id: `home_page_workflow_section_more_button_title` })}<ArrowRightOutlined />
                </Link>
            </AppContainer>
        </div >
    )
}

export default WorkFlowSection

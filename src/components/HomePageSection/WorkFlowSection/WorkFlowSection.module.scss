.WorkFlowSection {
    padding: 48px 0;

    @include responsive('md') {
        padding: 96px 0;
    }

    .sectionContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .sectionHeader {
        padding: 48px 0 36px 0;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }
    }

    .tutorialCardGrid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        column-gap: 24px;
        row-gap: 24px;
        margin-bottom: 24px;

        @include responsive('md') {
            margin-bottom: 48px;
            grid-template-columns: repeat(3, 1fr);
        }

        .tutorialCard {
            min-height: 500px;
            color: $color-app-white;
            row-gap: 6px;
            border: 1px solid $color-separator-white-2;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            overflow: hidden;
            position: relative;
            height: 420px;

            video {
              position: absolute;
              z-index: 0;
              inset: 0;
              object-fit: cover;
              pointer-events: none;
            }

            &.card1 video {
              top: auto;
              bottom: -20%;
            }

            &.card3 video {
              top: auto;
              bottom: -10%;
            }

            &.card2 {
              background-position: center;
              background-size: cover;
              background-repeat: no-repeat;
            }

            .cardContent {
                padding: 24px;
                position: relative;
                display: flex;
                flex-direction: column;
                background-image: linear-gradient(to bottom, black, rgba(0, 0, 0, 60%) 80%, transparent 100%);
                
                @include responsive('md') {
                    padding: 40px;
                }

                .step {
                    width: 100%;
                    opacity: 0.7;
                    font-weight: bold;
                    margin-bottom: 3px;
                    font-size: 14px;
                    line-height: 26px;

                    @include responsive('md') {
                        font-size: 16px;
                        line-height: 26px;
                    }
                }

                .title {
                    font-weight: bold;
                    margin-bottom: 6px;
                    font-size: 16px;
                    line-height: 26px;

                    @include responsive('md') {
                        font-size: 20px;
                        line-height: 30px;
                    }
                }

                .instruction {
                    opacity: 0.6;
                    font-size: 14px;
                    line-height: 24px;

                    @include responsive('md') {
                        font-size: 16px;
                        line-height: 26px;
                    }
                }
            }
        }
    }

    .sectionActionButton {
        cursor: pointer;
        padding: 10px 12px;
        background-color: $color-app-white;
        border-radius: 4px;
        color: #000;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        // justify-content: space-between;
        column-gap: 12px;
        width: 100%;
        font-size: 14px;
    
        @include responsive('md') {
          width: fit-content;
          font-size: 16px;
        }
    }
}

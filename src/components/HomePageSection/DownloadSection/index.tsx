import AppContainer from 'components/AppContainer'
import { APP_APP_STORE_URL, APP_GOOGLE_PLAY_URL } from 'constants/app'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './DownloadSection.module.scss'
import { tagDownloadAndroidAppEvent, tagDownloadiOSAppEvent } from 'utils/gtag'

const DownloadSection = () => {
  const router = useRouter()
  const intl = useIntl()
  return (
    <div className={css.DownloadSection}>
      <div className={css.sectionBackground}>
        <div />
        <div
          className={css.coverImage}
          style={{
            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-download.png`, { width: 1000 })})`,
          }}
        />
      </div>
      <AppContainer className={css.sectionContainer}>
        <div className={css.contentPart}>
          <div>
            <img className={css.downloadAppLogo} src='/logo-legit-app.png' alt='LEGIT APP' />
          </div>
          <div className={css.sectionHeader}>
            <h3 className={css.subtitle}>
              {intl.formatMessage({ id: 'home_page_download_section_subtitle' })}
            </h3>
            <h2 className={css.title}>
              {intl.formatMessage({ id: 'home_page_download_section_title' })}
            </h2>
            <div className={css.description}>
              {intl.formatMessage({ id: 'home_page_download_section_description' })}
            </div>
          </div>
          <div className={css.downloadButtonContainer}>
            <div className={css.downloadButtonGrid}>
              <a onClick={tagDownloadiOSAppEvent} href={APP_APP_STORE_URL} target='_blank' rel='noopener noreferrer'>
                <img className={css.downloadButton} src='https://legitapp-static.oss-accelerate.aliyuncs.com/badge-app-store-blue.svg' alt='App Store' />
              </a>
              <a onClick={tagDownloadAndroidAppEvent} href={APP_GOOGLE_PLAY_URL} target='_blank' rel='noopener noreferrer'>
                <img className={css.downloadButton} src='https://legitapp-static.oss-accelerate.aliyuncs.com/badge-google-play-blue.svg' alt='Google Play' />
              </a>
            </div>
          </div>
        </div>
        <div />
      </AppContainer>
    </div>
  )
}

export default DownloadSection

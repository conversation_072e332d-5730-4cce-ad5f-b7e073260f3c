.DownloadSection {
  background-color: #000;
  overflow: hidden;
  padding: 24px 0;
  position: relative;
  background: radial-gradient(50% 32% at 50% 100%, #009dff 0%, rgba(0, 217, 255, .25) 53%, rgba(0, 119, 255, .15) 79%, rgba(0, 178, 255, 0) 100%);
  padding: 48px 0 300px 0;

  @include responsive('md') {
    padding: 96px 0;
  }

  .sectionBackground {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;

    @include responsive('md') {
      grid-template-columns: repeat(2, 1fr);
      width: 100%;
    }

    .coverImage {
      width: 100%;
      height: 100%;
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
    }
  }

  .sectionContainer {
    position: relative;
    display: grid;
    grid-template-columns: repeat(1, 1fr);

    @include responsive('md') {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .coverPart {
  }

  .contentPart {
    display: flex;
    flex-direction: column;
    align-items: center;

    @include responsive('md') {
      align-items: flex-start;
    }

    .downloadAppLogo {
      margin: 0 6px 24px 6px;
      width: 60px;
      height: 60px;
      border-radius: 17.5%;
      // box-shadow: rgba(255,255,255,0.8) 0 0 10px;
      border: 1px solid #fff;
    }

    .sectionHeader {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      margin-bottom: 24px;
      align-items: center;

      @include responsive('md') {
        align-items: flex-start;
        row-gap: 16px;
      }

      .subtitle {
        width: 100%;
        color: #fff;
        font-size: 12px;
        line-height: 22px;
        font-weight: bold;
        // text-align: center;
        background: linear-gradient(270deg,
            rgb(89, 119, 255) 0%,
            rgb(49, 255, 215) 101.39%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;

        @include responsive('md') {
          text-align: unset;
          margin-bottom: 12px;
          font-size: 20px;
        }
      }

      .title {
        width: 100%;
        color: #fff;
        font-size: 20px;
        text-align: center;
        font-weight: 800;
        text-transform: uppercase;

        @include responsive('md') {
          font-size: 40px;
          line-height: 50px;
          text-align: unset;
        }
      }

      .description {
        width: 100%;
        color: #fff;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        opacity: 0.6;
        max-width: 500px;

        @include responsive('md') {
          text-align: unset;
          font-size: 16px;
          line-height: 26px;
        }
      }
    }

    .downloadButtonGrid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      width: fit-content;
      margin-bottom: 48px;

      @include responsive('md') {
        margin-bottom: unset;
      }

      .downloadButton {
        text-align: center;
        margin: 0 6px 12px 6px;
        height: 50px;
        overflow: hidden;

        @include responsive('md') {
          height: 50px;
        }
      }
    }
  }
}
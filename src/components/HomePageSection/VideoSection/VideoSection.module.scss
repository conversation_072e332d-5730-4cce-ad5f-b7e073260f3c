.VideoSection {
    min-height: 500px;
    background-image: radial-gradient(circle, $color-app-gray-600 0%, $color-app-gray-900 40%, #000 100%);
    padding: 48px 0 100px 0;

    @include responsive('md') {
        padding: 96px 0 96px 0;
    }

    .sectionContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .sectionHeader {
        padding: 48px 0 36px 0;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }
    }

    .videoPlayerPart {
        background-color: #000;
        width: 100%;
        max-width: 800px;
        border: 1px solid $color-separator-white-1;
        border-radius: 20px;
        -webkit-backface-visibility: hidden;
        -moz-backface-visibility: hidden;
        -webkit-transform: translate3d(0, 0, 0);
        -moz-transform: translate3d(0, 0, 0);
        border-radius: $border-radius-theme-1;
        overflow: hidden;
        // box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.3);
        box-shadow: 0 4px 30px rgba(0, 0, 0, 1);
        margin-bottom: 48px;

        @include responsive('md') {
            padding: unset;
            min-width: 600px;
            width: 100%;
            max-width: 1000px;
            margin-bottom: 48px;
        }


        .playerWrapper {
            width: 100%;
            // height: 200px;
            padding-top: 56.25%;
            position: relative;
            // align-items: center;
            // justify-content: center;
            // display: none;

            @include responsive('md') {
                // display: flex;
                // width: 100%;
            }

            .videoPlayer {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;

                >iframe {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }

    .reviewItemGrid {
        width: 100%;
        display: grid;
        max-width: 800px;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 8px;

        @include responsive('md') {
            padding: unset;
            min-width: 600px;
            width: 100%;
            max-width: 1000px;
            column-gap: 24px;
        }

        .reviewItem {
            width: 100%;
            color: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
            column-gap: 12px;
            row-gap: 12px;
            margin-bottom: 24px;
            line-height: 24px;
            // border: 1px solid $color-separator-white-1;
            padding: 0 12px;
            // border-radius: $border-radius-theme-1;
            border-right: 1px solid $color-separator-white-1;

            &:last-child {
                border-right: 0px;
            }

            @include responsive('md') {
                padding: 24px;
            }

            // background-color: #000;
            // background-image: linear-gradient(to right, rgba(0, 0, 0, 0.84), rgba(0, 0, 0, 0.75));

            .reviewContent {
                display: flex;
                flex-direction: column;
                align-items: center;
                column-gap: 12px;
                font-size: 14px;

                @include responsive('md') {
                    flex-direction: row;
                    font-size: 16px;
                }

                .title {
                    opacity: 0.8;
                    font-weight: bold;
                    font-size: 16px;

                    .star {
                        color: #ffc107;
                    }
                }

                .subtitle {
                    opacity: 0.6;
                }
            }

            .logo {
                max-width: 90px;
                margin-bottom: 12px;

                @include responsive('md') {
                    max-width: 130px;
                }

                img {
                    width: 100%;
                    // max-width: 100px;
                }
            }
        }
    }

    .downloadMessage {
        width: 100%;
        color: #fff;
        font-size: 14px;
        text-align: center;
        opacity: 0.6;
        max-width: 400px;
        margin-bottom: 24px;
        line-height: 24px;

        @include responsive('md') {
            max-width: 600px;
            font-size: 16px;
            line-height: 30px;
        }
    }

    .downloadButtonGrid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        width: fit-content;

        .downloadButton {
            text-align: center;
            margin: 0 6px 12px 6px;
            height: 50px;
            overflow: hidden;

            @include responsive('md') {
                height: 50px;
            }
        }
    }
}
import { StarFilled } from '@ant-design/icons'
import Vimeo from '@u-wave/react-vimeo'
import AppContainer from 'components/AppContainer'
import { APP_APP_STORE_URL, APP_GOOGLE_PLAY_URL, APP_TRUSTPILOT_URL } from 'constants/app'
import { useAnimation } from 'framer-motion'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import HomePageSectionHeader from '../HomePageSectionHeader'
import css from './VideoSection.module.scss'

const VideoSection = () => {
    const router = useRouter()
    const intl = useIntl()
    const fadeInAnimation = useAnimation()
    // const { ref, inView } = useInView({
    //   threshold: 0.5,
    //   triggerOnce: true,
    // })

    // const startAnimation = async () => {
    //   await fadeInAnimation.start((i) => ({
    //     opacity: 1,
    //     y: 0,
    //     transition: { delay: i * 0.5, ease: 'easeInOut', duration: 0.5 },
    //   }))
    // }

    // useEffect(() => {
    //   if (!inView) {
    //     return
    //   }
    //   startAnimation()
    // }, [inView])

    const getVimeoPlayer = () => {
        try {
            return (
                <Vimeo
                    video={`1005288416`}
                    className={css.videoPlayer}
                    loop={false}
                    showPortrait={false}
                    showTitle={false}
                    showByline={false}
                />
            )
        } catch {
            // Empty
        }
        return null
    }

    return (
        <div className={css.VideoSection}>
            <AppContainer className={css.sectionContainer}>
                <HomePageSectionHeader
                    className={css.sectionHeader}
                    title={intl.formatMessage({ id: 'home_page_video_section_title' })}
                    subtitle={intl.formatMessage({ id: 'home_page_video_section_subtitle' })}
                    description={intl.formatMessage({ id: 'home_page_video_section_description' })}
                />
                <div className={css.videoPlayerPart}>
                    <div className={css.playerWrapper}>
                        {getVimeoPlayer()}
                    </div>
                </div>
                <div className={css.reviewItemGrid}>
                    <div className={css.reviewItem}>
                        <a href={APP_TRUSTPILOT_URL} target='_blank' rel='noopener noreferrer'>
                            <div className={css.logo}>
                                <img src='/icon-review-trustpilot.svg' alt='Trustpilot' />
                            </div>
                        </a>
                        <div className={css.reviewContent}>
                            <div className={css.title}>
                                Excellent
                            </div>
                            <div className={css.subtitle}>
                                4.7 / 5
                            </div>
                        </div>
                    </div>
                    <div className={css.reviewItem}>
                        <a href={APP_APP_STORE_URL} target='_blank' rel='noopener noreferrer'>
                            <div className={css.logo}>
                                <img src='/logo-review-appstore.svg' alt='App Store' />
                            </div>
                        </a>
                        <div className={css.reviewContent}>
                            <div className={css.title}>
                                4.9 <StarFilled className={css.star} />
                            </div>
                            <div className={css.subtitle}>
                                8.6k+ {intl.formatMessage({ id: `home_page_video_section_ratings` })}
                            </div>
                        </div>
                    </div>
                    <div className={css.reviewItem}>
                        <a href={APP_GOOGLE_PLAY_URL} target='_blank' rel='noopener noreferrer'>
                            <div className={css.logo}>
                                <img src='/logo-review-googleplay.svg' alt='Google Play' />
                            </div>
                        </a>
                        <div className={css.reviewContent}>
                            <div className={css.title}>
                                4.7 <StarFilled className={css.star} />
                            </div>
                            <div className={css.subtitle}>
                                2.7k+ {intl.formatMessage({ id: `home_page_video_section_ratings` })}
                            </div>
                        </div>
                    </div>
                </div>
                <div className={css.downloadMessage}>
                    {/* {intl.formatMessage({ id: 'home_page_review_section_description' })} */}
                    {/* <br /> */}
                    {/* {intl.formatMessage({ id: 'home_page_video_section_download_2' })} */}
                </div>
                {/* <div className={css.downloadButtonContainer}>
                    <div className={css.downloadButtonGrid}>
                        <a onClick={tagDownloadiOSAppEvent} href={APP_APP_STORE_URL} target='_blank' rel='noopener noreferrer'>
                            <img className={css.downloadButton} src='https://legitapp-static.oss-accelerate.aliyuncs.com/badge-app-store-blue.svg' alt='App Store' />
                        </a>
                        <a onClick={tagDownloadAndroidAppEvent} href={APP_GOOGLE_PLAY_URL} target='_blank' rel='noopener noreferrer'>
                            <img className={css.downloadButton} src='https://legitapp-static.oss-accelerate.aliyuncs.com/badge-google-play-blue.svg' alt='Google Play' />
                        </a>
                    </div>
                </div> */}
            </AppContainer>
        </div >
    )
}

export default VideoSection

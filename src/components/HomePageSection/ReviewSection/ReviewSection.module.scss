.ReviewSection {
    background: #000000;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    overflow: hidden;
    position: relative;
    overflow: hidden;
    padding: 48px 0;

    @include responsive('md') {
        padding: 96px 0;
    }

    .sectionHeader {
        padding: 48px 0 36px 0;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }
    }

    .highlightCardGrid {
        display: grid;
        row-gap: 24px;
        column-gap: 24px;
        margin-bottom: 24px;
        grid-template-columns: repeat(1, 1fr);

        @include responsive('md') {
            grid-template-columns: repeat(2, 1fr);
        }

        .highlightCard {
            border: 1px solid $color-separator-white-1;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            overflow: hidden;
            min-height: 120px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-repeat: no-repeat;

            @include responsive('md') {
                min-height: 240px;
            }

            .cardBackground {
                position: absolute;
                top: 0;
                width: 100%;
                height: 100%;
                background-position: center;
                background-size: cover;
                background-repeat: no-repeat;
            }

            .cardContent {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .cardTitle {
                    font-weight: bold;
                    font-size: 20px;
                    width: 100%;
                    text-align: center;
                    display: flex;
                    column-gap: 10px;

                    @include responsive('md') {
                        font-size: 30px;
                    }

                    .reviewStars {
                        font-size: 20px;
                        color: #ffc107;
                        display: flex;
                        column-gap: 2px;

                        @include responsive('md') {
                            font-size: 30px;
                        }
                    }
                }

                .cardSubtitle {
                    font-size: 16px;
                    font-weight: bold;
                    width: 100%;
                    text-align: center;

                    @include responsive('md') {
                        font-size: 20px;
                    }
                }
            }
        }
    }

    .userReviewItemGrid {
        grid-template-columns: repeat(1, 1fr);
        width: 100%;
        column-gap: 24px;
        row-gap: 24px;
        display: grid;

        @include responsive('md') {
            grid-template-columns: repeat(4, 1fr);
        }

        &.desktop {
            display: none;

            @include responsive('md') {
                display: grid;
            }
        }

        &.responsive {
            display: grid;

            @include responsive('md') {
                display: none;
            }
        }

        .userReviewItem {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            padding: 12px;
            border: 1px solid $color-separator-white-1;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
            overflow: hidden;


            @include responsive('md') {
                padding: 24px;
            }


            a.userReviewLink {
                display: flex;
                flex-direction: column;
                overflow: hidden;
                height: 100%;
                width: 100%;

                @include responsive('md') {
                    align-items: flex-start;
                }
            }

            .userReviewHeader {
                width: 100%;
                display: flex;
                align-content: center;
                column-gap: 16px;
                margin-bottom: 12px;
                justify-content: space-between;

                .leftPart {
                    display: flex;
                    align-items: center;
                    column-gap: 10px;

                    @include responsive('md') {
                        column-gap: 16px;
                    }
                }

                .rightPart {
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    overflow: hidden;
                    padding: 3px;
                    background-color: $color-app-gray-600;
                }

                .arrow {
                    width: 100%;
                    height: 100%;
                    background-image: url('/icon-home-review-arrow.svg');
                    background-size: contain;
                }

                .userAvatar {
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: cover;
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    overflow: hidden;

                    @include responsive('md') {
                        width: 40px;
                        height: 40px;
                    }
                }

                .userProfile {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .userName {
                        color: #fff;
                        font-size: 14px;
                        font-weight: bold;
                        overflow: hidden;
                    }

                    .userSubtitle {
                        color: #fff;
                        font-size: 12px;
                        opacity: 0.6;
                    }
                }
            }

            .userReviewContent {
                width: 100%;
                color: #fff;
                font-size: 16px;
                opacity: 0.6;
                font-weight: 300;
                opacity: 0.6;
                font-size: 14px;
                line-height: 24px;

                @include responsive('md') {
                    font-size: 16px;
                    line-height: 26px;
                }
            }
        }
    }

    .sectionFooterContainer {
        display: flex;
        align-content: center;
        justify-content: center;
    }

    .sectionActionButton {
        cursor: pointer;
        margin: 24px 0 0 0;
        padding: 10px 12px;
        background-color: $color-app-white;
        border-radius: 4px;
        color: #000;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        // justify-content: space-between;
        column-gap: 12px;
        width: 100%;
        font-size: 14px;

        @include responsive('md') {
            width: fit-content;
            font-size: 16px;
        }
    }
}
.MediaSection {
  background: #000;
  padding: 24px 12px;
  border-bottom: 1px solid $color-separator-white-1;
  position: relative;
  // background: radial-gradient(50% 32% at 50% 100%,#009dff 0%,rgba(0,217,255,.25) 53%,rgba(0,119,255,.15) 79%,rgba(0,178,255,0) 100%);
  // background: radial-gradient(50% 32% at 50% 100%,#009dff 0%,rgba(0,217,255,.25) 53%,rgba(0,119,255,.15) 79%,rgba(0,178,255,0) 100%);

  @include responsive('md') {
    padding: 48px 0;
  }

  .sectionBackground {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .layer1 {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.5;
      background: radial-gradient(50% 32% at 50% 100%, #009dff 0%, rgba(0, 217, 255, .25) 53%, rgba(0, 119, 255, .15) 79%, rgba(0, 178, 255, 0) 100%);
      ;
    }

    .layer2 {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.5;
      background: linear-gradient(180deg, rgba(0, 153, 255, 0) 0%, rgb(0, 178, 255) 100%);
      mask: radial-gradient(73% 93% at 48.5% 40.300000000000004%, rgba(0, 0, 0, .1) 0%, rgba(0, 0, 0, .3) 64%, rgba(0, 0, 0, 1) 100%) add;
    }

    .layer3 {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.5;
      background: linear-gradient(180deg, rgba(0, 85, 255, 0) 0%, rgba(8, 0, 255, .7) 100%);
      mask: radial-gradient(50% 131% at 48.699999999999996% 31.8%, rgba(0, 0, 0, .5) 0%, rgba(0, 0, 0, .32556) 64%, rgba(0, 0, 0, 1) 100%) add;
      ;
    }

  }

  .sectionHeader {
    margin-bottom: 24px;
  }

  .mediaItemContainer {
    // background-color: red;
    display: flex;
    overflow: hidden;
    position: relative;
    align-items: center;
    justify-content: center;
  }

  .mediaItemFadeOverlay {
    position: absolute;
    top: 0;
    width: 100px;
    height: 100%;
    // background-color: red;
    // background: linear-gradient(180deg, rgb(0, 0, 0) 0%, rgb(0, 0, 0) 100%);

    &.left {
      left: 0;
      background: linear-gradient(90deg, rgb(0, 0, 0) 0%, transparent);
    }

    &.right {
      right: 0;
      background: linear-gradient(90deg, transparent 0%, rgb(0, 0, 0));
    }
  }


  .mediaItemGrid {
    position: relative;
    grid-template-columns: repeat(5, 1fr);
    row-gap: 12px;
    column-gap: 24px;
    display: none;

    &.first {
      display: grid;
    }

    @include responsive('md') {
      grid-template-columns: repeat(10, 1fr);
      animation: carousel 30s linear infinite;
      min-width: 2000px;
      display: grid;
    }

    @keyframes carousel {
      0% {
        transform: translateX(0);
      }

      100% {
        transform: translateX(-100%);
      }
    }

    .mediaItem {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .mediaItemLogo {
      img {
        height: 70px;

        @include responsive('md') {
          height: 100px;
        }
      }
    }
  }
}
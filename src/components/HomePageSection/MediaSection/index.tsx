import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import getImageUrl from 'utils/imageUrl'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './MediaSection.module.scss'

const MEDIA_ITEMS = [{
  title: 'ebay',
  icon_image_url: 'logo-ebay.png',
}, {
  title: 'PayPal',
  icon_image_url: 'logo-paypal.png',
},
{
  title: 'ELLE',
  icon_image_url: 'logo-elle.png',
  // url: 'https://www.elle.com.hk/fashion/legit-app-instant-authentication-of-fashion-items',
},
{
  title: 'Bazaar',
  icon_image_url: 'logo-bazaar.png',
  // url: 'https://www.harpersbazaar.com.hk/fashion/ways-to-authenticate-luxury-items',
},
{
  title: 'HYPEBEAST',
  icon_image_url: 'logo-hypebeast.png',
  // url: 'https://hypebeast.com/2020/12/legit-app-digital-authenticator-info',
},
{
  title: 'SneakerFreaker',
  icon_image_url: 'logo-sneakerfreaker.png',
  // url: 'https://www.sneakerfreaker.com/news/dont-get-duped-legit-app-offers-rapid-sneaker-authentication',
},
{
  title: 'ComplexCon',
  icon_image_url: 'logo-complexcon.png',
  // url: 'https://www.sneakerfreaker.com/news/dont-get-duped-legit-app-offers-rapid-sneaker-authentication',
},
{
  title: 'Vogue',
  icon_image_url: 'logo-vogue.png',
  // url: 'https://www.sneakerfreaker.com/news/dont-get-duped-legit-app-offers-rapid-sneaker-authentication',
},
{
  title: 'GotSole',
  icon_image_url: 'logo-gotsole.png',
  // url: 'https://www.sneakerfreaker.com/news/dont-get-duped-legit-app-offers-rapid-sneaker-authentication',
},
{
  title: 'Sneaker Con',
  icon_image_url: 'logo-sneakercon.png',
  // url: 'https://www.sneakerfreaker.com/news/dont-get-duped-legit-app-offers-rapid-sneaker-authentication',
},
]

const MediaSection = () => {
  const router = useRouter()
  const intl = useIntl()
  return (
    <div className={css.MediaSection}>
      <AppContainer>
        {/* <HomePageSectionHeader
          className={css.sectionHeader}
          title={intl.formatMessage({
            id: 'home_page_media_section_title',
          })}
        /> */}
        <div className={css.mediaItemContainer}>
          <div className={clsx(css.mediaItemGrid, css.first)}>
            {
              MEDIA_ITEMS.map(item => (
                <div
                  key={`home-page-media-item-${item.title}-col`}
                  className={css.mediaItem}
                >
                  <div className={css.mediaItemLogo}>
                    {/* <img src={`${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/${item.icon_image_url}`), { width: 200 })}`} alt={item.title} /> */}
                    <img src={`/media/${item.icon_image_url}`} alt={item.title} />
                  </div>
                </div>
              ))
            }
          </div>
          <div className={css.mediaItemGrid}>
            {
              MEDIA_ITEMS.map(item => (
                <div
                  key={`home-page-media-item-${item.title}-col`}
                  className={css.mediaItem}
                >
                  <div className={css.mediaItemLogo}>
                    {/* <img src={`${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/${item.icon_image_url}`), { width: 200 })}`} alt={item.title} /> */}
                    <img src={`/media/${item.icon_image_url}`} alt={item.title} />
                  </div>
                </div>
              ))
            }
          </div>
          <div className={css.mediaItemGrid}>
            {
              MEDIA_ITEMS.map(item => (
                <div
                  key={`home-page-media-item-${item.title}-col`}
                  className={css.mediaItem}
                >
                  <div className={css.mediaItemLogo}>
                    {/* <img src={`${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/${item.icon_image_url}`), { width: 200 })}`} alt={item.title} /> */}
                    <img src={`/media/${item.icon_image_url}`} alt={item.title} />
                  </div>
                </div>
              ))
            }
          </div>
          <div className={clsx(css.mediaItemFadeOverlay, css.left)} />
          <div className={clsx(css.mediaItemFadeOverlay, css.right)} />
        </div>
      </AppContainer>
      <div className={css.sectionBackground}>
        <div className={clsx(css.background, css.layer1)} />
        <div className={clsx(css.background, css.layer2)} />
        <div className={clsx(css.background, css.layer3)} />
      </div>
    </div>
  )
}

export default MediaSection

import data from 'data/productCategory'
import { motion, useAnimation } from 'framer-motion'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { useInView } from 'react-intersection-observer'
import { useIntl } from 'react-intl'

import { ArrowRightOutlined } from '@ant-design/icons'
import AppContainer from 'components/AppContainer'
import Link from 'next/link'
import getImageUrl from 'utils/imageUrl'
import { getLocalisedField } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import HomePageSectionHeader from '../HomePageSectionHeader'
import css from './ProductCategorySection.module.scss'

const PRODUCT_MODEL_ITEMS = [
  { image_url: '/background-home-category-1.jpg', }
  , { image_url: '/background-home-category-2.jpg', }
  , { image_url: '/background-home-category-3.jpg', }
  , { image_url: '/background-home-category-4.jpg', }
  , { image_url: '/background-home-category-5.jpg', }
  , { image_url: '/background-home-category-6.jpeg', }
  , { image_url: '/background-home-category-7.jpeg', }
  , { image_url: '/background-home-category-8.jpg', }
  , { image_url: '/background-home-category-9.png', }
  , { image_url: '/background-home-category-10.jpeg', }
  , { image_url: '/background-home-category-11.jpg', }
  , { image_url: '/background-home-category-12.jpg', }
  , { image_url: '/background-home-category-13.jpg', }
  , { image_url: '/background-home-category-14.jpg', }
  , { image_url: '/background-home-category-15.jpg', }
  , { image_url: '/background-home-category-16.jpg', }
  , { image_url: '/background-home-category-17.jpg', }
  , { image_url: '/background-home-category-18.jpg', }
  , { image_url: '/background-home-category-19.jpg', }
  , { image_url: '/background-home-category-20.jpg', }
  , { image_url: '/background-home-category-21.jpg', }
  , { image_url: '/background-home-category-22.jpeg', }
  , { image_url: '/background-home-category-23.jpeg', }
  , { image_url: '/background-home-category-24.jpg', }
  , { image_url: '/background-home-category-25.jpg', }
  , { image_url: '/background-home-category-26.jpg', }
  , { image_url: '/background-home-category-28.jpg', }
  , { image_url: '/background-home-category-29.jpg', }
  , { image_url: '/background-home-category-30.jpg', }
  , { image_url: '/background-home-category-31.png', }
  , { image_url: '/background-home-category-32.jpg', }
  , { image_url: '/background-home-category-33.jpg', }
  , { image_url: '/background-home-category-34.jpg', }
  , { image_url: '/background-home-category-35.jpg', }
  , { image_url: '/background-home-category-36.jpg', }
  , { image_url: '/background-home-category-37.jpg', }
  , { image_url: '/background-home-category-38.jpg', }
  , { image_url: '/background-home-category-39.jpg', }
  , { image_url: '/background-home-category-40.jpg', }
]

const ProductCategorySection = () => {
  const intl = useIntl()
  const router = useRouter()
  const { locale } = router
  const fadeInAnimation = useAnimation()
  const { ref, inView } = useInView({
    threshold: 0.5,
    triggerOnce: true,
  })

  const startAnimation = async () => {
    await fadeInAnimation.start((i) => ({
      opacity: 1,
      y: 0,
      transition: { delay: i * 0.5, ease: 'easeInOut', duration: 0.5 },
    }))
  }

  useEffect(() => {
    if (!inView) {
      return
    }
    startAnimation()
  }, [inView])

  const getVisionImageCustom = (index: any) => {
    if (index < 8) {
      return index % 2
    }
    if (index < 16) {
      return index % 2 === 0 ? 1 : 0
    }
    return index % 2
  }

  return (
    <div className={css.ProductCategorySection}>
      <div className={css.visionImageBackground}>
        <div
          className={css.visionImageItemGrid}
        >
          {
            PRODUCT_MODEL_ITEMS.map((item, index) => (
              <div
                className={css.visionImageItemContainer}
                key={`vision-image-${item.image_url}`}
              >
                <motion.div
                  className={css.visionImageItem}
                  animate={fadeInAnimation}
                  initial={{
                    opacity: 0,
                  }}
                  custom={getVisionImageCustom(index)}
                  style={{
                    // backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com${item.image_url}`, { width: 200 })})`,
                    backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/${item.image_url}`), { width: 100 })})`,
                  }}
                />
              </div>
            ))
          }
        </div>
      </div>
      <div className={css.visionImageBackgroundOverlay} />
      <AppContainer className={css.sectionContainer}>
        <div className={css.contentCard} ref={ref}>
          <HomePageSectionHeader
            className={css.sectionHeader}
            subtitle={intl.formatMessage({ id: 'home_page_product_category_section_subtitle' })}
            // title={intl.formatMessage({ id: 'home_page_product_category_section_title' })}
            title={intl.formatMessage({ id: 'home_page_product_category_section_title' })}
            // subtitle={intl.formatMessage({ id: 'home_page_product_category_section_subtitle' })}
            description={intl.formatMessage({ id: 'home_page_product_category_section_description' })}
          />
          <div className={css.productCategoryItemGrid}>
            {
              data.map(item => (
                <Link
                  href={`/what-we-authenticate/category/${item.slug}`}
                  className={css.productCategoryItemContainer}
                  key={`home-page-product-category-item-${item.id}-col`}
                >
                  <div className={css.productCategoryItemIcon}>
                    <img
                      src={`${resizeImageUrl(getImageUrl(`${item.icon_image_url}`), { width: 200 })}`}
                      title={`${intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} ${getLocalisedField(item, 'title', locale)}`}
                      alt={`${intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} ${getLocalisedField(item, 'title', locale)}`}
                    />
                  </div>
                  <h3 className={css.productCategoryItemTitle}>
                    <span style={{ display: 'none' }}>{intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} </span> {getLocalisedField(item, 'title', locale)}
                  </h3>
                </Link>
              ))
            }
          </div>
          <Link href={'/what-we-authenticate'} className={css.sectionActionButton}>
            {intl.formatMessage({ id: 'home_page_product_category_section_full_brand_button_title' })} <ArrowRightOutlined />
          </Link>
        </div>
      </AppContainer>
    </div >
  )
}

export default ProductCategorySection

.ProductCategorySection {
  background: #000;
  padding: 96px 12px;
  position: relative;
  overflow: hidden;

  @include responsive('md') {
    padding: 160px 0;
  }

  .visionImageBackgroundOverlay {
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.84), rgba(0, 0, 0, 0.75));
    opacity: 0.7;
    transition: all 0.25s ease-in;
    top: 0;
    left: 0;
  }

  .visionImageBackground {
    height: 600%;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    padding: 0 12px;

    @include responsive('md') {
      // padding: 0 24px;
    }

    .visionImageItemGrid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      row-gap: 12px;
      column-gap: 12px;

      @include responsive('md') {
        grid-template-columns: repeat(7, 1fr);
        // row-gap: 24px;
        // column-gap: 24px;
      }

      .visionImageItemContainer {
        .visionImageItem {
          background-color: #191c21;
          height: 100%;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          height: 200px;
          // margin-bottom: 16px;
          border-radius: $border-radius-theme-1;
          overflow: hidden;
        }
      }
    }
  }

  .sectionContainer {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .contentCard {
    background-color: black;
    border: 1px solid $color-separator-white-1;
    color: $color-app-white;
    border-radius: $border-radius-theme-2;
    overflow: hidden;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 48px 24px 24px 24px;
    align-items: center;
    background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
    // background-color: transparent;

    // background: rgba(255, 255, 255, 0.2);
    // border-radius: 16px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 1);
    // backdrop-filter: blur(5px);
    // -webkit-backdrop-filter: blur(5px);
    // border: 1px solid rgba(255, 255, 255, 0.3);

    @include responsive('md') {
      padding: 48px;
      width: 90%;
    }
  }

  .sectionHeader {

    @include responsive('md') {}
  }

  .productCategoryItemGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    row-gap: 24px;
    column-gap: 24px;
    padding: 24px;
    width: 100%;
    margin-bottom: 24px;

    @include responsive('md') {
      grid-template-columns: repeat(5, 1fr);
      padding: 48px;
    }

    .productCategoryItemContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      row-gap: 12px;

      .productCategoryItemIcon {
        img {
          height: 40px;
        }
      }

      .productCategoryItemTitle {
        width: 100%;
        color: #fff;
        font-size: 10px;
        text-align: center;
        font-weight: bold;
        text-transform: uppercase;

        @include responsive('md') {
          font-size: 14px;
        }
      }
    }
  }

  .sectionActionButton {
    cursor: pointer;
    padding: 10px 12px;
    background-color: $color-app-white;
    border-radius: 4px;
    color: #000;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    // justify-content: space-between;
    column-gap: 12px;
    width: 100%;
    font-size: 14px;

    @include responsive('md') {
      width: fit-content;
      font-size: 16px;
    }
  }
}
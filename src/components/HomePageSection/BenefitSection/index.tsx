import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppVideoBackground from 'components/AppVideoBackground'
import { useAnimation } from 'framer-motion'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import resizeImageUrl from 'utils/resizeImageUrl'
import HomePageSectionHeader from '../HomePageSectionHeader'
import css from './BenefitSection.module.scss'

const BenefitSection = () => {
    const router = useRouter()
    const intl = useIntl()
    const fadeInAnimation = useAnimation()
    // const { ref, inView } = useInView({
    //   threshold: 0.5,
    //   triggerOnce: true,
    // })

    // const startAnimation = async () => {
    //   await fadeInAnimation.start((i) => ({
    //     opacity: 1,
    //     y: 0,
    //     transition: { delay: i * 0.5, ease: 'easeInOut', duration: 0.5 },
    //   }))
    // }

    // useEffect(() => {
    //   if (!inView) {
    //     return
    //   }
    //   startAnimation()
    // }, [inView])

    return (
        <div className={css.BenefitSection}>
            <AppContainer className={css.sectionContainer}>
                <div className={css.informationPart}>
                    <HomePageSectionHeader
                        className={clsx(css.sectionHeader)}
                        title={intl.formatMessage({ id: 'home_page_benefit_section_title' })}
                        subtitle={intl.formatMessage({ id: 'home_page_benefit_section_subtitle' })}
                        description={intl.formatMessage({ id: 'home_page_benefit_section_description' })}
                    />
                    <div className={css.benefitItemCardGrid}>
                        <div className={clsx(css.benefitItemCardGridRow, css.row1)}>
                            <div
                                className={clsx(css.benefitItemCard, css.card1)}
                                style={{
                                    backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-feature-1.jpg`, { width: 1000 })})`,
                                }}
                            >
                                <div className={css.benefitContentContainer}>
                                    <div className={css.benefitContentTitle}>
                                        {intl.formatMessage({ id: 'home_page_benefit_section_item_1_title' })}
                                    </div>
                                    <div className={css.benefitContentSubtitle}>
                                        {intl.formatMessage({ id: 'home_page_benefit_section_item_1_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={clsx(css.benefitItemCard, css.card2)}>
                                <AppVideoBackground
                                    src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-feature-2.mp4'
                                    poster={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-feature-2.jpg`, { width: 1000 })}
                                />
                                <div className={css.benefitContentContainer}>
                                    <div className={css.benefitContentTitle}>
                                        {intl.formatMessage({ id: 'home_page_benefit_section_item_2_title' })}
                                    </div>
                                    <div className={css.benefitContentSubtitle}>
                                        {intl.formatMessage({ id: 'home_page_benefit_section_item_2_description' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className={clsx(css.benefitItemCardGridRow, css.row2)}>
                            <div className={clsx(css.benefitItemCard, css.card3)}>
                                <AppVideoBackground
                                    src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-feature-3.mp4'
                                    poster={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-feature-3.jpg`, { width: 1000 })}
                                />
                                <div className={css.benefitContentContainer}>
                                    <div className={css.benefitContentTitle}>
                                        {intl.formatMessage({ id: 'home_page_benefit_section_item_3_title' })}
                                    </div>
                                    <div className={css.benefitContentSubtitle}>
                                        {intl.formatMessage({ id: 'home_page_benefit_section_item_3_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={clsx(css.benefitItemCard, css.card4)}>
                                <AppVideoBackground
                                    src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-feature-4.mp4'
                                    poster={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-feature-4.jpg`, { width: 1000 })}
                                />
                                <div className={css.benefitContentContainer}>
                                    <div className={css.benefitContentTitle}>
                                        {intl.formatMessage({ id: 'home_page_benefit_section_item_4_title' })}
                                    </div>
                                    <div className={css.benefitContentSubtitle}>
                                        {intl.formatMessage({ id: 'home_page_benefit_section_item_4_description' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div >
                </div>
            </AppContainer>
        </div >
    )
}

export default BenefitSection

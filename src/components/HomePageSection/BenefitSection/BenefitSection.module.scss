.BenefitSection {
    position: relative;
    padding: 48px 0;

    @include responsive('md') {
        padding: 96px 0;
    }

    .sectionContainer {
        position: relative;
        display: grid;

        .coverPart {}

        .informationPart {
            color: $color-app-white;
            display: flex;
            flex-direction: column;

            .sectionHeader {
                padding: 48px 0 36px 0;

                @include responsive('md') {
                    padding: 96px 0 60px 0;
                }
            }

            .benefitItemCardGrid {
                display: grid;
                grid-template-columns: 1fr;
                row-gap: 24px;
                column-gap: 24px;

                .benefitItemCardGridRow {
                    display: grid;
                    grid-template-columns: 1fr;
                    row-gap: 24px;
                    column-gap: 24px;

                    &.row1 {
                        @include responsive('md') {
                            grid-template-columns: 6fr 8fr;
                        }
                    }

                    &.row2 {
                        @include responsive('md') {
                            grid-template-columns: 8fr 6fr;
                        }
                    }
                }
            }

            .benefitItemCard {
                display: flex;
                flex-direction: column;
                align-items: center;
                column-gap: 24px;
                border: 1px solid $color-separator-white-2;
                border-radius: $border-radius-theme-2;
                // background: radial-gradient(128% 107% at 0% 0%, #292929 0%, rgb(0, 0, 0) 77.61472409909909%);;
                overflow: hidden;
                position: relative;
                height: 420px;

                video {
                  position: absolute;
                  z-index: 0;
                  inset: 0;
                  object-fit: cover;
                  pointer-events: none;
                }

                &.card1 {
                  background-position: center;
                  background-size: cover;
                  background-repeat: no-repeat;
                }

                &.card2 video {
                  top: auto;
                  bottom: -7.5%;
                }

                &.card3 video {
                  top: auto;
                  bottom: -16%;
                }

                &.card4 video {
                  top: auto;
                  bottom: -17.5%;
                }
                
                @include responsive('md') {                  
                  height: 440px;

                  &.card3 video {
                    bottom: -7.5%;
                  }

                  &.card4 video {
                    bottom: -15%;
                  }
                }

                .benefitContentContainer {
                    position: relative;
                    padding: 24px;
                    display: flex;
                    flex-direction: column;
                    row-gap: 6px;
                    width: 100%;
                    background-image: linear-gradient(to bottom, black, rgba(0, 0, 0, 60%) 80%, transparent 100%);

                    @include responsive('md') {
                        padding: 40px;
                    }

                    .benefitContentTitle {
                        font-weight: bold;
                        text-transform: uppercase;
                        display: inline;
                        margin-right: 6px;
                        font-size: 16px;
                        line-height: 26px;

                        @include responsive('md') {
                            font-size: 20px;
                            line-height: 30px;
                        }


                    }

                    .benefitContentSubtitle {
                        display: inline;
                        opacity: 0.6;
                        font-size: 14px;
                        line-height: 24px;

                        @include responsive('md') {
                            font-size: 16px;
                            line-height: 26px;
                        }
                    }
                }
            }
        }
    }
}

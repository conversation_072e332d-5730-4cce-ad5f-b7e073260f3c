import { useIntl } from 'react-intl'

import { ArrowRightOutlined, CaretRightFilled } from '@ant-design/icons'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import productAll from 'data/productAll.json'
import Link from 'next/link'
import getImageUrl from 'utils/imageUrl'
import resizeImageUrl from 'utils/resizeImageUrl'
import HomePageSectionHeader from '../HomePageSectionHeader'
import css from './BrandSection.module.scss'
import { getLocalisedField } from 'utils/locale'
const {
    product_brand: productBrandList,
} = productAll

const BrandSection = () => {
    const intl = useIntl()
    return (
        <div className={css.BrandSection}>
            <AppContainer className={css.sectionContainer}>
                <HomePageSectionHeader
                    className={clsx(css.sectionHeader)}
                    subtitle={intl.formatMessage({ id: 'what_we_authenticate_page_category_section_title' })}
                    title={intl.formatMessage({ id: 'what_we_authenticate_page_brand_section_subtitle' })}
                // description={<>
                //     {intl.formatMessage({ id: 'home_page_review_section_description' })}
                //     <br />
                //     {intl.formatMessage({ id: 'home_page_pricing_section_description_2' })}
                // </>}
                />
            </AppContainer>
            <AppContainer>
                <div className={clsx(css.brandItemGrid)}>
                    {
                        productBrandList
                            .filter((brandItem: any) => !!brandItem.featured)
                            .sort((a, b) => a.featured - b.featured)
                            .map((brandItem: any) => (
                                <Link
                                    href={`/what-we-authenticate/${brandItem.slug}`}
                                    key={`home-page-desktop-brand-section-brand-${brandItem.id}`}
                                >
                                    <div className={css.brandItem}>
                                        <div
                                            className={css.brandImage}
                                            style={{
                                                backgroundImage: `url(${resizeImageUrl(getImageUrl(brandItem?.icon_image_url), { width: 100 })})`,
                                            }}
                                        />
                                        <h3 className={css.brandTitle}>
                                            <span style={{ display: 'none' }}>{intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} </span>{brandItem?.title}
                                        </h3>
                                        <div className={css.link}>
                                            <CaretRightFilled className={css.arrowIcon} />
                                        </div>
                                    </div>
                                </Link>
                            ))
                    }
                </div>
            </AppContainer>
            <AppContainer className={css.sectionFooterContainer}>
                <Link href={'/what-we-authenticate'} className={css.sectionActionButton}>
                    {intl.formatMessage({ id: 'home_page_product_category_section_full_brand_button_title' })} <ArrowRightOutlined />
                </Link>
            </AppContainer>
        </div>
    )
}

export default BrandSection

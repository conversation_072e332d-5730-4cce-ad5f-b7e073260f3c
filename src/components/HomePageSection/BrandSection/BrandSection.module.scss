.BrandSection {
    background: #000000;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    overflow: hidden;
    position: relative;
    overflow: hidden;
    padding: 48px 0;

    @include responsive('md') {
        padding: 96px 0;
    }

    .sectionHeader {
        padding: 48px 0 36px 0;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }
    }

    .brandItemGrid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 12px;
        row-gap: 12px;
        width: 100%;

        @include responsive('md') {
            column-gap: 24px;
            row-gap: 24px;
        }

        @include responsive('md') {
            grid-template-columns: repeat(6, 1fr);
        }

        .brandItem {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 8px 12px 8px;
            padding-top: 24px;
            row-gap: 24px;
            width: 100%;
            height: 100%;
            border: 0.5px solid $color-separator-white-1;
            transition: all 0.25s ease-in;
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: $border-radius-theme-2;
            position: relative;
            overflow: hidden;

            @include responsive('md') {
                padding: 24px;
                padding-top: 48px;
            }

            &:hover {
                background-color: $color-app-header-dropdown-menu-item-hover-background;

                .link {
                    opacity: 1;
                    right: 16px;
                }
            }

            .brandImage {
                display: flex;
                align-items: center;
                justify-content: center;
                background-size: contain;
                background-position: center;
                height: 50px;
                width: 50px;
                
                @include responsive('md') {
                    height: 100px;
                    width: 100px;
                }
            }

            .brandTitle {
                width: 100%;
                color: #fff;
                font-size: 10px;
                text-align: center;
                font-weight: bold;
                text-transform: uppercase;

                @include responsive('md') {
                    font-size: 14px;
                }
            }

            .link {
                font-size: 12px;
                background-color: $color-app-white;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                position: absolute;
                top: 16px;
                right: 24px;
                opacity: 0;
                transition: all 0.25s ease-in;

                .arrowIcon {
                    font-weight: bold;
                    color: $color-app-gray-900;
                    font-size: 12px;
                }
            }
        }
    }

    .sectionFooterContainer {
        display: flex;
        align-content: center;
        justify-content: center;
    }

    .sectionActionButton {
        cursor: pointer;
        margin: 24px 0 0 0;
        padding: 10px 12px;
        background-color: $color-app-white;
        border-radius: 4px;
        color: #000;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        // justify-content: space-between;
        column-gap: 12px;
        width: 100%;
        font-size: 14px;

        @include responsive('md') {
            width: fit-content;
            font-size: 16px;
        }
    }
}
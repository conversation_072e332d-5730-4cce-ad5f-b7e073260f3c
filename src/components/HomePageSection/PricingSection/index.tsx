import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import { useAnimation } from 'framer-motion'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import HomePageSectionHeader from '../HomePageSectionHeader'
import css from './PricingSection.module.scss'
import Link from 'next/link'
import { ArrowRightOutlined, CheckOutlined } from '@ant-design/icons'
import resizeImageUrl from 'utils/resizeImageUrl'

const PricingSection = () => {
    const router = useRouter()
    const intl = useIntl()
    const fadeInAnimation = useAnimation()
    // const { ref, inView } = useInView({
    //   threshold: 0.5,
    //   triggerOnce: true,
    // })

    // const startAnimation = async () => {
    //   await fadeInAnimation.start((i) => ({
    //     opacity: 1,
    //     y: 0,
    //     transition: { delay: i * 0.5, ease: 'easeInOut', duration: 0.5 },
    //   }))
    // }

    // useEffect(() => {
    //   if (!inView) {
    //     return
    //   }
    //   startAnimation()
    // }, [inView])

    return (
        <div className={css.PricingSection}>
            <AppContainer className={css.sectionContainer}>
                <HomePageSectionHeader
                    className={clsx(css.sectionHeader)}
                    title={intl.formatMessage({ id: 'home_page_pricing_section_title' })}
                    subtitle={intl.formatMessage({ id: 'home_page_pricing_section_subtitle' })}
                    description={<>
                        {intl.formatMessage({ id: 'home_page_pricing_section_description_1' })}
                        <br />
                        {intl.formatMessage({ id: 'home_page_pricing_section_description_2' })}
                    </>}
                />
                <div className={css.solutionCardGrid}>
                    <div className={css.solutionCard}>
                        <div className={css.iconImage}>
                            <img
                                src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-category-01.png`, { width: 140 })}
                                alt={intl.formatMessage({ id: 'home_page_pricing_section_luxury_title' })}
                            />
                        </div>
                        <div className={css.title}>
                            {intl.formatMessage({ id: 'home_page_pricing_section_luxury_title' })}
                        </div>
                        <div className={css.subtitle}>
                            {intl.formatMessage({ id: 'home_page_pricing_section_luxury_subtitle' })}
                        </div>
                        <div className={css.highlights}>
                            <div className={css.tagline}>{intl.formatMessage({ id: 'home_page_pricing_section_start_from' })}</div>
                            <div className={css.price}>10 USD</div>
                        </div>
                        <div className={css.footer}>
                            <div className={css.pricingTable}>
                                <div className={css.tableTitle}>
                                    {intl.formatMessage({ id: 'home_page_pricing_section_option_title' })}
                                </div>
                                <div className={css.pricingItem}>
                                    <div className={css.turnaroundTime}>30 {intl.formatMessage({ id: 'home_page_pricing_section_minutes' })}</div>
                                    <div className={css.price}>20 USD</div>
                                </div>
                                <div className={css.pricingItem}>
                                    <div className={css.turnaroundTime}>1 {intl.formatMessage({ id: 'home_page_pricing_section_hour' })}</div>
                                    <div className={css.price}>15 USD</div>
                                </div>
                                <div className={css.pricingItem}>
                                    <div className={css.turnaroundTime}>4 {intl.formatMessage({ id: 'home_page_pricing_section_hours' })}</div>
                                    <div className={css.price}>10 USD</div>
                                </div>
                            </div>
                            <div className={css.instruction}>
                                <div className={css.instructionTitle}>
                                    {intl.formatMessage({ id: 'home_page_pricing_section_service_title' })}
                                </div>
                                <div className={css.serviceItemGrid}>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_1' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_1' })}
                                    </div>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_2' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_2' })}
                                    </div>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_3' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_3' })}
                                    </div>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_4' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_4' })}
                                    </div>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_5' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_5' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className={css.solutionCard}>
                        <div className={css.iconImage}>
                            <img
                                src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-category-02.png`, { width: 140 })}
                                alt={intl.formatMessage({ id: 'home_page_pricing_section_sneaker_title' })}
                            />
                        </div>
                        <div className={css.title}>
                            {intl.formatMessage({ id: 'home_page_pricing_section_sneaker_title' })}
                        </div>
                        <div className={css.subtitle}>
                            {intl.formatMessage({ id: 'home_page_pricing_section_sneaker_subtitle' })}
                        </div>
                        <div className={css.highlights}>
                            <div className={css.tagline}>{intl.formatMessage({ id: 'home_page_pricing_section_start_from' })}</div>
                            <div className={css.price}>3 USD</div>
                        </div>
                        <div className={css.footer}>
                            <div className={css.pricingTable}>
                                <div className={css.tableTitle}>
                                    {intl.formatMessage({ id: 'home_page_pricing_section_option_title' })}
                                </div>
                                <div className={css.pricingItem}>
                                    <div className={css.turnaroundTime}>10 {intl.formatMessage({ id: 'home_page_pricing_section_minutes' })}</div>
                                    <div className={css.price}>5 USD</div>
                                </div>
                                <div className={css.pricingItem}>
                                    <div className={css.turnaroundTime}>15 {intl.formatMessage({ id: 'home_page_pricing_section_minutes' })}</div>
                                    <div className={css.price}>4 USD</div>
                                </div>
                                <div className={css.pricingItem}>
                                    <div className={css.turnaroundTime}>30 {intl.formatMessage({ id: 'home_page_pricing_section_minutes' })}</div>
                                    <div className={css.price}>3 USD</div>
                                </div>
                            </div>
                            <div className={css.instruction}>
                                <div className={css.instructionTitle}>
                                    {intl.formatMessage({ id: 'home_page_pricing_section_service_title' })}
                                </div>
                                <div className={css.serviceItemGrid}>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_1' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_1' })}
                                    </div>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_2' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_2' })}
                                    </div>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_3' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_3' })}
                                    </div>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_4' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_4' })}
                                    </div>
                                    <div className={css.serviceItem}>
                                        <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' alt={intl.formatMessage({ id: 'home_page_pricing_section_services_item_5' })} />
                                        {intl.formatMessage({ id: 'home_page_pricing_section_services_item_5' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <Link
                    href={'/pricing'}
                    className={css.sectionActionButton}
                >
                    {intl.formatMessage({ id: 'home_page_pricing_section_more_button_title' })} <ArrowRightOutlined />
                </Link>
            </AppContainer>
        </div >
    )
}

export default PricingSection

.PricingSection {
    padding: 48px 0;

    @include responsive('md') {
        padding: 96px 0;
    }

    .sectionContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .sectionHeader {
        padding: 48px 0 36px 0;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }
    }

    .solutionCardGrid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        column-gap: 24px;
        row-gap: 24px;
        margin-bottom: 24px;
        width: 100%;

        @include responsive('md') {
            margin-bottom: 48px;
            grid-template-columns: repeat(2, 1fr);
        }

        .solutionCard {
            min-height: 300px;
            padding: 24px;
            padding-top: 0px;
            color: $color-app-white;
            row-gap: 6px;
            border: 1px solid $color-separator-white-1;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

            @include responsive('md') {
                padding-top: 12px;
                padding: 40px;
            }

            .iconImage {
                width: 100%;
                max-width: 140px;
            }

            .title {
                font-weight: bold;
                width: 100%;
                text-align: center;
                font-size: 16px;
                line-height: 26px;

                @include responsive('md') {
                    font-size: 20px;
                    line-height: 30px;
                }
            }

            .subtitle {
                opacity: 0.6;
                margin-bottom: 12px;
                width: 100%;
                text-align: center;
                font-size: 14px;
                line-height: 24px;

                @include responsive('md') {
                    font-size: 16px;
                    line-height: 26px;
                    margin-bottom: 24px;
                }
            }

            .highlights {
                display: flex;
                flex-direction: column;
                align-items: center;
                row-gap: 0px;
                margin-bottom: 24px;

                .tagline {
                    font-size: 16px;
                    opacity: 0.6;

                    @include responsive('md') {
                        font-size: 20px;
                    }
                }

                .price {
                    font-size: 30px;
                    line-height: 40px;
                    font-weight: bold;

                    @include responsive('md') {
                        line-height: 60px;
                        font-size: 50px;
                    }

                }
            }

            .coverImage {
                min-height: 200px;
            }

            .footer {
                width: 100%;
                display: grid;
                grid-template-columns: repeat(1, 1fr);
                row-gap: 12px;
                column-gap: 24px;

                @include responsive('md') {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            .pricingTable {
                display: flex;
                flex-direction: column;
                row-gap: 6px;
                margin-bottom: 12px;
                height: 100%;

                @include responsive('md') {
                    padding-right: 24px;
                    border-right: 1px solid $color-separator-white-1;
                }

                .tableTitle {
                    opacity: 0.6;
                    margin-bottom: 6px;
                    font-size: 14px;
                    line-height: 24px;
                }

                .pricingItem {
                    display: flex;
                    align-items: center;
                    column-gap: 12px;
                    font-size: 14px;
                    justify-content: space-between;

                    .price {
                        font-weight: bold;
                    }

                    .turnaroundTime {}
                }
            }

            .instruction {
                display: flex;
                flex-direction: column;
                row-gap: 4px;
                font-size: 14px;
                // opacity: 0.6;

                .instructionTitle {
                    opacity: 0.6;
                    margin-bottom: 6px;
                }

                .serviceItemGrid {
                    display: flex;
                    flex-direction: column;
                    row-gap: 10px;
                }

                .serviceItem {
                    display: flex;
                    column-gap: 6px;
                    align-items: flex-start;
                    font-size: 14px;
                    line-height: 20px;

                    .checkIcon {
                        margin-top: 2px;
                        margin-right: 3px;
                        width: 16px;
                    }
                }
            }
        }
    }

    .sectionActionButton {
        cursor: pointer;
        padding: 10px 12px;
        background-color: $color-app-white;
        border-radius: 4px;
        color: #000;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        // justify-content: space-between;
        column-gap: 12px;
        width: 100%;
        font-size: 14px;
    
        @include responsive('md') {
          width: fit-content;
          font-size: 16px;
        }
    }
}
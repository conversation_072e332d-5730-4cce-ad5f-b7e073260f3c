.HomePageSectionHeader {
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 8px;

    @include responsive('md') {
        row-gap: 16px;
    }

    .subtitle {
        width: fit-content;
        color: #fff;
        font-size: 12px;
        line-height: 22px;
        font-weight: bold;
        text-align: center;
        background: linear-gradient(270deg,
                rgb(89, 119, 255) 0%,
                rgb(49, 255, 215) 101.39%);
        background-clip: text;
        -webkit-text-fill-color: transparent;

        @include responsive('md') {
            margin-bottom: 12px;
            font-size: 20px;
        }
    }

    .title {
        width: fit-content;
        color: #fff;
        font-size: 20px;
        text-align: center;
        font-weight: 800;
        text-transform: uppercase;

        @include responsive('md') {
            font-size: 40px;
            line-height: 40px;
        }
    }

    .description {
        width: fit-content;
        color: #fff;
        font-size: 14px;
        text-align: center;
        opacity: 0.6;
        max-width: 700px;
        line-height: 24px;

        @include responsive('md') {
            font-size: 16px;
            line-height: 26px;
        }
    }

    @media print {
        .subtitle {
            -webkit-text-fill-color: #000;
        }

        .title {
            color: #000;
        }

        .description {
            color: #000;
        }
    }
}

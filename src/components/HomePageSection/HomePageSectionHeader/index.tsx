import clsx from 'clsx'
import css from './HomePageSectionHeader.module.scss'

type HomePageSectionHeaderProps = {
    className?: string
    title?: string
    subtitle?: string
    tagline?: string
    description?: any
}

const HomePageSectionHeader = (props: HomePageSectionHeaderProps) => {
    if (!props.title) {
        return null
    }
    return (
        <div className={clsx(props.className, css.HomePageSectionHeader)}>
            {
                !!props.subtitle
                    ? (
                        <h3 className={css.subtitle}>
                            {props.subtitle}
                        </h3>
                    )
                    : null
            }
            {
                !!props.title
                    ? (
                        <h2 className={css.title}>
                            {props.title}
                        </h2>
                    )
                    : null
            }
            {/* {
                !!props.subtitle
                    ? (
                        <div className={css.subtitle}>
                            {props.subtitle}
                        </div>
                    )
                    : null
            } */}
            {
                !!props.description
                    ? (
                        <div className={css.description}>
                            {props.description}
                        </div>
                    )
                    : null
            }
        </div>
    )
}

export default HomePageSectionHeader

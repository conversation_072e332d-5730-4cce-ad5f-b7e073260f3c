.ResourceSection {
    padding: 48px 0;
    margin-bottom: 48px;

    @include responsive('md') {
        padding: 96px 0;
        margin-bottom: 96px;
    }

    .sectionContainer {}

    .sectionHeader {
        padding: 48px 0 36px 0;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }
    }

    .resourceCardGrid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        column-gap: 24px;
        row-gap: 24px;

        @include responsive('md') {
            grid-template-columns: repeat(3, 1fr);
        }

        .resourceCard {
            border: 1px solid $color-separator-white-2;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            overflow: hidden;
            position: relative;
            height: 500px;
            display: flex;
            flex-direction: column-reverse;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;

            video {
              position: absolute;
              z-index: 1;
              inset: 0;
              object-fit: cover;
              pointer-events: none;
            }

            &.card1 {
              &::after {
	              content: "";
                position: absolute;
                inset: 0;
                z-index: 1;
                background-image: linear-gradient(to top, black, rgba(0, 0, 0, 40%) 60%, transparent 100%);
              }

              .cardInformation {
                background: none;
                position: relative;
                z-index: 2;
              }
            }

            .cardInformation {
                position: relative;
                display: flex;
                flex-direction: column;
                row-gap: 6px;
                padding: 24px;
                background-image: linear-gradient(to top, black, rgba(0, 0, 0, 70%) 70%, transparent 100%);

                @include responsive('md') {
                    padding: 36px;
                }

                .title {
                    font-weight: bold;
                    font-size: 16px;
                    line-height: 26px;
                }

                .subtitle {
                    font-size: 14px;
                    opacity: 0.6;
                    line-height: 24px;
                }

                .link {
                    width: fit-content;
                    font-size: 14px;
                    display: flex;
                    column-gap: 6px;

                    .arrowIcon {
                        font-size: 12px;
                    }
                }
            }
        }
    }
}

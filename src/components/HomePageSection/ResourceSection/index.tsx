import clsx from 'clsx'
import { ArrowRightOutlined } from '@ant-design/icons'
import AppContainer from 'components/AppContainer'
import AppVideoBackground from 'components/AppVideoBackground'
import { APP_AUTHCLASS_URL, APP_DISCORD_URL } from 'constants/app'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import resizeImageUrl from 'utils/resizeImageUrl'
import HomePageSectionHeader from '../HomePageSectionHeader'
import css from './ResourceSection.module.scss'

const ResourceSection = () => {
    const router = useRouter()
    const { locale } = router
    const intl = useIntl()

    return (
        <div className={css.ResourceSection}>
            <AppContainer className={css.sectionContainer}>
                <HomePageSectionHeader
                    className={css.sectionHeader}
                    title={intl.formatMessage({ id: 'home_page_resource_section_title' })}
                    subtitle={intl.formatMessage({ id: 'home_page_resource_section_subtitle' })}
                    description={<>
                        {intl.formatMessage({ id: 'home_page_resource_section_description' })}
                    </>}
                />
                <div className={css.resourceCardGrid}>
                    <a href={APP_AUTHCLASS_URL} target='_blank' rel='noopener noreferrer'
                        className={clsx(css.resourceCard, css.card1)}>
                        <AppVideoBackground
                            src='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-resources-1.mp4'
                            poster='https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-resources-1.jpg'
                        />
                        <div className={css.cardInformation}>
                            <div className={css.title}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_1_title' })}
                            </div>
                            <div className={css.subtitle}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_1_description' })}
                            </div>
                            <div className={css.link}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_3_link' })} <ArrowRightOutlined className={css.arrowIcon} />
                            </div>
                        </div>
                    </a>
                    <Link
                        href='/blog'
                        className={css.resourceCard}
                        style={{
                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-resources-2.jpg`, { width: 1000 })})`
                        }}
                    >
                        <div className={css.cardInformation}>
                            <div className={css.title}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_2_title' })}
                            </div>
                            <div className={css.subtitle}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_2_description' })}
                            </div>
                            <div className={css.link}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_2_link' })} <ArrowRightOutlined className={css.arrowIcon} />
                            </div>
                        </div>
                    </Link>
                    <a
                        href={APP_DISCORD_URL}
                        className={css.resourceCard}
                        target='_blank' rel='noopener noreferrer'
                        style={{
                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-resources-3.jpg`, { width: 1000 })})`
                        }}
                    >
                        <div className={css.cardInformation}>
                            <div className={css.title}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_3_title' })}
                            </div>
                            <div className={css.subtitle}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_3_description' })}
                            </div>
                            <div className={css.link}>
                                {intl.formatMessage({ id: 'home_page_resource_section_item_3_link' })} <ArrowRightOutlined className={css.arrowIcon} />
                            </div>
                        </div>
                    </a>
                </div>
            </AppContainer>
        </div>
    )
}

export default ResourceSection

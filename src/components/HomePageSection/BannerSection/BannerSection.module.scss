.BannerSection {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  overflow: hidden;
  position: relative;
  overflow: hidden;
  // margin-top: -1px;
  // border-top: 1px solid $color-separator-white-1;
  // border-bottom: 1px solid $color-separator-white-1;

  @include responsive('md') {
    min-height: 500px;
    height: calc(80vh - $app-header-height);
    max-height: 700px;

    &:hover {
      .banner-overlay {
        opacity: 0.75;
      }
    }
  }

  .bannerContainerGrid {
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;

    @include responsive('sm') {
      // grid-template-columns: 1fr;
    }

    @include responsive('md') {
      grid-template-columns: 10fr 14fr;
    }

    .bannerImagePart {
      // height: 100%;
      // min-height: 300px;
      // background-color: green;
      min-height: 300px;

      @include responsive('md') {}

      .bannerImageContainerGrid {
        height: 100%;
        display: grid;
        grid-template-columns: 5fr 4fr 5fr;
        column-gap: 12px;
        padding: 12px;

        .bannerImageSeparator {
          background-color: $color-separator-white-1;
        }

        .bannerImageColumn1 {
          display: grid;
          grid-template-rows: 4fr 2fr;
          row-gap: 12px;
        }

        .bannerImageColumn2 {
          display: grid;
          grid-template-rows: 2fr 3fr;
          row-gap: 12px;
        }

        .bannerImageContainer {
          height: 100%;
          background-color: #191c21;
          overflow: hidden;
          border-radius: $border-radius-theme-1;
          border: 1px solid $color-separator-white-1;

          .bannerImage {
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            height: 100%;
            // min-height: 200px;

            @include responsive('md') {
              // min-height: 600px;
              transition: 0.3s;

              &:hover {
                transform: scale(1.05);
              }
            }
          }
        }
      }
    }

    .bannerInformationPart {
      // height: 100%;
      padding: 48px 16px;
      display: flex;
      flex-direction: column-reverse;
      align-items: center;
      // justify-content: center;
      // background-color: blue;

      @include responsive('md') {
        padding: 12px 48px;
        // padding-bottom: 0px;
        align-items: flex-start;
      }

      .bannerTitle {
        color: #fff;
        font-size: 30px;
        line-height: 35px;
        font-weight: bold;
        text-transform: uppercase;
        text-align: center;
        margin-bottom: 6px;

        @include responsive('md') {
          text-align: left;
          font-size: 34px;
          line-height: 43px;
          margin-bottom: 12px;
        }
      }


      .bannerSubtitleContainer {
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-bottom: 24px;

        @include responsive('md') {
          align-items: flex-start;
        }

        .bannerSubtitle {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          text-align: center;
          font-weight: bold;
          margin-bottom: 6px;
          text-transform: uppercase;
          max-width: 300px;

          @include responsive('md') {
            max-width: unset;
            text-align: left;
            font-size: 16px;
          }
        }

        .bannerTagline {
          color: rgba(255, 255, 255, 0.6);
          font-size: 14px;
          text-align: center;
          max-width: 280px;

          @include responsive('md') {
            text-align: left;
            max-width: unset;
          }
        }
      }

      .appIcon {
        margin: 0 0 24px 0;
        width: 60px;
        height: 60px;
        border-radius: 17.5%;
        // box-shadow: rgba(255,255,255,0.8) 0 0 10px;
        border: 1px solid #fff;

        @include responsive('md') {
          width: 70px;
          height: 70px;
        }
      }
    }

    .downloadInformation {
      display: flex;
      column-gap: 12px;
      max-width: 500px;
      align-items: center;
      justify-content: center;
      padding: 0 24px;
      margin-bottom: 12px;
      width: fit-content;

      @include responsive('md') {
        column-gap: 12px;
        padding: 0 0px;
        align-items: flex-start;
        justify-content: flex-start;
      }

      .downloadButtonLink {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        transition: 0.3s;
        margin-bottom: 12px;

        @include responsive('md') {
          width: fit-content;
          align-items: flex-start;
          justify-content: flex-start;
        }

        &:hover {
          transform: translateY(-3px);
        }

        .downloadButton {
          // margin: 0 6px 12px 6px;
          width: 100%;
          max-width: 160px;
          // height: 40px;
          // border: 1px solid #6c757d;
          overflow: hidden;

          @include responsive('md') {
            max-width: 160px;
            // width: 80%;
          }
        }
      }
    }

    .additionalInformation {
      color: #fff;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      column-gap: 0px;

      @include responsive('md') {
        column-gap: 24px;
      }

      .reviewPart {
        border-right: 1px solid #6c757d;
        padding-right: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;

        @include responsive('md') {
          padding-right: 24px;
          align-items: flex-start;
        }

        .reviewTitle {
          display: flex;
          column-gap: 6px;
          align-items: center;

          .reviewRate {
            font-size: 24px;
            line-height: 30px;
            font-weight: bold;
          }

          .reviewStars {
            font-size: 16px;
            color: #ffc107;
            display: flex;
            column-gap: 2px;
          }
        }

        .reviewSubtitle {
          opacity: 0.6;
          text-align: center;
          font-size: 14px;

          @include responsive('md') {
            text-align: left;
            font-size: 16px;
          }
        }
      }

      .downloadPart {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-left: 12px;

        @include responsive('md') {
          align-items: flex-start;
          padding-left: 0px;
        }

        .downloadTitle {
          font-size: 24px;
          font-weight: bold;
          line-height: 30px;
          display: flex;
          column-gap: 6px;
          align-items: baseline;

          span {
            font-size: 20px;
          }
        }

        .downloadSubtitle {
          opacity: 0.6;
          text-align: center;
          font-size: 14px;

          @include responsive('md') {
            text-align: left;
            font-size: 16px;
          }
        }
      }
    }

  }
}
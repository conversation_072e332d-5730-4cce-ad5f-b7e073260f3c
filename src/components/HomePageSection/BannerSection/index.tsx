import {
  StarFilled,
} from '@ant-design/icons'
import { APP_APP_STORE_URL, APP_GOOGLE_PLAY_URL } from 'constants/app'
import { motion, useAnimation } from 'framer-motion'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { useInView } from 'react-intersection-observer'
import { useIntl } from 'react-intl'
import { tagDownloadAndroidAppEvent, tagDownloadiOSAppEvent } from 'utils/gtag'
import getImageUrl from 'utils/imageUrl'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './BannerSection.module.scss'

const BannerSection = () => {
  const router = useRouter()
  const intl = useIntl()
  const fadeInAnimation = useAnimation()
  const { ref, inView } = useInView({
    threshold: 0.5,
    triggerOnce: true,
  })

  const startAnimation = async () => {
    await fadeInAnimation.start((i) => ({
      opacity: 1,
      y: 0,
      transition: { delay: i * 0.3, ease: 'easeInOut', duration: 0.4 },
    }))
  }

  useEffect(() => {
    if (!inView) {
      return
    }
    startAnimation()
  }, [inView])

  return (
    <>
      <div className={css.BannerSection}>
        <div ref={ref} />
        <div className={css.bannerContainerGrid}>
          <div className={css.bannerInformationPart}>
            <motion.div
              className={css.additionalInformation}
              animate={fadeInAnimation}
              initial={{
                opacity: 0,
              }}
              custom={3}
            >
              <div className={css.reviewPart}>
                <div className={css.reviewTitle}>
                  <div className={css.reviewRate}>4.9</div>
                  <div className={css.reviewStars}><StarFilled /><StarFilled /><StarFilled /><StarFilled /><StarFilled /></div>
                </div>
                <div className={css.reviewSubtitle}>
                  {intl.formatMessage({ id: 'home_page_hero_section_review_subtitle' })}
                </div>
              </div>
              <div className={css.downloadPart}>
                <div className={css.downloadTitle}>
                  3 <span>{intl.formatMessage({ id: 'home_page_hero_section_case_title_million_plus' })}</span>
                </div>
                <div className={css.downloadSubtitle}>
                  {intl.formatMessage({ id: 'home_page_hero_section_case_subtitle' })}
                </div>
              </div>
            </motion.div>
            <motion.div
              className={css.downloadInformation}
              animate={fadeInAnimation}
              initial={{
                opacity: 0,
              }}
              custom={3}
            >
              <a onClick={tagDownloadiOSAppEvent} className={css.downloadButtonLink} href={APP_APP_STORE_URL} target='_blank' rel='noopener noreferrer'>
                <img className={css.downloadButton} src='/badge-app-store-blue.svg' alt='App Store' />
              </a>
              <a onClick={tagDownloadAndroidAppEvent} className={css.downloadButtonLink} href={APP_GOOGLE_PLAY_URL} target='_blank' rel='noopener noreferrer'>
                <img className={css.downloadButton} src='badge-google-play-blue.svg' alt='Google Play' />
              </a>
            </motion.div>
            <div className={css.bannerSubtitleContainer}>
              <motion.h2
                className={css.bannerSubtitle}
                animate={fadeInAnimation}
                initial={{
                  opacity: 0,
                }}
                custom={3}
              >
                {intl.formatMessage({ id: 'home_page_hero_section_subtitle' })}
              </motion.h2>
              <motion.h3
                className={css.bannerTagline}
                animate={fadeInAnimation}
                initial={{
                  opacity: 0,
                }}
                custom={3}
              >
                {intl.formatMessage({ id: 'home_page_hero_section_description' })}
              </motion.h3>
            </div>
            <motion.h2
              className={css.bannerTitle}
            >
              <motion.span
                animate={fadeInAnimation}
                initial={{
                  opacity: 0,
                }}
                custom={1}
              >
                {`${intl.formatMessage({ id: 'home_page_hero_section_title_1' })} `}
              </motion.span>
              <br />
              <motion.span
                animate={fadeInAnimation}
                initial={{
                  opacity: 0,
                }}
                custom={2}
              >
                {intl.formatMessage({ id: 'home_page_hero_section_title_2' })}
              </motion.span>
              <br />
              {/* <motion.span
                animate={fadeInAnimation}
                initial={{
                  opacity: 0,
                }}
                custom={2}
              >
                {intl.formatMessage({ id: 'home_page_hero_section_title_3' })}
              </motion.span> */}
            </motion.h2>
            <motion.div
              animate={fadeInAnimation}
              initial={{
                opacity: 0,
              }}
              custom={0}
            >
              <img className={css.appIcon} src='/logo-legit-app.png' alt='LEGIT APP' />
            </motion.div>
          </div>
          <div className={css.bannerImagePart}>
            <div className={css.bannerImageContainerGrid}>
              <motion.div
                animate={fadeInAnimation}
                initial={{
                  opacity: 0,
                }}
                custom={4}
                className={css.bannerImageContainer}
              >
                <div
                  className={css.bannerImage}
                  style={{
                    backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-banner-handbag-optimised.jpg`), { width: 250 })})`,
                  }}
                />
              </motion.div>
              <div className={css.bannerImageColumn1}>
                <motion.div
                  animate={fadeInAnimation}
                  initial={{
                    opacity: 0,
                  }}
                  custom={5}
                  className={css.bannerImageContainer}
                >
                  <div
                    className={css.bannerImage}
                    style={{
                      backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-banner-clothes.png`), { width: 300 })})`,
                    }}
                  />
                </motion.div>
                <motion.div
                  animate={fadeInAnimation}
                  initial={{
                    opacity: 0,
                  }}
                  custom={6}
                  className={css.bannerImageContainer}
                >
                  <div
                    className={css.bannerImage}
                    style={{
                      backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-banner-jewellery.png`), { width: 300 })})`,
                    }}
                  />
                </motion.div>
              </div>
              <div className={css.bannerImageColumn2}>
                <motion.div
                  animate={fadeInAnimation}
                  initial={{
                    opacity: 0,
                  }}
                  custom={6}
                  className={css.bannerImageContainer}
                >
                  <div
                    className={css.bannerImage}
                    style={{
                      backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-banner-watches.png`), { width: 300 })})`,
                    }}
                  />
                </motion.div>
                <motion.div
                  animate={fadeInAnimation}
                  initial={{
                    opacity: 0,
                  }}
                  custom={5}
                  className={css.bannerImageContainer}
                >
                  <div
                    className={css.bannerImage}
                    style={{
                      backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-banner-sneakers.jpg`), { width: 300 })})`,
                    }}
                  />
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default BannerSection

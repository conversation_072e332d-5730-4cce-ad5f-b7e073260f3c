.IntroductionSection {
  background: #000000;
  padding: 24px 0 24px 0;

  @include responsive('md') {
    padding: 48px 0 48px 0;
  }

  :global {
    // border-top: 1px solid #000;
    .container {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .section-header {
        margin-bottom: 48px;

        .section-title {
          font-size: 28px;
          line-height: 34px;
          font-weight: 500;
          color: #fff;
          text-align: center;
          margin-bottom: 24px;
          padding: 0 24px;

          @include responsive('md') {
            font-size: 40px;
          }
        }

        .section-subtitle {
          font-size: 16px;
          padding: 0 24px;
          text-align: center;
          color: #fff;
          opacity: 0.7;
        }
      }

      .section-content {

        display: flex;
        flex-direction: column;
        align-items: center;

        @media only screen and (min-width: 1200px) {
          flex-direction: row;
        }

        img.product-image {
          // max-width: 700px;
          // height: 500px;
          width: 80%;
          margin-bottom: 24px;

          @media only screen and (min-width: 1200px) {
            height: unset;
            margin-right: 48px;
            max-width: 600px;
            width: 100%;
            margin-bottom: 0;
            // height: 500px;
          }
        }

        .introduction-card {
          display: flex;
          align-items: center;
          border: .5px solid #e5e9f2;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .1);
          border-radius: 5px;
          padding: 24px;
          background: #000000;
          transition: all 0.25s ease-in;
          text-align: center;
          margin-bottom: 12px;

          @include responsive('md') {
            margin-bottom: 24px;
            height: 100%;

            &:hover {
              transform: translate(0px, -5px);
            }
          }

          .card-image {
            height: 50px;
            margin-right: 24px;
          }

          .card-content {
            text-align: left;

            .card-title {
              font-size: 18px;
              line-height: 22px;
              font-weight: 600;
              margin-bottom: 12px;
              // color: rgba(0, 0, 0, 0.84);
              color: #fff;
            }

            .card-subtitle {
              font-size: 14px;
              color: #fff;
              opacity: 0.7;

              @include responsive('md') {
                font-size: 16px;
              }
            }
          }

        }

      }
    }
  }
}
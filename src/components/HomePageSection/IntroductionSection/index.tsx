import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import css from './IntroductionSection.module.scss'

const INTRODUCTION_ITEMS = [
  {
    id: 1,
    title: 'home_page_introduction_section_title_1',
    description: 'home_page_introduction_section_description_1',
    icon_image_url: '/icon-home-introduction-1.png',
  },
  {
    id: 2,
    title: 'home_page_introduction_section_title_2',
    description: 'home_page_introduction_section_description_2',
    icon_image_url: '/icon-home-introduction-2.png',
  },
  {
    id: 3,
    title: 'home_page_introduction_section_title_3',
    description: 'home_page_introduction_section_description_3',
    icon_image_url: '/icon-home-introduction-3.png',
  },
]

const IntroductionSection = () => {
  const router = useRouter()
  const intl = useIntl()
  return (
    <div className={css.IntroductionSection}>
      <div className='container'>
        <div className='section-header'>
          <div className='section-title'>
            {intl.formatMessage({ id: 'home_page_introduction_section_title' })}
          </div>
          <div className='section-subtitle'>
            {intl.formatMessage({ id: 'home_page_introduction_section_subtitle' })}
          </div>
        </div>
        <div className='section-content'>
          <img
            className='product-image'
            src='/icon-home-introduction-phone.png'
            alt='legitapp product'
          />
          <div className='introduction-card-part'>
            {
              INTRODUCTION_ITEMS.map(item => (
                <div
                  className='introduction-card'
                  key={`introduction-card-col-${item.id}`}
                >
                  <img
                    className='card-image'
                    src={item.icon_image_url}
                    alt={item.title}
                  />
                  <div className='card-content'>
                    <div className='card-title'>{intl.formatMessage({ id: item.title })}</div>
                    <div className='card-subtitle'>{intl.formatMessage({ id: item.description })}</div>
                  </div>
                </div>
              ))
            }
          </div>
        </div>
      </div>
    </div>
  )
}

export default IntroductionSection

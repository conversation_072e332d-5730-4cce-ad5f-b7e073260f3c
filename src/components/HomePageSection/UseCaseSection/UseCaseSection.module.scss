.UseCaseSection {
    padding: 48px 0;

    @include responsive('md') {
        padding: 96px 0;
    }

    .sectionContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .sectionHeader {
        padding: 48px 0 36px 0;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }
    }

    .sectionContentGrid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        align-items: center;
        row-gap: 48px;

        @include responsive ('md') {
            grid-template-columns: repeat(2, 1fr);
        }

        .coverImage {
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                width: 100%;
                max-width: 600px;
            }
        }

        .useCaseCard {
            padding: 24px;
            padding-top: 36px;
            color: $color-app-white;
            row-gap: 12px;
            border: 1px solid $color-separator-white-1;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: fit-content;
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

            @include responsive('md') {
                align-items: flex-start;
                padding: 40px;
            }

            img {
                max-width: 200px;
                width: 100%;
                margin-bottom: 12px;

                @include responsive('md') {
                    margin-bottom: 24px;
                }
            }

            .title {
                font-weight: bold;
                width: 100%;
                text-align: center;
                font-size: 16px;
                line-height: 26px;

                @include responsive('md') {
                    font-size: 20px;
                    line-height: 30px;
                    text-align: unset;
                }
            }

            .description {
                width: 100%;
                color: #fff;
                font-size: 14px;
                opacity: 0.6;
                max-width: 700px;
                line-height: 24px;
                margin-bottom: 12px;
                text-align: center;

                @include responsive('md') {
                    text-align: unset;
                    font-size: 16px;
                    line-height: 26px;
                }
            }
        }
    }

    .sectionActionButton {
        cursor: pointer;
        padding: 10px 12px;
        background-color: $color-app-white;
        border-radius: 4px;
        color: #000;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        // justify-content: space-between;
        column-gap: 12px;
        width: 100%;
        font-size: 14px;

        @include responsive('md') {
            width: fit-content;
            font-size: 16px;
        }
    }
}
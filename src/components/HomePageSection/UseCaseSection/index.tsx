import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import { useAnimation } from 'framer-motion'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import css from './UseCaseSection.module.scss'
import HomePageSectionHeader from '../HomePageSectionHeader'
import Link from 'next/link'
import { ArrowRightOutlined } from '@ant-design/icons'
import resizeImageUrl from 'utils/resizeImageUrl'

const UseCaseSection = () => {
    const router = useRouter()
    const intl = useIntl()
    const fadeInAnimation = useAnimation()
    // const { ref, inView } = useInView({
    //   threshold: 0.5,
    //   triggerOnce: true,
    // })

    // const startAnimation = async () => {
    //   await fadeInAnimation.start((i) => ({
    //     opacity: 1,
    //     y: 0,
    //     transition: { delay: i * 0.5, ease: 'easeInOut', duration: 0.5 },
    //   }))
    // }

    // useEffect(() => {
    //   if (!inView) {
    //     return
    //   }
    //   startAnimation()
    // }, [inView])

    return (
        <div className={css.UseCaseSection}>
            <AppContainer className={css.sectionContainer}>
                <HomePageSectionHeader
                    className={css.sectionHeader}
                    title={intl.formatMessage({ id: 'home_page_business_section_title' })}
                    subtitle={intl.formatMessage({ id: 'home_page_business_section_subtitle' })}
                    description={<>
                        {intl.formatMessage({ id: 'home_page_business_section_description_1' })} {intl.formatMessage({ id: 'home_page_business_section_description_2' })}
                    </>}
                />
                <div className={css.sectionContentGrid}>
                    <div className={css.coverImage}>
                        <img
                            src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-use-case-2.png`, { width: 1000 })}
                            alt={'LEGIT APP X PopChill Partnership'}
                        />
                    </div>
                    <div className={css.useCaseCard}>
                        <img
                            src={resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-home-use-case-1.png`, { width: 400 })}
                            alt={'LEGIT APP X PopChill Partnership'}
                        />
                        <div className={css.title}>
                            {intl.formatMessage({ id: 'home_page_business_section_item_1_title' })}
                        </div>
                        <div className={css.description}>
                            {intl.formatMessage({ id: 'home_page_business_section_item_1_description' })}
                        </div>
                        <Link
                            href='/contact'
                            className={css.sectionActionButton}
                            target='_top'
                        >
                            {intl.formatMessage({ id: 'home_page_business_section_more_button_title' })}<ArrowRightOutlined />
                        </Link>
                    </div>
                </div>
            </AppContainer>
        </div >
    )
}

export default UseCaseSection

import React from "react";
import { useIntl } from "react-intl";
import Link from "next/link";
import Image from "next/image";
import clsx from "clsx";
import { useRouter } from "next/router";

import { menuItems } from "components/StartAuthentication/constant";

const SlideMenu = ({ className }: { className?: string }) => {
  const intl = useIntl();
  const router = useRouter();
  const { route } = router;
  return (
    <div className={clsx("flex gap-10 flex-col", className)}>
      {menuItems.map((menu) => (
        <Link
          key={menu.href}
          className={clsx("text-white flex gap-4 cursor-pointer", {
            "opacity-[31%]": route !== menu.href,
          })}
          href={menu.href}
        >
          <div>
            <Image
              src={menu.icon}
              alt={menu.title}
              width={20}
              height={20}
              className="w-5 h-5"
            />
          </div>
          <div className="font-semibold">
            {intl.formatMessage({
              id: `start_authentication_page_menu_${menu.title}`,
            })}
          </div>
        </Link>
      ))}
    </div>
  );
};

export default SlideMenu;

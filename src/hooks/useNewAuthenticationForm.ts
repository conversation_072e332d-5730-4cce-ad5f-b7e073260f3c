import { RootState } from "reducers";
import {
  updateNewAuthenticationForm,
  resetNewAuthenticationForm,
  setServiceSet,
  setCurrentServiceLevel,
  setCurrentAdditionalServiceList,
  setUploadPhotoList,
  updateUploadPhotoList,
  setCertificateOwnerName,
  setFormFields,
  setUserCustomCode,
  setUserRemark,
  setProductTitle,
  setCategoryBrandModel,
} from "actions/newAuthenticationForm";
import {
  INewAuthenticationFormState,
  IServiceExtraService,
  IUploadPhotoItem,
  IServicePlaceholder,
  IProductTitle,
} from "types/orders";
import { IUser } from "types/app";
import useAppDispatch from "./useAppDispatch";
import useAppSelector from "./useAppSelector";

export const useNewAuthenticationForm = () => {
  const dispatch = useAppDispatch();
  const formState = useAppSelector(
    (state: RootState) => state.newAuthenticationForm
  );

  const updateForm = (payload: Partial<INewAuthenticationFormState>) => {
    dispatch(updateNewAuthenticationForm(payload));
  };

  const resetForm = () => {
    dispatch(resetNewAuthenticationForm());
  };

  const actions = {
    setCategoryBrandModel: (payload: {
      category_id: string;
      brand_id: string;
      model_id: string;
    }) => dispatch(setCategoryBrandModel(payload)),
    setServiceSet: (serviceSet: INewAuthenticationFormState["serviceSet"]) =>
      dispatch(setServiceSet(serviceSet)),
    setCurrentServiceLevel: (
      level: INewAuthenticationFormState["currentServiceLevel"]
    ) => dispatch(setCurrentServiceLevel(level)),
    setCurrentAdditionalServiceList: (
      list: INewAuthenticationFormState["currentAdditionalServiceList"]
    ) => dispatch(setCurrentAdditionalServiceList(list)),
    setUploadPhotoList: (
      listOrUpdater:
        | INewAuthenticationFormState["uploadPhotoList"]
        | ((
            prevList: INewAuthenticationFormState["uploadPhotoList"]
          ) => INewAuthenticationFormState["uploadPhotoList"])
    ) => {
      if (typeof listOrUpdater === "function") {
        dispatch(updateUploadPhotoList(listOrUpdater));
      } else {
        dispatch(setUploadPhotoList(listOrUpdater));
      }
    },
    setCertificateOwnerName: (name: string) =>
      dispatch(setCertificateOwnerName(name)),
    setUserCustomCode: (code: string) => dispatch(setUserCustomCode(code)),
    setUserRemark: (remark: string) => dispatch(setUserRemark(remark)),
    setProductTitle: (title: IProductTitle) => dispatch(setProductTitle(title)),
    setFormFields: (fields: Parameters<typeof setFormFields>[0]) =>
      dispatch(setFormFields(fields)),
  };

  const validateRequiredPhotos = () => {
    const { serviceSet, uploadPhotoList } = formState;

    if (!serviceSet?.service_placeholder) {
      return { isValid: true, missingPhotos: [] };
    }

    const requiredPhotos = serviceSet.service_placeholder.filter(
      (item: IServicePlaceholder) => item.required === 1
    );

    const missingPhotos = requiredPhotos.filter(
      (requiredPhoto: IServicePlaceholder) => {
        return !uploadPhotoList.some(
          (uploadedPhoto: IUploadPhotoItem) =>
            uploadedPhoto.system_image_remark.service_placeholder_id ===
            requiredPhoto.id
        );
      }
    );

    return {
      isValid: missingPhotos.length === 0,
      missingPhotos,
      totalRequired: requiredPhotos.length,
      totalUploaded: requiredPhotos.length - missingPhotos.length,
    };
  };

  const validateBalance = (user: IUser | null) => {
    const { currentServiceLevel, currentAdditionalServiceList } = formState;

    if (!user || !currentServiceLevel) {
      return { isValid: true, message: "" };
    }

    const authServiceCost = Number(currentServiceLevel.credit || 0);

    const additionalServicesCost = currentAdditionalServiceList?.reduce(
      (total: number, service: IServiceExtraService) =>
        total + Number(service.credit || 0),
      0
    );

    const totalCost = authServiceCost + additionalServicesCost;
    const userBalance = Number(user.credit_balance || 0);

    if (userBalance < totalCost) {
      return {
        isValid: false,
        message: `Insufficient balance. Required: ${totalCost.toFixed(
          2
        )} $LEGIT Token, Available: ${userBalance.toFixed(2)} $LEGIT Token`,
        totalCost,
        userBalance,
      };
    }

    return {
      isValid: true,
      message: "",
      totalCost,
      userBalance,
    };
  };

  const getSubmitData = () => {
    const {
      category_id,
      brand_id,
      model_id,
      certificateOwnerName,
      user_custom_code,
      product_source_currency,
      currentServiceLevel,
      currentAdditionalServiceList,
      user_remark,
      uploadPhotoList,
      serviceSet,
      product_title,
    } = formState;

    return {
      service_set_id: serviceSet?.id,
      category_id: Number(category_id),
      brand_id: Number(brand_id),
      model_id: Number(model_id),
      certificate_owner_name: certificateOwnerName,
      user_custom_code: user_custom_code || null,
      product_source_currency: product_source_currency || "USD",
      product_title:
        product_title?.brand_title + " - " + product_title?.model_title,
      service_level_id: currentServiceLevel?.id,
      service_extra_service_ids: currentAdditionalServiceList?.map(
        (item: IServiceExtraService) => item.id
      ),
      user_remark: user_remark || null,
      images: uploadPhotoList?.map((item: IUploadPhotoItem) => ({
        ...item,
        system_image_remark: JSON.stringify(item.system_image_remark),
      })),
    };
  };

  return {
    ...formState,
    updateForm,
    resetForm,
    ...actions,
    getSubmitData,
    validateRequiredPhotos,
    validateBalance,
  };
};

export default useNewAuthenticationForm;

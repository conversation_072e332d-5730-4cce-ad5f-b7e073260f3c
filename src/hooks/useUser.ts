import { useEffect } from "react";
import { RootState } from "reducers";
import { fetchUser } from "actions/app";
import useAppSelector from "./useAppSelector";
import { useAppDispatch } from "./useAppDispatch";

const useUserHook = () => {
  const appState: any = useAppSelector((state: RootState) => state.app);
  const dispatch = useAppDispatch();

  const { accessToken, user, isFetchUserLoading } = appState;

  useEffect(() => {
    if (accessToken && !user) {
      dispatch(fetchUser({ accessToken }));
    }
  }, [accessToken, user, dispatch]);

  return {
    accessToken,
    user,
    isFetchUserLoading,
  };
};

export default useUserHook;

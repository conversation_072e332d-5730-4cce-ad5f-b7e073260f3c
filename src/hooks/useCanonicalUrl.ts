import { APP_URL } from "constants/app";
import { useRouter } from "next/router";
import removeTrailingSlash from "utils/removeTrailingSlash";

const useCanonicalUrl = () => {
    const router = useRouter()
    const { locale, defaultLocale, asPath } = router
    const cleanPath = asPath.split('#')[0].split('?')[0];
    const canonicalUrl = `${APP_URL}` + (locale === defaultLocale ? '' : `/${locale}`) + (asPath === '/' ? '' : cleanPath);
    return removeTrailingSlash(canonicalUrl)
}

export default useCanonicalUrl
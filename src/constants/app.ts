import getConfig from "next/config";

// import { isDevelopmentServer } from 'utils'
const { publicRuntimeConfig } = getConfig();
const { APP_URL: APP_CONFIG_URL } = publicRuntimeConfig;

export const APP_VERSION = "0.0.0";
export const APP_NAME = "LEGIT APP Authentication";
export const APP_DESCIPTION =
  "LEGIT APP is your fast & globally trusted authentication solution for luxury handbags, sneakers, watches and designer products. Powered by a great team of expert authenticators and cutting-edge AI technology.";
export const APP_OG_IMAGE_URL =
  "https://legitapp-static.oss-accelerate.aliyuncs.com/og-legitapp.png";
export const APP_URL = APP_CONFIG_URL || "https://legitapp.com";
export const APP_TWITTER_URL = "https://twitter.com/legitappcom";
export const APP_DISCORD_URL = "https://discord.gg/legitapp";
export const APP_TELEGRAM_URL = "";
export const APP_FACEBOOK_URL = "https://www.facebook.com/legitappcom";
export const APP_TIKTOK_URL = "https://www.tiktok.com/@legitappcom";
export const APP_INSTAGRAM_URL = "https://instagram.com/legitappcom";
export const APP_THREADS_URL = "https://www.threads.net/@legitappcom";
export const APP_TRUSTPILOT_URL =
  "https://www.trustpilot.com/review/legitapp.com";
export const APP_MEDIUM_URL = "";
export const APP_PINTEREST_URL = "https://www.pinterest.com/legitappcom/";
export const APP_TUMBLR_URL = "https://www.tumblr.com/legitappcom";
export const APP_WHATSAPP_URL = "https://wa.me/message/O4CMT3MYV5DMF1";
export const APP_LINKEDIN_URL = "https://www.linkedin.com/company/legitapp";
export const APP_SUPPORT_EMAIL = "<EMAIL>";
export const APP_APP_STORE_URL =
  "https://apps.apple.com/us/app/legit-app-authentication/id1531627344";
export const APP_IOS_APP_ID = "1531627344";
export const APP_FACEBOOK_APP_ID = "930683725570114";
export const APP_GOOGLE_PLAY_URL =
  "https://play.google.com/store/apps/details?id=com.legitapp.client";
export const APP_LIST_PAGESIZE = 20;
export const APP_TWITTER_HANDLE = "@legitappcom";
export const APP_KEYWORDS = "";
export const APP_AUTHCLASS_URL = "https://authclass.com";
// reddit-sneakermarket.oss-us-west-1.aliyuncs.com
export const APP_LOCALES = [
  "en",
  "zh-Hans",
  "zh-Hant",
  //   'pl',
];
export const APP_DEFAULT_LOCALE = "en";

export const ZENDESK_KEY = "5e7a6386-02a9-4c2d-8055-750d1844cefe";
export const ZENDESK_SETTINGS = {
  color: {
    theme: "#3534FF",
  },
  launcher: {
    label: {
      "en-US": `Hello`,
    },
    chatLabel: {
      "en-US": "Leave us a message",
    },
  },
  // contactForm: {
  //   fields: [
  //     { id: 'description', prefill: { '*': 'My pre-filled description' } },
  //   ],
  // },
};

export const REAL_VS_FAKE_ARTICLE_DESCRIPTION = `Learn to spot the difference between authentic and fake luxury items with LEGIT APP's comprehensive Real vs Fake library. Our expert-curated resources empower you to make informed decisions and protect your investments.`;

export const LIST_PAGESIZE = 25;
export const LIST_PAGESIZE_24 = 24;

export const PRODUCT_CATEGORY_ID_LUXURY_HANDBAGS = 4;
export const PRODUCT_CATEGORY_ID_SNEAKERS = 1;
export const PRODUCT_CATEGORY_ID_LUXURY_CLOTHING = 5;
export const PRODUCT_CATEGORY_ID_LUXURY_SHOES = 6;
export const PRODUCT_CATEGORY_ID_LUXURY_ACCESSORIES = 7;
export const PRODUCT_CATEGORY_ID_LUXURY_WATCHES = 9;
export const PRODUCT_CATEGORY_ID_STREETWEAR = 2;
export const PRODUCT_CATEGORY_ID_TOYS_FIGURES = 3;
export const PRODUCT_CATEGORY_ID_TRADING_CARDS = 10;
export const PRODUCT_CATEGORY_ID_COSMETIC_PRODUCTS = 11;

export const PRODUCT_CATEGORY_OFFER_PRICE_STARTING_FROM: any = {
  [PRODUCT_CATEGORY_ID_LUXURY_HANDBAGS]: 10,
  [PRODUCT_CATEGORY_ID_SNEAKERS]: 3,
  [PRODUCT_CATEGORY_ID_LUXURY_CLOTHING]: 10,
  [PRODUCT_CATEGORY_ID_LUXURY_SHOES]: 10,
  [PRODUCT_CATEGORY_ID_LUXURY_ACCESSORIES]: 10,
  [PRODUCT_CATEGORY_ID_LUXURY_WATCHES]: 15,
  [PRODUCT_CATEGORY_ID_STREETWEAR]: 4,
  [PRODUCT_CATEGORY_ID_TOYS_FIGURES]: 4,
  [PRODUCT_CATEGORY_ID_TRADING_CARDS]: 4,
  [PRODUCT_CATEGORY_ID_COSMETIC_PRODUCTS]: 4,
};

export const PATH_ROUTE = {
  LOGIN: "/login",
  REGISTER: "/register",
  VERIFY_EMAIL: "/verify-email",
  REGISTER_SUCCESS: "/register-success",
  CREATE_PASSWORD: "/register/create-password",
  TROUBLE_LOGIN: "/login/trouble-login",
  NTF_CERTIFICATE: "/nft-certificate",
  STANDARDS: "/app/standards",
  CERT: "/cert",
};

export const STORAGE_KEY = {
  USER: "user",
  ACCESS_TOKEN: "accessToken",
};

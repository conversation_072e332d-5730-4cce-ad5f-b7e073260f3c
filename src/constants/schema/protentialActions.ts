import { APP_APP_STORE_URL, APP_GOOGLE_PLAY_URL } from "constants/app";
import { Action } from "schema-dts";

const SCHEMA_POTENTIAL_ACTION: Action[] = [
    {
        "@type": "DownloadAction",
        "name": "App Store",
        "url": APP_APP_STORE_URL,
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": APP_APP_STORE_URL,
            "actionPlatform": [
                "http://schema.org/DesktopWebPlatform",
                "http://schema.org/IOSPlatform"
            ]
        }
    },
    {
        "@type": "DownloadAction",
        "name": "Google Play",
        "url": APP_GOOGLE_PLAY_URL,
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": APP_GOOGLE_PLAY_URL,
            "actionPlatform": [
                "http://schema.org/DesktopWebPlatform",
                "http://schema.org/AndroidPlatform"
            ]
        }
    }
]

export default SCHEMA_POTENTIAL_ACTION

const isClient = typeof window !== "undefined";

interface StorageOptions {
  expires?: number;
}

interface StorageItem<T> {
  value: T;
  timestamp: number;
  expires?: number;
}

class Storage {
  static set<T>(key: string, value: T, options: StorageOptions = {}): boolean {
    if (!isClient) {
      return false;
    }

    try {
      const item: StorageItem<T> = {
        value,
        timestamp: Date.now(),
        expires: options.expires ? Date.now() + options.expires : undefined,
      };

      const serializedValue = JSON.stringify(item);
      localStorage.setItem(key, serializedValue);
      return true;
    } catch (error) {
      return false;
    }
  }

  static get<T>(key: string, defaultValue: T | null = null): T | null {
    if (!isClient) {
      return defaultValue;
    }

    try {
      const serializedValue = localStorage.getItem(key);
      if (!serializedValue) {
        return defaultValue;
      }

      const item: StorageItem<T> = JSON.parse(serializedValue);

      if (item.expires && Date.now() > item.expires) {
        localStorage.removeItem(key);
        return defaultValue;
      }

      return item.value;
    } catch (error) {
      try {
        const rawValue = localStorage.getItem(key);
        return rawValue ? JSON.parse(rawValue) : defaultValue;
      } catch {
        localStorage.removeItem(key);
        return defaultValue;
      }
    }
  }

  static remove(key: string): boolean {
    if (!isClient) {
      return false;
    }
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      return false;
    }
  }

  static clear(): boolean {
    if (!isClient) {
      return false;
    }
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      return false;
    }
  }

  static has(key: string): boolean {
    if (!isClient) {
      return false;
    }

    return localStorage.getItem(key) !== null;
  }

  static keys(): string[] {
    if (!isClient) {
      return [];
    }

    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        keys.push(key);
      }
    }
    return keys;
  }
}

export default Storage;

import { message } from "antd";

const MESSAGE_CLASS_NAME = 'app-pop-up-message'
const NOTIFICATION_CLASS_NAME = 'app-notification-notice'
// style in global.scss

export const showSuccessPopupMessage = (content: any) => (
  message.success({
    content,
    className: MESSAGE_CLASS_NAME,
  })
)

export const showErrorPopupMessage = (content: any) => (
  message.error({
    content,
    className: MESSAGE_CLASS_NAME,
  })
)

export const showInfoMessage = (content: any) => (
  message.info({
    content,
    className: MESSAGE_CLASS_NAME,
  })
) 

// // export const showSuccessPopupNotification = ({
// //   message,
// //   description,
// // }: any) => {
// //   notification.open({
// //     message,
// //     className: NOTIFICATION_CLASS_NAME,
// //     description,
// //     // icon: <CheckCircleFilled style={{ color: '#14f195' }} />,
// //   })
// // }

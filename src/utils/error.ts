import _ from "lodash"

const unknownErrorMessage = 'We got some problems. Please try again later.'

export const parseError = (error: any): Error => {
  return new Error(getErrorMessage(error))
}

export const getErrorMessage = (error: any): string => {
  const metaMaskErrorMessage = _.get(error, 'details')
  if (metaMaskErrorMessage && typeof metaMaskErrorMessage === 'string') {
    return metaMaskErrorMessage
  }
  const generatErrorMessage = _.get(error, 'message')
  if (generatErrorMessage && typeof generatErrorMessage === 'string') {
    return generatErrorMessage
  }
  const serverErrorMessage = _.get(error, '[0].message')
  if (serverErrorMessage && typeof serverErrorMessage === 'string') {
    return serverErrorMessage
  }
  return unknownErrorMessage
}
export const GA_TRACKING_ID = "G-H5KH63XZBP" //replace it with your measurement id

// https://developers.google.com/analytics/devguides/collection/gtagjs/pages
export const pageview = url => {
  if (process.env.NODE_ENV === 'development') {
    return
  }
  window.gtag("config", GA_TRACKING_ID, {
    page_path: url,
  })
}

// https://developers.google.com/analytics/devguides/collection/gtagjs/events
export const event = ({ action, category, label, value }) => {
  if (process.env.NODE_ENV === 'development') {
    return
  }
  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value,
  })
}

// <!-- Event snippet for iOS App Store conversion page -->
// <script>
//   gtag('event', 'conversion', {'send_to': 'AW-16712308936/8jjaCLO0juMZEMixhqE-'});
// </script>

export const tagDownloadiOSAppEvent = () => {
  if (process.env.NODE_ENV === 'development') {
    return
  }
  window.gtag('event', 'app_store_click');
  window.gtag('event', 'conversion', { 'send_to': 'AW-16712308936/8jjaCLO0juMZEMixhqE-' })
}

// <!-- Event snippet for Android Google Play conversion page -->
// <script>
//   gtag('event', 'conversion', {'send_to': 'AW-16712308936/jui0CLa0juMZEMixhqE-'});
// </script>

export const tagDownloadAndroidAppEvent = () => {
  if (process.env.NODE_ENV === 'development') {
    return
  }
  window.gtag('event', 'play_store_click');
  window.gtag('event', 'conversion', { 'send_to': 'AW-16712308936/jui0CLa0juMZEMixhqE-' })
}

// <!-- Event snippet for Contact Form conversion page -->
// <script>
//   gtag('event', 'conversion', {'send_to': 'AW-16712308936/pD7lCLm0juMZEMixhqE-'});
// </script>

export const tagContactFormEvent = () => {
  if (process.env.NODE_ENV === 'development') {
    return
  }
  window.gtag('event', 'contact_form_sub');
  window.gtag('event', 'conversion', { 'send_to': 'AW-16712308936/pD7lCLm0juMZEMixhqE-' })
}

import {
  CATEGORY,
  ORDER_STATUS,
  RESULT_TYPE,
  RISK_LEVELS,
} from "components/StartAuthentication/constant";
import moment from "moment";

export const getAuthStatus = ({
  status,
  result,
  categoryId,
}: {
  status?: string;
  result?: "pass" | "not_pass";
  categoryId?: number;
}) => {
  switch (status) {
    case ORDER_STATUS.USER_PENDING:
    case ORDER_STATUS.IN_PROGRESS:
      return "order_status_in_progress";
    case ORDER_STATUS.CANCELLED:
      return "order_status_cancelled";
    case ORDER_STATUS.USER_PENDING:
      return "order_status_photos_required";
    case ORDER_STATUS.COMPLETED:
    default:
      if (categoryId === CATEGORY.CODE_CHECKING) {
        return result === RESULT_TYPE.PASS
          ? "order_status_matched"
          : "order_status_not_matched";
      } else {
        return getResultText(result || "");
      }
  }
};

export const getAuthResultDesc = ({
  categoryId,
  result,
}: {
  categoryId?: number;
  result?: "pass" | "not_pass";
}) => {
  if (categoryId === CATEGORY.CODE_CHECKING) {
    return "order_status_matched_or_not_matched_desc";
  } else {
    return getResultTextDesc(result || "");
  }
};

export const formatDate = (
  locale: string,
  defaultLocale: string,
  time: string
) => {
  return locale === defaultLocale
    ? moment(time).format("D MMMM YYYY, h:mm A")
    : moment(time).format("YYYY年MM月DD日 HH:mm");
};

export const formatServiceTime = (
  minutes: number,
  minuteText: string,
  hourText: string,
  isEn: boolean
): string => {
  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60);
    return hours === 1 || !isEn
      ? `${hours} ${hourText}`
      : `${hours} ${hourText}s`;
  } else {
    return minutes === 1 || !isEn
      ? `${minutes} ${minuteText}`
      : `${minutes} ${minuteText}s`;
  }
};

export const getAIScoreKeyFromNumber = (aiScore: number) => {
  switch (aiScore) {
    case 0:
      return RISK_LEVELS.VERY_LOW;
    case 1:
      return RISK_LEVELS.LOW;
    case 2:
      return RISK_LEVELS.MODERATE;
    case 3:
      return RISK_LEVELS.ELEVATED;
    case 4:
      return RISK_LEVELS.HIGH;
    case 5:
      return RISK_LEVELS.VERY_HIGH;
    case 6:
      return RISK_LEVELS.SIGNIFICANT;
    case 7:
      return RISK_LEVELS.MAJOR;
    case 8:
      return RISK_LEVELS.SEVERE;
    case 9:
      return RISK_LEVELS.CRITICAL;
    default:
      return RISK_LEVELS.EXTREME;
  }
};

export const getAIScoreImgFromNumber = (aiScore: number) => {
  switch (aiScore) {
    case 0:
    case 1:
    case 2:
      return "/order/level/level-1.png";
    case 3:
    case 4:
      return "/order/level/level-2.png";
    case 5:
    case 6:
      return "/order/level/level-3.png";
    case 7:
    case 8:
      return "/order/level/level-4.png";
    default:
      return "/order/level/level-5.png";
  }
};

export const getResultBgColor = (result: string) => {
  return result === RESULT_TYPE.PASS
    ? "bg-gradient-blue"
    : result === RESULT_TYPE.NOT_PASS
    ? "bg-gradient-red"
    : "bg-gray-600";
};

export const getResultTextColor = (result: string, status?: string) => {
  return result === RESULT_TYPE.PASS
    ? "text-white"
    : result === RESULT_TYPE.NOT_PASS || status === ORDER_STATUS.USER_PENDING
    ? "text-red-600"
    : "text-gray-100";
};

export const getResultText = (result: string) => {
  return result === RESULT_TYPE.PASS
    ? "order_status_authentic"
    : result === RESULT_TYPE.NOT_PASS
    ? "order_status_replica"
    : "order_status_unable_to_verify";
};

export const getResultTextDesc = (result: string) => {
  return result === RESULT_TYPE.PASS
    ? "order_status_authentic_desc"
    : result === RESULT_TYPE.NOT_PASS
    ? "order_status_replica_desc"
    : "order_status_unable_to_verify_desc";
};

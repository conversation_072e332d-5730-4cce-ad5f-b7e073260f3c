import messagesEN from 'languages/en'
import messagesZHHANS from 'languages/zh-Hans'
import messagesZHHANT from 'languages/zh-Hant'

export const getLocaleMessages = (language: any) => {
  if (language === 'en') {
    return messagesEN
  } else if (language === 'zh-Hant') {
    return messagesZHHANT
  } else if (language === 'zh-Hans') {
    return messagesZHHANS
  // } else if (language === 'pl') {
  //   return messagesPL
    // }  else if (language === 'vi') {
    // // return messagesVI
    // } else if (language === 'es') {
    //   // return messagesES
    // } else if (language === 'ko') {
    //   // return messagesKO
    // } else if (language === 'ja') {
    //   // return messagesJA
    // } else if (language === 'th') {
    // return messagesTH
  }
  return messagesEN
}

export const getLocaleTitle = (language: any) => {
  if (language === 'en') {
    return 'English'
  } else if (language === 'zh-Hant') {
    return '繁體中文'
  } else if (language === 'zh-Hans') {
    return '简体中文'
  } else if (language === 'vi') {
    return 'Tiếng Việt'
  } else if (language === 'es') {
    return 'Español'
  } else if (language === 'ko') {
    return '한국어'
  } else if (language === 'ja') {
    return '日本語'
  } else if (language === 'th') {
    return 'ไทย'
  } else if (language === 'pl') {
    return 'Polski'
  }
  return 'English'
}

export const getLocalisedField = (item: any, field: any, language: any) => {
  if (!item) {
    return null
  }
  const fallbackValue = item[`${field}_en`] || item[field]
  if (language === 'en') {
    return item[field] || fallbackValue
  } else if (language === 'zh-Hant') {
    return item[`${field}_tc`] || fallbackValue
  } else if (language === 'zh-Hans') {
    return item[`${field}_sc`] || fallbackValue
  }
  return fallbackValue
}

export const getLocalisedFieldByObj = (
  item: any,
  field: any,
  language: any
) => {
  if (!item) {
    return null;
  }
  if (language === "en") {
    return item[field]["en"];
  } else if (language === "zh-Hant") {
    return item[`${field}`]["tc"];
  } else if (language === "zh-Hans") {
    return item[`${field}`]["sc"];
  }
};
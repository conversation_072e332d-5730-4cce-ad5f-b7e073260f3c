const getImageUrl = (url: any) => {
  if (!url) {
    return null
  }
  // https://legitapp-prod.oss-accelerate.aliyuncs.com/20200817fc999cd4992d2f4a.png
  return url
    .replace('http://', 'https://')
    .replace(/legitapp-prod.oss-cn-hongkong.aliyuncs.com/g, 'legitapp-prod.oss-accelerate.aliyuncs.com')
    .replace(/legitapp-static.oss-cn-hongkong.aliyuncs.com/g, 'legitapp-static.oss-accelerate.aliyuncs.com')
    .replace(/authclass-prod.oss-cn-hongkong.aliyuncs.com/g, 'authclass-prod.oss-accelerate.aliyuncs.com')
    .replace(/authclass-static.oss-cn-hongkong.aliyuncs.com/g, 'authclass-static.oss-accelerate.aliyuncs.com')
}

// https://legitapp-prod.oss-accelerate.aliyuncs.com/2020012163f9ae27570d0b8d.jpg

export default getImageUrl

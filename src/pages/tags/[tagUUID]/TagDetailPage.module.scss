.TagDetailPage {
  color: #FFF;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 48px 0;

  .TagCoverImageContainer {
    .CoverImage {
      width: 100%;
      max-width: 500px;
    }
  }

  .TagValidContainer {
    .TagValidContentContainer {
      margin: 60px 0 24px 0;

      .TagValidTitle {
        margin: 0 auto;
        padding: 12px 0px 0px 0px;
        width: 100%;
        max-width: 500px;
        color: #fff;
        font-size: 20px;
        text-align: center;
        font-weight: bold;
        margin-bottom: 12px;
        text-transform: uppercase;
      }

      .TagValidDescription {
        margin: 0 auto;
        padding: 0px;
        width: 100%;
        max-width: 500px;
        color: #fff;
        font-size: 14px;
        text-align: center;
        font-weight: normal;
      }
    }

    .TagDescription {
      padding: 150px 0px;
      width: 100%;
      color: #fff;
      font-size: 18px;
      text-align: center;
      font-weight: bold;
      margin-bottom: 12px;
      text-transform: uppercase;
    }

    .TagUuidBanner {
      margin: 60px auto;
      max-width: 500px;
      background-color: #fff;
      padding: 24px 60px;
      margin-bottom: 24px;
      border-radius: 3px;
      display: flex;
      flex-direction: column;
      align-items: center;
      row-gap: 12px;
      background-color: #191c21;

      .DownloadMessage {
        display: flex;
        align-items: center;
        column-gap: 8px;
        justify-content: center;
        line-height: 16px;
        font-size: 14px;
        font-weight: bold;
        width: fit-content;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;

        .anticon {
          color: red;
        }
      }

      .UuidBox {
        border: 1px solid #000;
        display: flex;
        align-items: center;
        padding: 3px 24px;
        column-gap: 6px;
        border-radius: 3px;
        font-weight: bold;
        width: 100%;
        justify-content: center;
        background-color: #fff;
        font-size: 18px;

        .separator {
          width: 1px;
          height: 100%;
          min-height: 10px;
          background-color: #000;
        }
      }
    }

    .TagValidOpenAppButton {
      margin: 0 auto;
      width: 80%;
      border-radius: 6px;
      background: #6236FF;
      background: linear-gradient(121deg, #6236FF 0%, #42A7FF 100%);
      max-width: 500px;
      color: #ffffff;
      background-color: #aaa;
      line-height: 30px;
      padding: 10px 20px;
      text-align: center;

      a {
        color: #ffffff;
        font-size: 24px;
        font-weight: bold;
        font-family: 'Montserrat', Helvetica, arial, sans-serif;
      }
    }
  }

}
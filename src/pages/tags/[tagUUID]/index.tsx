import { fetchItem } from 'actions/tagDetail'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppSpin from 'components/AppSpin'
import DownloadAppSection from 'components/HomePageSection/DownloadSection'
import useAppDispatch from 'hooks/useAppDispatch'
import useAppSelector from 'hooks/useAppSelector'
import _ from 'lodash'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { useIntl } from 'react-intl'
import { RootState } from 'reducers'
import AppLayout from '../../../components/AppLayout'
import css from './TagDetailPage.module.scss'

const TagsDetailPage = () => {
  const intl = useIntl()
  const router = useRouter()
  const dispatch = useAppDispatch();
  const tagDetailState: any = ({} = useAppSelector(
    (state: RootState) => state.tagDetail
  ));
  const {
    item,
    isFetchItemSuccess,
    isFetchItemLoading,
    fetchItemErrors,
  } = tagDetailState

  const tagUUID = _.get(router, 'query.tagUUID')
  const tagEnabled = !!item && item?.enabled
  const serviceRequestUUID = item?.service_request_uuid

  // http://localhost:3000/tags/6DT7NKTFR4YT

  const fetchTagItem = async () => {
    dispatch(fetchItem({ uuid: tagUUID }))
    // try {
    //   dispatch(fetchItem({ uuid: tagUUID }))
    //   const item = await apiCore.get(dispatch, `v2/service_feed/legit_tag_uuid/${tagUUID}`)
    //   console.log('item', item)
    //   const {
    //     service_request_uuid: serviceRequestUUID,
    //     enabled,
    //   } = item
    //   if (serviceRequestUUID) {
    //     router.push(`/cert/${serviceRequestUUID}`)
    //   }
    // } catch (errors) {
    //   console.log('errors', errors)
    // }
  }

  useEffect(() => {
    if (router.isReady) {
      if (tagUUID) {
        fetchTagItem()
      }
    }
  }, [router.isReady])

  // useEffect(() => {
  //   if (!isFetchItemLoading && !!fetchItemErrors) {
  //     const errorMessage = fetchItemErrors.map(error => error.message).join(', ')
  //     message.error(errorMessage)
  //   }
  // }, [fetchItemErrors])

  useEffect(() => {
    if (serviceRequestUUID) {
      // call tag id request to get case id
      router.push(`/cert/${serviceRequestUUID}`)
    }
  }, [isFetchItemSuccess])

  return (
    <div className={css.TagsPage}>
      <Head>
        <meta name="robots" content="noindex , nofollow" />
      </Head>
      <AppLayout>
        <AppHeader />
        <AppContainer>
          <div className={css.TagDetailPage}>
            <div className={css.TagCoverImageContainer}>
              <img
                className={css.CoverImage}
                src='/illustration-tags-detail-page-cover.png'
                alt={intl.formatMessage({ id: 'tags_page_tutorial_item_1_title' })}
              />
            </div>
            {
              isFetchItemLoading && (
                <AppSpin />
              )
            }
            {
              item && serviceRequestUUID && (
                <div className={css.TagValidContainer}>
                  <div className={css.TagValidContentContainer}>
                    <div className={css.TagValidTitle}>
                      {intl.formatMessage({ id: 'tags_detail_page_redirecting' }, { caseId: `#${serviceRequestUUID}` })}
                    </div>
                  </div>
                </div>
              )
            }
            {
              item && tagEnabled && !serviceRequestUUID && (
                <div className={css.TagValidContainer}>
                  <div className={css.TagValidContentContainer}>
                    <div className={css.TagValidTitle}>
                      {intl.formatMessage({ id: 'tags_detail_page_valid_tag_title' })}
                    </div>
                    <div className={css.TagValidDescription}>
                      {intl.formatMessage({ id: 'tags_detail_page_valid_tag_description' })}
                    </div>
                  </div>
                  <div className={css.TagUuidBanner}>
                    <div className={css.DownloadMessage}>
                      {intl.formatMessage({ id: 'tags_detail_page_new_tag_description' })}
                    </div>
                    <div className={css.UuidBox}>
                      <span>
                        <span
                          style={{
                            color: tagEnabled
                              ? 'green'
                              : 'red',
                          }}
                        >
                          {tagUUID}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              )
            }
            {
              item && !tagEnabled && (
                <div className={css.TagValidContainer}>
                  <div className={css.TagValidContentContainer}>
                    <div className={css.TagValidTitle}>
                      {intl.formatMessage({ id: 'tags_detail_page_invalid_tag_title' })}
                    </div>
                    <div className={css.TagValidDescription}>
                      {intl.formatMessage({ id: 'tags_detail_page_invalid_tag_description' })}
                    </div>
                  </div>
                </div>
              )
            }
            {/* {
              item && tagEnabled && (
                <div className={css.TagValidContainer}>
                  <div className={css.TagValidContentContainer}>
                    <div className={css.TagValidTitle}>
                      {intl.formatMessage({ id: 'tags_detail_page_open_legit_app_title' })}
                    </div>
                    <div className={css.TagValidOpenAppButton}>
                      <a href='' target='_blank' rel='noopener noreferrer'>
                        {intl.formatMessage({
                          id: 'tags_detail_page_open_legit_app_button_title',
                        })}
                      </a>
                    </div>
                  </div>
                </div>
              )
            } */}
          </div>
        </AppContainer>
        <DownloadAppSection />
        <AppFooter />
      </AppLayout >
    </div >
  )
}

export default TagsDetailPage

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import React from "react";
import { useRouter } from "next/router";

import { userSignOut } from "actions/app";
import { PATH_ROUTE } from "constants/app";
import useAppDispatch from "hooks/useAppDispatch";
import { showSuccessPopupMessage } from "utils/message";

const Profile = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();

  const handleLogout = () => {
    dispatch(userSignOut());
    showSuccessPopupMessage("You have successfully logged out.");
    router.push(PATH_ROUTE.LOGIN);
  };

  return (
    <div className="min-h-screen">
      <StartAuthenticationHeader>
        <div
          onClick={handleLogout}
          className="cursor-pointer text-white text-center py-1 border border-gray-100 rounded-sm"
        >
          Log out
        </div>
      </StartAuthenticationHeader>
    </div>
  );
};

export default Profile;

import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Image from "next/image";
import { LeftOutlined } from "@ant-design/icons";

import apiCore from "utils/apiCore";
import { getLocalisedField, getLocalisedFieldByObj } from "utils/locale";
import AppSpin from "components/AppSpin";
import { CheckerData } from "types/orders";

const AuthenticatorProfile = () => {
  const router = useRouter();
  const { checkerId } = router.query;
  const { locale = "en" } = router;

  const [isLoading, setIsLoading] = useState(false);
  const [checkerData, setCheckerData] = useState<CheckerData | null>(null);

  const fetchCheckerData = async (id: string) => {
    setIsLoading(true);
    try {
      const res = await apiCore.get(null, `v1/checker/${id}`);
      setCheckerData(res);
    } catch (error) {
      console.error("Error fetching checker data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (checkerId) {
      fetchCheckerData(checkerId as string);
    }
  }, [checkerId]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <AppSpin />
      </div>
    );
  }

  if (!checkerData) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center text-white">
        <div>Authenticator not found</div>
      </div>
    );
  }

  return (
    <div className="text-white max-w-screen-lg m-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-4">
        <button onClick={() => router.back()} className="text-white">
          <LeftOutlined style={{ fontSize: "24px" }} />
        </button>
        <h1 className="md:text-xl font-bold">Authenticator Profile</h1>
        <div className="w-6"></div>
      </div>

      {/* Profile */}
      <div className="px-4 py-6">
        <div className="flex items-center gap-4 mb-6">
          <div className="w-20 h-20 rounded-full overflow-hidden">
            <Image
              src={
                checkerData.profile_image_url ||
                "/order/icon_detail_result_overlay.png"
              }
              alt={checkerData.name}
              width={80}
              height={80}
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <h2 className="text-2xl font-bold">{checkerData.name}</h2>
            <p>
              {getLocalisedFieldByObj(checkerData, "headline", locale)} (#
              {checkerData.id})
            </p>
          </div>
        </div>

        {/* Category Badge */}
        {checkerData.category && checkerData.category.length > 0 && (
          <div className="flex gap-2 flex-wrap">
            {checkerData.category.map((category) => (
              <div key={category.title}>
                <span className="bg-gray-700 px-3 py-1 rounded-md text-sm">
                  {getLocalisedField(category, "title", locale)}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Description */}
        <div className="my-8">
          <p className="text-gray-100 leading-relaxed text-sm">
            {getLocalisedFieldByObj(checkerData, "description", locale)}
          </p>
        </div>

        {/* Brands Covered */}
        {checkerData.brand && checkerData.brand.length > 0 && (
          <div>
            <h3 className="text-xl font-bold mb-2">Brands Covered</h3>
            <div className="rounded-md">
              <div className="grid grid-cols-4 md:gap-4 gap-2">
                {checkerData.brand.map((brand, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-center p-4 bg-dark-100 rounded-lg"
                  >
                    {brand.icon_image_url ? (
                      <Image
                        src={brand.icon_image_url}
                        alt={brand.title}
                        width={60}
                        height={60}
                        className="object-contain"
                      />
                    ) : (
                      <span className="text-white font-bold text-lg">
                        {brand.title}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthenticatorProfile;

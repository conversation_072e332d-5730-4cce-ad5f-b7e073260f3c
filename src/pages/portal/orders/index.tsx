import React, { useEffect, useState, useMemo } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import Image from "next/image";
import { RootState } from "reducers";
import { Select } from "antd";
import { RightOutlined } from "@ant-design/icons";
import clsx from "clsx";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import {
  fetchOrderList,
  reset,
  fetchPhotosRequiredOrders,
} from "actions/orderList";
import AppSpin from "components/AppSpin";
import { fetchItems } from "actions/articleList";
import AppPlaceholder from "components/AppPlaceholder";
import { getErrorMessage } from "utils/error";
import { OrderItem } from "types/orders";
import styles from "./OrdersPage.module.scss";
import OrderCard from "components/StartAuthentication/OrdersComp/OrderCard";
import AppListLoadMoreCard from "components/AppListLoadMoreCard";
import {
  ORDER_STATUS,
  ordersPath,
} from "components/StartAuthentication/constant";

const Orders = () => {
  const intl = useIntl();
  const router = useRouter();

  const dispatch = useAppDispatch();
  const {
    items,
    isFetchItemsLoading,
    fetchItemsErrors,
    pagination = {},
    photosRequiredItems,
    isFetchPhotosRequiredLoading,
  } = useAppSelector((state: RootState) => state.orderList);
  const { accessToken } = useAppSelector((state: RootState) => state.app);
  const [filterStatus, setFilterStatus] = useState<string>("all");

  const { total } = pagination;
  const hasMore = items && total ? items.length < total : false;

  const filteredItems = useMemo(() => {
    if (!items) return null;

    switch (filterStatus) {
      case "in_progress":
        return items.filter((order: OrderItem) =>
          [ORDER_STATUS.IN_PROGRESS, ORDER_STATUS.USER_PENDING].includes(
            order.status
          )
        );
      case "completed":
        return items.filter(
          (order: OrderItem) => order.status === ORDER_STATUS.COMPLETED
        );
      case "all":
      default:
        return items;
    }
  }, [items, filterStatus]);

  useEffect(() => {
    dispatch(reset());

    if (accessToken) {
      dispatch(fetchOrderList({ $offset: 0 }, [], accessToken));
      dispatch(fetchPhotosRequiredOrders(accessToken));
    }
  }, [dispatch, accessToken]);

  const loadMore = () => {
    if (items && !isFetchItemsLoading) {
      const currentOffset = items.length;
      if (accessToken) {
        dispatch(
          fetchOrderList({ $offset: currentOffset }, items, accessToken)
        );
      } else {
        dispatch(fetchItems({ $offset: currentOffset }, items));
      }
    }
  };

  return (
    <div className={styles.OrdersPage}>
      <StartAuthenticationHeader>
        <div>
          <div className="sticky top-0 bg-black z-10 pb-1">
            <div className="text-3xl sm:text-4xl font-bold mb-6">
              {intl.formatMessage({
                id: "start_authentication_page_order_title",
              })}
            </div>
            <div className="flex justify-end items-center gap-2 mb-4">
              <Select
                value={filterStatus}
                onChange={setFilterStatus}
                className={clsx(styles.filterSelect)}
                size="middle"
                options={[
                  {
                    value: "all",
                    label: "ALL",
                  },
                  {
                    value: "in_progress",
                    label: "IN PROGRESS",
                  },
                  {
                    value: "completed",
                    label: "COMPLETED",
                  },
                ]}
              />
            </div>
          </div>

          {/* loading */}
          {isFetchItemsLoading && isFetchPhotosRequiredLoading && !items && (
            <AppSpin />
          )}

          {/* error */}
          {fetchItemsErrors && fetchItemsErrors.length > 0 && (
            <div>
              <AppPlaceholder
                iconType="exclamation-circle"
                title={getErrorMessage(fetchItemsErrors)}
              />
            </div>
          )}

          {/* additional Photos Required */}
          {!!photosRequiredItems?.length && (
            <div
              className="cursor-pointer border border-yellow-500  rounded-sm md:p-4 p-2 mb-4 text-white flex justify-between items-center"
              onClick={() =>
                router.push(`${ordersPath}/${photosRequiredItems[0].id}`)
              }
            >
              <div className="flex gap-4 items-center">
                <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-black font-bold">
                  i
                </div>
                <div className="md:text-base text-xs">
                  <div>Additional Photos Required</div>
                  <div>#{photosRequiredItems[0].uuid}</div>
                </div>
              </div>
              <div>
                <RightOutlined />
              </div>
            </div>
          )}

          {/* order list */}
          {filteredItems && filteredItems.length > 0 && (
            <div className="w-full">
              {filteredItems.map((order: OrderItem) => (
                <OrderCard key={order.id} order={order} />
              ))}
            </div>
          )}

          {/* load more button */}
          {hasMore && !fetchItemsErrors && (
            <div className="mt-6">
              <AppListLoadMoreCard
                onClick={loadMore}
                loading={isFetchItemsLoading}
              />
            </div>
          )}

          {/* empty */}
          {((items && items.length === 0) ||
            (filteredItems && filteredItems.length === 0)) &&
            !isFetchItemsLoading && (
              <div className="flex flex-col justify-center items-center gap-6 text-center py-[20vh] text-gray-500">
                <div className="w-16 h-24">
                  <Image
                    src="/order/icon_empty_bill_board.png"
                    width={138}
                    height={183}
                    alt="empty-order"
                    className="w-full h-full object-contain"
                  />
                </div>
                <div>
                  <div>No Authentication Record</div>
                  <div>Start your first authentication</div>
                </div>
              </div>
            )}
        </div>
      </StartAuthenticationHeader>
    </div>
  );
};

export default Orders;

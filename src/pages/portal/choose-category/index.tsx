import React, { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import { RootState } from "reducers";

import { fetchCategoryList } from "actions/productCategory";
import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import { IProductCategory } from "types/orders";
import NavigationHeader from "components/NavigationHeader";
import { chooseBrandPath } from "components/StartAuthentication/constant";
import AppSpin from "components/AppSpin";
import { getLocalisedField } from "utils/locale";

const ChooseCategory = () => {
  const router = useRouter();
  const { locale } = router;
  const dispatch = useAppDispatch();
  const productCategory = useAppSelector(
    (state: RootState) => state.productCategory
  );
  const { items, isFetchItemsLoading } = productCategory;

  useEffect(() => {
    if (!items?.length) {
      dispatch(fetchCategoryList());
    }
  }, [dispatch, items?.length]);

  return (
    <div className="text-white">
      <NavigationHeader hideBackButton progress="20%" />
      <div className="max-w-screen-lg m-auto md:py-10 py-4 md:px-12 px-4">
        <div className="text-2xl md:text-3xl font-bold text-center md:mb-10 mb-4">
          Choose a Category
        </div>

        {/* loading */}
        {(!items || items.length === 0) && isFetchItemsLoading && <AppSpin />}

        <div className="grid grid-cols-2 md:grid-cols-3 gap-5">
          {items?.map((product: IProductCategory) => (
            <div
              key={product.id}
              className="cursor-pointer md:h-72 h-40 border border-gray-200 rounded-xl flex flex-col justify-center items-center"
              onClick={() => {
                router.push(`${chooseBrandPath}/${product.id}`);
              }}
            >
              <div className="w-20 h-20 md:w-30 md:h-30 flex justify-center items-center">
                <Image
                  src={product.icon_image_url}
                  width={120}
                  height={120}
                  alt={product.title}
                />
              </div>
              <div className="font-semibold md:mt-7 mt-4 md:text-base text-[10px]">
                {getLocalisedField(product, "title", locale)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChooseCategory;

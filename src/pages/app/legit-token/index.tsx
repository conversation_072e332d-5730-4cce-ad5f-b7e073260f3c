import AppContainer from 'components/AppContainer'
import AppLayout from 'components/AppLayout'
import LegitTokenContent from 'components/LegitTokenContent'
import css from './AppLegitTokenPage.module.scss'
import AppInAppPageHeader from 'components/AppInAppPageHeader'

const AppLegitTokenPage = () => {
  
  return (
    <div className={css.AppLegitTokenPage}>
      <AppInAppPageHeader />
      <AppLayout>
        <AppContainer className={css.sectionContainer}>
          <LegitTokenContent />
        </AppContainer>
      </AppLayout>
    </div>
  )
}

export default AppLegitTokenPage

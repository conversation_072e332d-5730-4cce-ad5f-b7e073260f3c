// import getConfig from 'next/config'
import AppContainer from 'components/AppContainer'
import AppLayout from 'components/AppLayout'
import { APP_NAME } from 'constants/app'
import parse from 'html-react-parser'
import moment from 'moment'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import apiCore from 'utils/apiCore'
import { getLocaleMessages, getLocalisedField } from 'utils/locale'
import css from './ArticleDetailPage.module.scss'
import resizeImageUrl from 'utils/resizeImageUrl'
import getImageUrl from 'utils/imageUrl'
import AppInAppPageHeader from 'components/AppInAppPageHeader'

// const { publicRuntimeConfig } = getConfig()
// const { API_URL } = publicRuntimeConfig

const AppArticleDetailPage = ({ item, pageTitle }: any) => {

  const intl = useIntl()
  const router = useRouter()
  // const dispatch = useAppDispatch();
  // const articleDetailState: any = ({} = useAppSelector(
  //   (state: RootState) => state.articleDetail
  // ));
  // const {
  //   item,
  //   isFetchItemLoading,
  //   fetchItemErrors,
  // } = articleDetailState
  const { locale = '' } = router
  // const articleSlug = _.get(router, 'query.articleSlug')

  // const refreshItem = () => {
  //   dispatch(fetchItem({
  //     slug: articleSlug,
  //   }))
  // }

  // useEffect(() => {
  //   if (router.isReady) {
  //     if (articleSlug) {
  //       refreshItem()
  //     }
  //   }
  // }, [router.isReady])

  // useEffect(() => {
  //   if (router.isReady && !!articleSlug && !!item && item.slug !== articleSlug) {
  //     props.reset()
  //     props.fetchItem({ slug: articleSlug })
  //   }
  // }, [articleSlug])
  return (
    <div className={css.ArticleDetailPage}>
      <AppInAppPageHeader />
      <AppLayout>
        <AppContainer className={css.pageContainer}>
          {
            !!item && (
              <article className={css.articleItemContent}>
                {
                  !!item.subtitle && (
                    <div className={css.itemSubtitle}>
                      {getLocalisedField(item, 'subtitle', locale)}
                    </div>
                  )
                }
                <div className={css.itemTitle}>
                  {getLocalisedField(item, 'title', locale)}
                </div>
                <div className={css.itemMetadata}>
                  <div className={css.itemDate}>
                    {moment(item.published_at || item.created_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).format('YYYY-MM-DD HH:mm')}
                  </div>
                </div>
                <div className={css.itemContent}>
                  {parse((item.content.trim() || '').replace(/legitapp-prod.oss-cn-hongkong.aliyuncs.com/g, 'legitapp-prod.oss-accelerate.aliyuncs.com'))}
                </div>
              </article>
            )
          }
        </AppContainer>
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const { articleSlug: itemId } = params;
  if (itemId && itemId !== "undefined") {
    try {
      const item = await apiCore.get(null, `v1/article/slug/${itemId}`)
      if (item) {
        const {
          title,
          highlight,
          cover_image_url: coverImageUrl,
        } = item
        const ogTitle = `${title}`
        const pageTitle = `${ogTitle} | ${getLocaleMessages(locale)?.[`app_title`]}`;
        const pageMetaTagItemMap: any = {
          'og-type': {
            property: "og:type",
            content: "article",
          },
          'og-title': {
            property: "og:title",
            content: pageTitle,
          }
        }
        if (highlight) {
          pageMetaTagItemMap['og-description'] = {
            property: "og:description",
            content: highlight,
          }
        }
        if (coverImageUrl) {
          pageMetaTagItemMap['og-image'] = {
            property: "og:image",
            content: resizeImageUrl(getImageUrl(coverImageUrl), { width: 600 }),
          }
        }
        return {
          props: {
            pageTitle,
            pageMetaTagItemMap,
            item,
          },
        };
      }
    } catch (error) {
      console.log(`error for v1/article/slug/${itemId}`, error);
    }
  }
  return { props: {} };
}

export async function getStaticPaths() {
  return { paths: [], fallback: "blocking" };
}

export default AppArticleDetailPage

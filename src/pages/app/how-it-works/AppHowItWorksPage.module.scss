.AppHowItWorksPage {
  .onlineSection {
    .sectionContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .sectionHeader {
      padding: 24px 0 24px 0;

      @include responsive('md') {
        padding: 48px 0 48px 0;
      }
    }

    .tutorialCardGrid {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      column-gap: 24px;
      row-gap: 24px;
      margin-bottom: 48px;

      @include responsive('md') {
        grid-template-columns: repeat(3, 1fr);
      }

      .tutorialCard {
        min-height: 400px;
        color: $color-app-white;
        row-gap: 6px;
        border: 1px solid $color-separator-white-2;
        color: $color-app-white;
        border-radius: $border-radius-theme-1;
        overflow: hidden;
        position: relative;

        @include responsive('md') {
          min-height: 500px;
        }

        .cardBackground {
          position: absolute;
          width: 100%;
          height: 100%;

          .coverImage {
            width: 102%;
            height: 102%;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
          }
        }

        .cardContent {
          padding: 24px;
          position: relative;
          display: flex;
          flex-direction: column;

          @include responsive('md') {
            padding: 40px;
          }

          .step {
            width: 100%;
            opacity: 0.7;
            font-weight: bold;
            margin-bottom: 3px;
            font-size: 14px;
            line-height: 26px;

            @include responsive('md') {
              font-size: 16px;
              line-height: 26px;
            }
          }

          .title {
            font-weight: bold;
            margin-bottom: 6px;
            font-size: 16px;
            line-height: 26px;

            @include responsive('md') {
              font-size: 20px;
              line-height: 30px;
            }
          }

          .instruction {
            opacity: 0.6;
            font-size: 14px;
            line-height: 24px;

            @include responsive('md') {
              font-size: 16px;
              line-height: 26px;
            }
          }
        }
      }
    }

    .sectionActionButton {
      cursor: pointer;
      padding: 10px 12px;
      background-color: $color-app-white;
      border-radius: 4px;
      color: #000;
      font-weight: bold;
      text-align: center;
      // max-width: 300px;
      display: flex;
      width: fit-content;
      column-gap: 6px;
    }
  }
}
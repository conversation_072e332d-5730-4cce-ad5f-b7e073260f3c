import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppLayout from 'components/AppLayout'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useIntl } from 'react-intl'
import css from './AppHowItWorksPage.module.scss'
import AppInAppPageHeader from 'components/AppInAppPageHeader'

const AppHowItWorksPage = () => {

    const intl = useIntl()

    return (
        <div className={css.AppHowItWorksPage}>
            <AppInAppPageHeader />
            <AppLayout>
                <div className={css.onlineSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'how_it_works_page_online_section_title' })}
                            subtitle={intl.formatMessage({ id: 'how_it_works_page_online_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'how_it_works_page_online_section_description' })}
                            </>}
                        />
                        <div className={css.tutorialCardGrid}>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(/background-how-it-works-online-1.jpg)`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 1
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_1_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_1_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(/background-how-it-works-online-2.jpg)`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 2
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_2_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_2_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(/background-how-it-works-online-3.jpg)`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 3
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_3_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_3_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(/background-how-it-works-online-4.jpg)`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 4
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_4_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_4_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(/background-how-it-works-online-5.jpg)`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 5
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_5_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_5_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(/background-how-it-works-online-6.jpg)`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 6
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_6_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_6_description' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </AppContainer>
                </div >
            </AppLayout>
        </div>
    )
}

export default AppHowItWorksPage

import AppContainer from 'components/AppContainer'
import AppLayout from 'components/AppLayout'
import NFTCertificateContent from 'components/NFTCertificateContent'
import css from './AppNFTCertificatePage.module.scss'
import AppInAppPageHeader from 'components/AppInAppPageHeader'

const AppNFTCertificatePage = () => {
  
  return (
    <div className={css.AppLegitTokenPage}>
      <AppInAppPageHeader />
      <AppLayout>
        <AppContainer className={css.sectionContainer}>
          <NFTCertificateContent />
        </AppContainer>
      </AppLayout>
    </div>
  )
}

export default AppNFTCertificatePage

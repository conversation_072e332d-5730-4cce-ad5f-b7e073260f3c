import { fetchData } from 'actions/verifiedBusinessList'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppListLoadMoreCard from 'components/AppListLoadMoreCard'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import AppPlaceholder from 'components/AppPlaceholder'
import AppSpin from 'components/AppSpin'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { LIST_PAGESIZE } from 'constants/app'
import useAppDispatch from 'hooks/useAppDispatch'
import useAppSelector from 'hooks/useAppSelector'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { useIntl } from 'react-intl'
import { RootState } from 'reducers'
import { getErrorMessage } from 'utils/error'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages } from 'utils/locale'
import { showErrorPopupMessage } from 'utils/message'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './CustomerListPage.module.scss'
import { ArrowRightOutlined } from '@ant-design/icons'

const CustomerListPage = ({ pageTitle }: any) => {

    const intl = useIntl()
    const router = useRouter()
    const dispatch = useAppDispatch();
    const verifiedBusinessListState: any = ({} = useAppSelector(
        (state: RootState) => state.verifiedBusinessList
    ));
    const {
        requestLoading,
        requestResult,
        requestErrors,
    } = verifiedBusinessListState
    const {
        total,
        data,
    } = requestResult || {}

    const { locale } = router

    const refresh = () => {
        dispatch(fetchData({
            $limit: LIST_PAGESIZE,
            language: locale,
        }))
    }

    useEffect(() => {
        if (!data) {
            refresh()
        }
    }, [])

    useEffect(() => {
        if (!requestLoading && !!requestErrors) {
            const errorMessage = getErrorMessage(requestErrors)
            showErrorPopupMessage(errorMessage)
        }
    }, [requestErrors])

    const handleLoadMoreOnClick = () => {
        const currentItems = data || []
        dispatch(fetchData({
            $offset: currentItems.length,
            $limit: LIST_PAGESIZE,
            language: locale,
        }, data))
    }

    // console.log('data', data)

    return (
        <div className={css.CustomerListPage}>
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.blogListSection}>
                    <AppContainer className={css.pageContainer}>
                        <HomePageSectionHeader
                            className={css.sectionHeader}
                            subtitle={intl.formatMessage({ id: 'customer_list_page_subtitle' })}
                            title={intl.formatMessage({ id: 'customer_list_page_title' })}
                            description={<>
                                {intl.formatMessage({ id: 'customer_list_page_description' })}
                                {/* <br /> */}
                                {/* {intl.formatMessage({ id: 'home_page_pricing_section_description_2' })} */}
                            </>}
                        />
                        <div className={css.customerList}>
                            {
                                (!data || data.length === 0) && requestLoading && (
                                    <AppSpin />
                                )
                            }
                            {
                                !requestLoading && (data && data.length === 0) && (
                                    <AppPlaceholder
                                        iconType='meh'
                                        title={intl.formatMessage({ id: 'placeholder_no_search_result' })}
                                    />
                                )
                            }
                            <div className={css.customerListCardGrid}>
                                {
                                    data && (
                                        data.map((customerListItem: any) => (
                                            <Link
                                                href={`/customers/${customerListItem.slug}`}
                                                key={`verified-business-${customerListItem.slug}-col`}
                                            >
                                                <div
                                                    className={css.verifiedBusinessCard}
                                                >
                                                    <div className={css.coverImageContainer}>
                                                        <div
                                                            className={css.coverImage}
                                                            style={{
                                                                backgroundImage: `url(${resizeImageUrl(getImageUrl(customerListItem.quote_image_url), { width: 500 })})`,
                                                            }}
                                                        >

                                                            <div
                                                                className={css.overlay}
                                                                style={{
                                                                    backgroundImage: `linear-gradient(to bottom, rgba(0, 0, 0, 0), ${customerListItem.color})`,
                                                                }}
                                                            />
                                                            <div
                                                                className={css.overlay}
                                                                style={{
                                                                    // backgroundImage: `linear-gradient(to left, rgba(0, 0, 0, 0), ${customerListItem.color})`,
                                                                }}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className={css.companyLogo}>
                                                        <img src={customerListItem.logo_image_url} />
                                                    </div>
                                                    <div className={css.companyInformation}>
                                                        <div className={css.companyTitle}>
                                                            {customerListItem.article_title}
                                                        </div>
                                                    </div>
                                                </div>
                                            </Link>
                                        ))
                                    )
                                }
                            </div>
                            {
                                requestErrors && requestErrors.length > 0 && (
                                    <div>
                                        <AppPlaceholder
                                            iconType='exclamation-circle'
                                            title={getErrorMessage(requestErrors)}
                                        />
                                    </div>
                                )
                            }
                            {
                                data && (data.length < total) && (
                                    <div>
                                        <AppListLoadMoreCard
                                            onClick={handleLoadMoreOnClick}
                                            loading={requestLoading}
                                        />
                                    </div>
                                )
                            }
                        </div>
                        <div className={css.sectionActionButtonContainer}>
                            <Link
                                href={'/contact'}
                                className={css.sectionActionButton}
                            >
                                {intl.formatMessage({ id: `pricing_page_business_section_action_button_title` })}<ArrowRightOutlined />
                            </Link>
                        </div>
                    </AppContainer>
                </div>
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`customers_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageDescription = getLocaleMessages(locale)?.[`customer_list_page_description`]
    const pageMetaTagItemMap = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
        'og-desciption': {
            property: 'og:description',
            content: pageDescription,
        }
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}


export default CustomerListPage

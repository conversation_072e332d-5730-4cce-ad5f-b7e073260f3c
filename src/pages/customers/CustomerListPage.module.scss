.CustomerListPage {
    background: #000;
    min-height: 100vh;

    .blogListSection {}

    .pageContainer {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .sectionHeader {
        padding: 48px 0 36px 0;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }
    }

    .sectionActionButtonContainer {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 90px;
    }

    .sectionActionButton {
        cursor: pointer;
        padding: 10px 12px;
        background-color: $color-app-white;
        border-radius: 4px;
        color: #000;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        // justify-content: space-between;
        column-gap: 12px;
        width: 100%;
        font-size: 14px;

        @include responsive('md') {
            width: fit-content;
            font-size: 16px;
        }
    }

    .customerList {
        margin-bottom: 24px;
    }

    .customerListCardGrid {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        column-gap: 24px;
        row-gap: 24px;
        margin-bottom: 24px;
        align-items: stretch;
        justify-content: stretch;
        // background-color: blue;

        @include responsive('sm') {
            // grid-template-columns: repeat(2, 1fr);
        }

        @include responsive('md') {
            grid-template-columns: repeat(3, 1fr);
            // row-gap: 72px;
        }
    }

    .verifiedBusinessCard {
        // background-color: red;
        position: relative;
        border-radius: $border-radius-theme-1;
        overflow: hidden;
        border: 1px solid $color-separator-white-1;

        &:hover {
            .coverImage {
                transform: scale(1.05);
            }
        }

        .coverImageContainer {
            overflow: hidden;

            .coverImage {
                transition: 0.3s;
                background-repeat: no-repeat;
                background-position: center;
                background-size: cover;
                width: 100%;
                padding-bottom: 60%;
                position: relative;

                .overlay {
                    position: absolute;
                    display: block;
                    width: 100%;
                    height: 100%;
                    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.84), rgba(0, 0, 0, 0.75));
                    opacity: 0.7;
                    transition: all 0.25s ease-in;
                    top: 0;
                    left: 0;
                }
            }

        }

        .companyLogo {
            position: absolute;
            display: block;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            padding: 20px;

            img {
                border-radius: $border-radius-theme-1;
                overflow: hidden;
                max-height: 40px;
            }
        }

        .companyInformation {
            background-color: $color-app-header-dropdown-menu-item-text;
            position: relative;
            // position: absolute;
            // bottom: 0;
            // left: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            background-color: transparent;
            padding: 20px;

            .companyTitle {
                color: $color-app-white;
                font-weight: bold;
            }
        }
    }
}
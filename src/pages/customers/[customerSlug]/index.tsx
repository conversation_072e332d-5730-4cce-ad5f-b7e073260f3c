// import getConfig from 'next/config'
import { DesktopOutlined, GlobalOutlined, SolutionOutlined } from '@ant-design/icons'
import AppBreadcrumb from 'components/AppBreadcrumb'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import apiCore from 'utils/apiCore'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './CustomerDetailPage.module.scss'

// const { publicRuntimeConfig } = getConfig()
// const { API_URL } = publicRuntimeConfig

const CustomerDetailPage = ({ item, pageTitle }: any) => {

    // console.log('item', item)

    const intl = useIntl()
    const router = useRouter()
    // const dispatch = useAppDispatch();
    // const articleDetailState: any = ({} = useAppSelector(
    //   (state: RootState) => state.articleDetail
    // ));
    // const {
    //   item,
    //   isFetchItemLoading,
    //   fetchItemErrors,
    // } = articleDetailState
    const { locale = '' } = router
    // const articleSlug = _.get(router, 'query.articleSlug')

    // const refreshItem = () => {
    //   dispatch(fetchItem({
    //     slug: articleSlug,
    //   }))
    // }

    // useEffect(() => {
    //   if (router.isReady) {
    //     if (articleSlug) {
    //       refreshItem()
    //     }
    //   }
    // }, [router.isReady])

    // useEffect(() => {
    //   if (router.isReady && !!articleSlug && !!item && item.slug !== articleSlug) {
    //     props.reset()
    //     props.fetchItem({ slug: articleSlug })
    //   }
    // }, [articleSlug])

    const getItemMetadata = () => {
        if (!item) {
            return null
        }
        return (
            <div className={css.customerMetadataContainer}>
                <div className={css.customerLogo}>
                    <img src={item.logo_image_url} />
                </div>
                <div className={css.customerProduct}>
                    <div className={css.fieldTitle}>
                        Products Used
                    </div>
                    <div className={css.fieldValue}>
                        {(item.products_used || '').split(',').map(((productUsed: any) => (
                            <div className={css.productUsed} key={`${item.slug}-product-used${productUsed}`}>
                                {productUsed}
                            </div>
                        )))}
                    </div>
                </div>
                <div className={css.customerRegion}>
                    <GlobalOutlined /> {item.country}
                </div>
                <div className={css.customerType}>
                    <SolutionOutlined /> Enterprise
                </div>
                {
                    item.website_url
                        ? (
                            <a href={item.website_url} target='_blank' rel='noopener noreferrer'>
                                <div className={css.customerWebsite}>
                                    <DesktopOutlined /> {item.website_url.replace('https://', '').replace('http://')}
                                </div>
                            </a>
                        )
                        : null
                }
            </div>
        )
    }

    const getPageContent = () => {
        if (!item) {
            return null
        }
        return (
            <AppContainer className={css.pageContainer}>
                <AppBreadcrumb
                    items={[
                        // {
                        //     title: <Link href='/blog'>{intl.formatMessage({ id: `blog_page_title` })}</Link>,
                        // },
                        {
                            title: <Link href='/customers'>{intl.formatMessage({ id: `customers_page_title` })}</Link>,
                        },
                        {
                            title: `${item.company}`,
                        }
                    ]}
                    className={css.pageBreadcrumb}
                />
                <div className={css.customerContentLayout}>
                    <div className={css.desktopOnlyContainer}>
                        {getItemMetadata()}
                    </div>
                    <div className={css.customerContentContainer}>
                        <div className={css.customerHeader}>
                            <h2 className={css.title}>
                                {item.company}
                            </h2>
                            <h3 className={css.subtitle}>
                                {item.article_title}
                            </h3>
                            <div className={css.description}>
                                {item.description}
                            </div>
                        </div>
                        <div className={css.customerImages}>
                            <img src={resizeImageUrl(getImageUrl(item.cover_image_url), { width: 500 })} />
                        </div>
                        <div className={css.responsiveOnlyContainer}>
                            {getItemMetadata()}
                        </div>
                        <div className={css.customerContent}>
                            {item.article_content}
                        </div>
                        <div className={css.customerBanner}>
                            <div className={css.customerBannerBackground}>
                                <div />
                                <div
                                    className={css.overlay}
                                    style={{
                                        backgroundImage: `linear-gradient(to right, rgba(0, 0, 0, 0), ${item.color})`,
                                    }}
                                />
                                <div className={css.desktopOnlyContainer}>
                                    <div
                                        className={css.customerBannerImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(getImageUrl(item.quote_image_url), { width: 500 })})`,
                                        }}
                                    />
                                </div>
                            </div>
                            <h2 className={css.customerQuote}>
                                {item.article_highlight}
                            </h2>
                            <div className={css.responsiveOnlyContainer}>
                                <div
                                    className={css.customerBannerImage}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(getImageUrl(item.quote_image_url), { width: 500 })})`,
                                    }}
                                />
                            </div>
                        </div>
                        <div className={css.customerImages}>
                            {
                                (item.image_urls || '')
                                    .split(',')
                                    .map((imageUrl: any) => (
                                        <img key={`customer-image-${imageUrl}`} src={resizeImageUrl(getImageUrl(imageUrl), { width: 500 })} />
                                    ))
                            }
                        </div>
                    </div>
                </div>
            </AppContainer>
        )
    }

    return (
        <div className={css.CustomerDetailPage}>
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                {getPageContent()}
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const { customerSlug: itemId } = params;
    if (itemId && itemId !== "undefined") {
        try {
            const item = await apiCore.get(null, `v1/business_verified/${itemId}`)
            if (item) {
                const {
                    company: title,
                    article_content: highlight,
                    cover_image_url: coverImageUrl,
                } = item
                const pageTitle = `${title} | ${getLocaleMessages(locale)?.[`app_title`]} ${getLocaleMessages(locale)?.[`customers_page_title`]}`
                const pageMetaTagItemMap: any = {
                    'og-type': {
                        property: "og:type",
                        content: "article",
                    },
                    'og-title': {
                        property: "og:title",
                        content: pageTitle,
                    },
                }
                if (highlight) {
                    pageMetaTagItemMap['og-description'] = {
                        property: "og:description",
                        content: highlight,
                    }
                }
                if (coverImageUrl) {
                    pageMetaTagItemMap['og-image'] = {
                        property: "og:image",
                        content: resizeImageUrl(getImageUrl(coverImageUrl), { width: 600 }),
                    }
                }
                return {
                    props: {
                        pageTitle,
                        pageMetaTagItemMap,
                        item,
                    },
                };
            }
        } catch (error) {
            console.log(`error for v1/business_verified/slug/${itemId}`, error);
        }
    }
    return { props: {} };
}

export async function getStaticPaths() {
    return { paths: [], fallback: "blocking" };
}

export default CustomerDetailPage

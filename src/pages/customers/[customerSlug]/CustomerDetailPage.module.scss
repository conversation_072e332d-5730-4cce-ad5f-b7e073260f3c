.CustomerDetailPage {

    .responsiveOnlyContainer {
        display: flex;

        @include responsive('md') {
            display: none;
        }
    }

    .desktopOnlyContainer {
        display: none;

        @include responsive('md') {
            display: flex;
        }
    }

    .pageBreadcrumb {
        margin-bottom: 12px;
        padding: 12px 0;

        @include responsive('md') {
            margin-bottom: 24px;
            padding: 24px 0;
        }
    }

    .customerContentLayout {
        display: grid;
        column-gap: 48px;
        row-gap: 24px;
        margin-bottom: 24px;

        @include responsive('md') {
            grid-template-columns: 300px 1fr;
        }


        .customerMetadataContainer {
            border: 1px solid $color-separator-white-2;
            border-radius: $border-radius-theme-1;
            background-color: $color-app-gray-900;
            color: $color-app-gray-400;
            height: fit-content;
            font-size: 14px;
            width: 100%;
            margin-bottom: 24px;
            overflow: hidden;

            @include responsive('md') {}

            .customerLogo {
                padding: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-bottom: 1px solid $color-separator-white-2;

                img {
                    width: 100%;
                    max-width: 140px;
                }
            }

            .customerProduct {
                padding: 12px 24px;
                border-bottom: 1px solid $color-separator-white-2;

                @include responsive('md') {
                    padding: 24px 24px;
                }

                .fieldTitle {
                    color: $color-app-gray-500;
                    margin-bottom: 6px;
                    font-size: 14px;

                    @include responsive('md') {
                        margin-bottom: 12px;
                    }
                }

                .fieldValue {
                    white-space: pre-line;
                    display: flex;
                    flex-direction: column;
                    row-gap: 10px;

                    .productUsed {
                        font-size: 12px;
                        border-radius: $border-radius-theme-1;
                        background-color: $color-app-gray-700;
                        padding: 3px 12px;
                        width: fit-content;
                    }
                }
            }

            .customerRegion {
                padding: 12px 24px;
                border-bottom: 1px solid $color-separator-white-2;
                display: flex;
                column-gap: 6px;

                @include responsive('md') {
                    padding: 24px 24px;
                }
            }

            .customerWebsite {
                padding: 12px 24px;
                display: flex;
                column-gap: 6px;
                border-top: 1px solid $color-separator-white-2;
                transition: all 0.25s ease-in;
                cursor: pointer;

                @include responsive('md') {
                    padding: 24px 24px;
                }

                &:hover {
                    color: $color-app-gray-100;
                }
            }

            .customerType {
                padding: 12px 24px;
                display: flex;
                column-gap: 6px;
                // border: 1px solid $color-separator-white-2;

                @include responsive('md') {
                    padding: 24px 24px;
                }
            }
        }



        .customerContentContainer {

            color: $color-app-white;

            .customerHeader {
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                margin-bottom: 48px;
                align-items: center;

                @include responsive('md') {
                    align-items: flex-start;
                    row-gap: 16px;
                }

                .subtitle {
                    width: 100%;
                    color: #fff;
                    font-size: 14px;
                    line-height: 22px;
                    font-weight: bold;
                    // text-align: center;
                    background: linear-gradient(270deg,
                            rgb(89, 119, 255) 0%,
                            rgb(49, 255, 215) 101.39%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    text-align: left;

                    @include responsive('md') {
                        text-align: unset;
                        margin-bottom: 12px;
                        font-size: 20px;
                    }
                }

                .title {
                    width: 100%;
                    color: #fff;
                    font-size: 30px;
                    text-align: left;
                    font-weight: 800;
                    // text-transform: uppercase;

                    @include responsive('md') {
                        font-size: 40px;
                        line-height: 50px;
                        text-align: unset;
                    }
                }

                .description {
                    width: 100%;
                    color: #fff;
                    font-size: 16px;
                    line-height: 24px;
                    text-align: left;
                    opacity: 0.6;
                    white-space: pre-line;

                    @include responsive('md') {
                        text-align: unset;
                        font-size: 16px;
                        line-height: 26px;
                    }
                }
            }

            .customerContent {
                margin-bottom: 48px;
                font-size: 16px;
                line-height: 26px;
                white-space: pre-line;
            }

            .customerBanner {
                border-radius: $border-radius-theme-1;
                border: 1px solid $color-separator-white-1;
                display: grid;
                font-size: 16px;
                line-height: 26px;
                margin-bottom: 48px;
                overflow: hidden;
                position: relative;

                @include responsive('md') {
                    // padding: 48px;
                    grid-template-columns: 2fr 1fr;
                }

                .customerBannerBackground {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    display: grid;

                    @include responsive('md') {
                        // padding: 48px;
                        grid-template-columns: 2fr 1fr;
                    }

                    .overlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                    }
                }

                .customerBannerImage {
                    position: relative;
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: cover;
                    position: relative;
                    width: 100%;
                    height: 100%;
                    padding-bottom: 70%;

                    @include responsive('md') {
                        padding-bottom: unset;
                    }
                }

                .customerQuote {
                    padding: 24px;
                    white-space: pre-line;
                    position: relative;

                    @include responsive('md') {
                        padding: 48px;
                    }
                }
            }

            .customerImages {
                display: flex;
                flex-direction: column;
                row-gap: 48px;
                margin-bottom: 48px;

                &.responsive {}

                &.desktop {}

                img {
                    border: 1px solid $color-separator-white-1;
                    border-radius: $border-radius-theme-1;
                    width: 100%;
                }
            }
        }
    }
}
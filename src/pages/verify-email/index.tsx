import { useState } from "react";
import Image from "next/image";
import Head from "next/head";
import { useRouter } from "next/router";
import Header from "components/Login/Header";

export default function VerifyEmail() {
  const router = useRouter();
  const { query } = router;
  const email = query.email as string;
  const [isResending, setIsResending] = useState(false);

  const handleResendEmail = async () => {
    setIsResending(true);
    try {
    } catch (error) {
      console.error("Failed to resend email", error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="h-screen bg-black text-white flex flex-col items-center">
      <Head>
        <title>Check Your Email | LEGIT APP</title>
      </Head>

      {/* Header */}
      <Header />

      {/* Content */}
      <div className="w-full max-w-xl flex flex-col items-center mt-[65px] sm:px-0 px-6">
        {/* email icon */}
        <div className="sm:mb-10 mb-8">
          <Image
            src="/login/email-icon.svg"
            alt="Email"
            width={160}
            height={160}
            className="object-contain sm:w-40 sm:h-40 w-32 h-32"
          />
        </div>

        <h1 className="sm:text-[52px] text-3xl font-[900] text-center mb-6">
          CHECK YOUR EMAIL
        </h1>

        <div className="w-full bg-gray-200 rounded-lg sm:px-6 sm:py-6 px-4 py-3 sm:mb-12 mb-8 flex items-center">
          <div className="bg-white rounded-full sm:w-12 sm:h-12 w-8 h-8 flex items-center justify-center mr-4 flex-shrink-0">
            <span className="text-black font-bold">!</span>
          </div>
          <p className="sm:text-base text-sm">
            Follow the link in the email we sent to {email}. the email can take
            up to 1 minute to arrive
          </p>
        </div>

        <button
          onClick={handleResendEmail}
          disabled={isResending}
          className="w-full bg-btn-gradient font-bold px-4 py-3 rounded-full"
        >
          {isResending ? "Sending..." : "Resend email"}
        </button>
      </div>
    </div>
  );
}

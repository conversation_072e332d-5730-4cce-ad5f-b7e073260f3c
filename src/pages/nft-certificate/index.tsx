import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import NFTCertificateContent from 'components/NFTCertificateContent'
import { useIntl } from 'react-intl'
import { getLocaleMessages } from 'utils/locale'
import css from './NFTCertificatePage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

const NFTCertificatePage = ({ pageTitle }: any) => {
  const intl = useIntl()

  return (
    <div className={css.NFTCertificatePage}>
      <AppPageHiddenH1
        title={pageTitle}
      />
      <AppLayout>
        <AppHeader />
        <div className={css.contentContainer}>
          <NFTCertificateContent />
        </div>
        <DownloadSection />
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const pageTitle = `${getLocaleMessages(locale)?.[`nft_certificate_page_subtitle`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
  const pageDescription = getLocaleMessages(locale)?.[`nft_certificate_page_description`]
  const pageMetaTagItemMap: any = {
    'og-type': {
      property: "og:type",
      content: "article",
    },
    'og-title': {
      property: "og:title",
      content: pageTitle,
    },
    'og-description': {
      property: 'og:description',
      content: pageDescription,
    },
  }
  return {
    props: {
      pageTitle,
      pageMetaTagItemMap,
    }
  }
}

export default NFTCertificatePage

.ArticleDetailPage {
  background: #000;
  min-height: 100vh;

  .pageContainer {
    max-width: 800px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .articleItemContent {
    padding: 48px 0;
    margin-bottom: 36px;

    .pageBreadcrumb {
      margin-bottom: 12px;
    }

    .taglineContainer {
      // width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 16px;
      margin-left: 16px;
      margin-bottom: 6px;

      @include responsive('md') {
        margin-right: unset;
        margin-left: unset;
      }

      .item-tagline {
        letter-spacing: 1px;
        color: #555555;
        font-size: 10px;
        text-transform: uppercase;
        font-weight: 900;
        // margin-right: 16px;
        // margin-left: 16px;

        @include responsive('md') {
          margin-right: unset;
          margin-left: unset;
          font-size: 18px;
        }
      }

      .item-view-count {
        display: flex;
        align-items: center;
        font-size: 10px;

        @include responsive('md') {
          display: none;
        }

        .anticon {
          margin-right: 3px;
        }
      }
    }

    .itemTitle {
      color: #fff;
      // text-transform: uppercase;
      font-size: 24px;
      line-height: 28px;
      font-weight: 900;
      margin-bottom: 12px;

      @include responsive('md') {
        margin-right: unset;
        margin-left: unset;
        font-size: 35px;
        line-height: 40px;
      }
    }

    .itemSubtitle {
      font-size: 16px;
      color: #fff;
      // text-transform: uppercase;
      opacity: 0.6;
      margin-bottom: 12px;

      @include responsive('md') {
        margin-right: unset;
        margin-left: unset;
      }
    }

    .item-cover-image {
      // margin-right: 24px;
      flex-shrink: 0;
      display: block;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      width: 100%;
      margin-bottom: 24px;

      @include responsive('md') {
        margin-bottom: 36px;
      }

      img {
        width: 100%;
      }
    }

    .itemMetadata {
      // width: 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 24px;
      justify-content: space-between;

      @include responsive('md') {
        margin-right: unset;
        margin-left: unset;
        justify-content: space-between;
        flex-direction: row;
        margin-bottom: 24px;
        // align-items: center;
      }

      .itemDate {
        color: #fff;
        // text-transform: uppercase;
        opacity: 0.7;
        font-size: 14px;

        @include responsive('md') {
          font-size: 14px;
        }
      }

      .item-view-count {
        display: none;

        @include responsive('md') {
          display: flex;
          align-items: center;
        }

        .anticon {
          margin-right: 3px;
        }
      }
    }

    .item-highlight {
      background: #fafafa;
      padding: 24px;
      font-size: 16px;
      line-height: 32px;
      border-left: 4px solid #e6e5e5;
      margin-bottom: 24px;
      color: #444444;
      white-space: pre-line;

      @include responsive('md') {
        margin-right: unset;
        margin-left: unset;
      }
    }

    .itemContent {
      color: #fff;
      font-size: 16px;
      line-height: 34px;
      margin-bottom: 24px;
      white-space: pre-line;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: center;

      @include responsive('md') {
        margin-right: unset;
        margin-left: unset;
      }

      p {
        width: 100%;
        color: #fff;
        opacity: 1;
        // display: flex;
        // flex-direction: column;
        // align-items: center;
        // justify-content: center;
      }

      img {
        display: block;
        max-width: 100%;
        // max-height: 500px;
        height: auto;
        margin-right: auto;
        margin-left: auto;
        box-shadow: rgba(51, 51, 51, 0.15) 0px 4px 16px 0px;
        border: 1px solid #272c33;
        border-radius: 4px;
        overflow: hidden;
      }

      a {
        text-decoration: underline;
      }
    }

    .item-tag {
      margin-bottom: 12px;
      margin-right: 16px;
      margin-left: 16px;

      @include responsive('md') {
        margin-right: unset;
        margin-left: unset;
      }

      .ant-tag {
        background: #ebebeb;
        border: 0px;
        color: rgb(128, 128, 128);
        margin-bottom: 6px;
        font-size: 12px;
      }
    }

    .item-social {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      flex-wrap: wrap;
      margin-right: 16px;
      margin-left: 16px;

      @include responsive('md') {
        margin-bottom: 24px;
        margin-right: unset;
        margin-left: unset;
      }

      .share-social-title {
        color: #444444;
        display: none;

        @include responsive('md') {
          display: block;
        }
      }

      .ant-divider {
        height: 32px;
        display: none;
        margin: 0 12px;

        @include responsive('md') {
          display: block;
        }
      }

      .react-share__ShareButton {
        flex-shrink: 0;
        margin-right: 12px;
        border-radius: 3px;
        overflow: hidden;
        // margin-bottom: 12px;
        display: flex;
        align-items: center;
        cursor: pointer;

        @include responsive('md') {
          margin-bottom: unset;
        }

        &.share-facebook-button {
          // background-color: #3b5998;
        }

        &.share-twitter-button {
          // background-color: #00aced;
        }

        &.share-linkedin-button {
          // background-color: #007fb1;
        }

        .share-title {
          margin: 0 12px 0 0;
          color: #fff;
          font-weight: 900;
        }
      }
    }
  }

}
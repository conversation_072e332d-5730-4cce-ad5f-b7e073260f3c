// import getConfig from 'next/config'
import AppBreadcrumb from 'components/AppBreadcrumb'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import parse from 'html-react-parser'
import moment from 'moment'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import apiCore from 'utils/apiCore'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages, getLocalisedField } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './ArticleDetailPage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import { Article, BreadcrumbList, WithContext } from 'schema-dts'
import { APP_NAME, APP_URL } from 'constants/app'
import Script from 'next/script'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'

// const { publicRuntimeConfig } = getConfig()
// const { API_URL } = publicRuntimeConfig

const ArticleDetail = ({ item, pageTitle }: any) => {

  const intl = useIntl()
  const router = useRouter()
  // const dispatch = useAppDispatch();
  // const articleDetailState: any = ({} = useAppSelector(
  //   (state: RootState) => state.articleDetail
  // ));
  // const {
  //   item,
  //   isFetchItemLoading,
  //   fetchItemErrors,
  // } = articleDetailState
  const { locale = '', defaultLocale } = router
  // const articleSlug = _.get(router, 'query.articleSlug')

  // const refreshItem = () => {
  //   dispatch(fetchItem({
  //     slug: articleSlug,
  //   }))
  // }

  // useEffect(() => {
  //   if (router.isReady) {
  //     if (articleSlug) {
  //       refreshItem()
  //     }
  //   }
  // }, [router.isReady])

  // useEffect(() => {
  //   if (router.isReady && !!articleSlug && !!item && item.slug !== articleSlug) {
  //     props.reset()
  //     props.fetchItem({ slug: articleSlug })
  //   }
  // }, [articleSlug])

  const canonicalUrl = useCanonicalUrl()

  const pageSchema: WithContext<Article> = {
    "@context": "https://schema.org",
    "@type": "Article",
    url: canonicalUrl,
    headline: getLocalisedField(item, 'title', locale),
    articleBody: (item.content.trim() || '').replace(/legitapp-prod.oss-cn-hongkong.aliyuncs.com/g, 'legitapp-prod.oss-accelerate.aliyuncs.com'),
    description: getLocalisedField(item, 'highlight', locale),
    image: item.cover_image_url,
    datePublished: moment(item.created_at).format('YYYY-MM-DD'),
    dateModified: moment(item.updated_at).format('YYYY-MM-DD'),
    potentialAction: SCHEMA_POTENTIAL_ACTION,
  };

  const breadCrumbSchema: WithContext<BreadcrumbList> = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        "position": 1,
        "name": intl.formatMessage({ id: 'home_page_title' }),
        "item": [
          APP_URL,
          locale === defaultLocale ? '' : locale,
        ].filter(path => !!path)
          .join('/')
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": intl.formatMessage({ id: 'blog_page_title' }),
        "item": [
          APP_URL,
          locale === defaultLocale ? '' : locale,
          'blog',
        ].filter(path => !!path)
          .join('/'),
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": pageTitle,
        "item": [
          APP_URL,
          locale === defaultLocale ? '' : locale,
          'blog',
          item.slug,
        ].filter(path => !!path)
          .join('/'),
      },
    ],
  };

  return (
    <div className={css.ArticleDetailPage}>
      <Script
        id='blog-page-schema'
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(pageSchema),
        }}
      />
      <Script
        id='breadcrumb-schema'
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadCrumbSchema),
        }}
      />
      <AppPageHiddenH1
        title={pageTitle}
      />
      <AppLayout>
        <AppHeader />
        <AppContainer className={css.pageContainer}>
          {/* {
            !item && isFetchItemLoading && (
              <AppSpin />
            )
          } */}
          {/* {
            fetchItemErrors && fetchItemErrors.length > 0 && (
              <AppPlaceholder
                iconType='exclamation-circle'
                title={getErrorMessage(fetchItemErrors)}
              />
            )
          } */}
          {
            !!item && (
              <article className={css.articleItemContent}>
                <AppBreadcrumb
                  items={[
                    {
                      title: <Link href='/'>{intl.formatMessage({ id: `home_page_title` })}</Link>,
                    },
                    {
                      title: <Link href='/blog'>{intl.formatMessage({ id: `blog_page_title` })}</Link>,
                    },
                    {
                      title: getLocalisedField(item, 'title', locale),
                    }
                  ]}
                  className={css.pageBreadcrumb}
                />
                {
                  !!item.subtitle && (
                    <h2 className={css.itemSubtitle}>
                      {getLocalisedField(item, 'subtitle', locale)}
                    </h2>
                  )
                }
                <h2 className={css.itemTitle}>
                  {getLocalisedField(item, 'title', locale)}
                </h2>
                <div className={css.itemMetadata}>
                  <div className={css.itemDate}>
                    {moment(item.published_at || item.created_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).local().format('YYYY-MM-DD HH:mm')}
                  </div>
                </div>
                <div className={css.itemContent}>
                  {parse((item.content.trim() || '').replace(/legitapp-prod.oss-cn-hongkong.aliyuncs.com/g, 'legitapp-prod.oss-accelerate.aliyuncs.com'))}
                </div>
              </article>
            )
          }
        </AppContainer>
        <DownloadSection />
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const { articleSlug: itemId } = params;
  if (itemId && itemId !== "undefined") {
    try {
      const item = await apiCore.get(null, `v1/article/slug/${itemId}`)
      if (item) {
        const {
          title,
          highlight,
          cover_image_url: coverImageUrl,
        } = item
        const pageTitle = `${title} | ${getLocaleMessages(locale)?.[`app_title`]}`
        const pageMetaTagItemMap: any = {
          'og-type': {
            property: "og:type",
            content: "article",
          },
          'og-title': {
            property: "og:title",
            content: pageTitle,
          },
        }
        if (highlight) {
          pageMetaTagItemMap['og-description'] = {
            property: "og:description",
            content: highlight,
          }
        }
        if (coverImageUrl) {
          pageMetaTagItemMap['og-image'] = {
            property: "og:image",
            content: resizeImageUrl(getImageUrl(coverImageUrl), { width: 600 }),
          }
        }
        return {
          props: {
            pageTitle,
            pageMetaTagItemMap,
            item,
          },
        };
      }
    } catch (error) {
      console.log(`error for v1/article/slug/${itemId}`, error);
    }
  }
  return { props: {} };
}

export async function getStaticPaths() {
  return { paths: [], fallback: "blocking" };
}

export default ArticleDetail

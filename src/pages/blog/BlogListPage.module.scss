.BlogListPage {
  background: #000;
  min-height: 100vh;

  .blogListSection {}

  .pageContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .sectionHeader {
    padding: 48px 0 36px 0;

    @include responsive('md') {
      padding: 96px 0 60px 0;
    }
  }

  .articleListCardGrid {
    max-width: 800px;
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    column-gap: 24px;
    row-gap: 72px;
    margin-bottom: 24px;
    align-items: stretch;
    justify-content: stretch;

    @include responsive('sm') {
      // grid-template-columns: repeat(2, 1fr);
    }

    @include responsive('md') {
      // grid-template-columns: repeat(3, 1fr);
      row-gap: 72px;
    }
  }
}
import { fetchItems } from 'actions/articleList'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppListLoadMoreCard from 'components/AppListLoadMoreCard'
import AppPlaceholder from 'components/AppPlaceholder'
import AppSpin from 'components/AppSpin'
import ArticleListCard from 'components/ArticleListCard'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_URL, LIST_PAGESIZE } from 'constants/app'
import useAppDispatch from 'hooks/useAppDispatch'
import useAppSelector from 'hooks/useAppSelector'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { useIntl } from 'react-intl'
import { RootState } from 'reducers'
import { getErrorMessage } from 'utils/error'
import { getLocaleMessages } from 'utils/locale'
import { showErrorPopupMessage } from 'utils/message'
import css from './BlogListPage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import { BreadcrumbList, WithContext } from 'schema-dts'
import Script from 'next/script'

const BlogListPage = ({ pageTitle }: any) => {

  const intl = useIntl()
  const router = useRouter()
  const dispatch = useAppDispatch();
  const articleListState: any = ({} = useAppSelector(
    (state: RootState) => state.articleList
  ));
  const {
    items,
    isFetchItemsLoading,
    fetchItemsErrors,
    pagination = {},
  } = articleListState

  const {
    total: itemsTotal,
  } = pagination

  const { locale, defaultLocale } = router

  const refresh = () => {
    dispatch(fetchItems({
      $limit: LIST_PAGESIZE,
      language: locale,
    }))
  }

  useEffect(() => {
    if (!items) {
      refresh()
    }
  }, [])

  useEffect(() => {
    if (!isFetchItemsLoading && !!fetchItemsErrors) {
      const errorMessage = getErrorMessage(fetchItemsErrors)
      showErrorPopupMessage(errorMessage)
    }
  }, [fetchItemsErrors])

  const handleLoadMoreOnClick = () => {
    const currentItems = items || []
    dispatch(fetchItems({
      $offset: currentItems.length,
      $limit: LIST_PAGESIZE,
      language: locale,
    }, items))
  }

  const breadCrumbSchema: WithContext<BreadcrumbList> = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        "position": 1,
        "name": intl.formatMessage({ id: 'home_page_title' }),
        "item": [
          APP_URL,
          locale === defaultLocale ? '' : locale,
        ].filter(path => !!path)
          .join('/')
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": pageTitle,
        "item": [
          APP_URL,
          locale === defaultLocale ? '' : locale,
          'blog',
        ].filter(path => !!path)
          .join('/'),
      },
    ],
  };

  return (
    <div className={css.BlogListPage}>
      <Script
        id='breadcrumb-schema'
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadCrumbSchema),
        }}
      />
      <AppPageHiddenH1
        title={`${pageTitle}`}
      />
      <AppLayout>
        <AppHeader />
        <div className={css.blogListSection}>
          <AppContainer className={css.pageContainer}>
            <HomePageSectionHeader
              className={css.sectionHeader}
              subtitle={intl.formatMessage({ id: 'blog_page_blog_section_subtitle' })}
              title={intl.formatMessage({ id: 'blog_page_blog_section_title' })}
              description={<>
                {intl.formatMessage({ id: 'blog_page_blog_section_description' })}
                {/* <br /> */}
                {/* {intl.formatMessage({ id: 'home_page_pricing_section_description_2' })} */}
              </>}
            />
            <div className={css.articleList}>
              {
                (!items || items.length === 0) && isFetchItemsLoading && (
                  <AppSpin />
                )
              }
              {
                !isFetchItemsLoading && (items && items.length === 0) && (
                  <AppPlaceholder
                    iconType='meh'
                    title={intl.formatMessage({ id: 'placeholder_no_search_result' })}
                  />
                )
              }
              <div className={css.articleListCardGrid}>
                {
                  items && (
                    items.map((articleItem: any) => (
                      <ArticleListCard item={articleItem} key={`latest-article-${articleItem.id}-col`} />
                    ))
                  )
                }
              </div>
              {
                fetchItemsErrors && fetchItemsErrors.length > 0 && (
                  <div>
                    <AppPlaceholder
                      iconType='exclamation-circle'
                      title={getErrorMessage(fetchItemsErrors)}
                    />
                  </div>
                )
              }
              {
                items && (items.length < itemsTotal) && (
                  <div>
                    <AppListLoadMoreCard
                      onClick={handleLoadMoreOnClick}
                      loading={isFetchItemsLoading}
                    />
                  </div>
                )
              }
            </div>
          </AppContainer>
        </div>
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const pageTitle = `${getLocaleMessages(locale)?.[`app_title`]} ${getLocaleMessages(locale)?.[`blog_page_title`]} `
  const pageDescription = getLocaleMessages(locale)?.[`blog_page_blog_section_description`]
  const pageMetaTagItemMap = {
    'og-title': {
      property: "og:title",
      content: pageTitle,
    },
    'og-desciption': {
      property: 'og:description',
      content: pageDescription,
    }
  }
  return {
    props: {
      pageTitle,
      pageMetaTagItemMap,
    },
  };
}


export default BlogListPage

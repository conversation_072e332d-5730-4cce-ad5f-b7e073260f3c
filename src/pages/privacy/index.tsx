import React from 'react'
import AppLayout from 'components/AppLayout'
import privacyContent from 'utils/companyPrivacy.html'
import css from './PrivacyPage.module.scss'
import AppHeader from 'components/AppHeader'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import { getLocaleMessages } from 'utils/locale'

const CompanyPrivacy = () => (
  <div className={css.PrivacyPage}>
    <AppLayout>
      <AppHeader />
      <AppContainer>
        <div
          className={css.content}
          dangerouslySetInnerHTML={{ __html: privacyContent }}
        />
      </AppContainer>
      <AppFooter />
    </AppLayout>
  </div>
)

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`app_footer_about_section_item_company_privacy`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}

export default CompanyPrivacy

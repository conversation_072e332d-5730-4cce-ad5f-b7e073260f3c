.ContactPage {
    .contactSection {
        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        @include responsive('md') {
            // padding: 96px 0;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .contactCardGrid {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            max-width: 1000px;
            row-gap: 24px;
            column-gap: 24px;
            margin-bottom: 96px;

            @include responsive('md') {
                grid-template-columns: repeat(2, 1fr);
            }

            .contactCard {
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                border: 1px solid $color-separator-white-2;
                color: $color-app-white;
                border-radius: $border-radius-theme-2;
                padding: 24px;
                display: flex;
                row-gap: 24px;
                flex-direction: column;
                justify-content: space-between;

                @include responsive('md') {
                    padding: 40px;
                }

                &.card1 {

                    @include responsive('md') {
                        grid-row-start: 1;
                        grid-row-end: 3;
                    }
                }

                .cardHeader {
                    display: flex;
                    flex-direction: column;
                    row-gap: 12px;

                    .cardHeaderTitle {
                        font-weight: bold;
                        width: 100%;
                        font-size: 16px;
                        line-height: 26px;

                        @include responsive('md') {
                            font-size: 20px;
                            line-height: 30px;
                        }
                    }

                    .cardHeaderSubtitle {
                        opacity: 0.6;
                        margin-bottom: 12px;
                        width: 100%;
                        font-size: 14px;
                        line-height: 24px;

                        @include responsive('md') {
                            font-size: 16px;
                            line-height: 26px;
                        }
                    }
                }

                .cardContent {
                    width: 100%;

                    .contactForm {

                        label {
                            color: $color-app-white;
                            font-size: 12px;
                        }

                        .formLayout {
                            // display: grid;
                            // grid-template-columns: 1fr 100px;
                            // column-gap: 24px;
                        }

                        .formItemGrid {
                            display: grid;
                            grid-template-columns: repeat(2, 1fr);
                            row-gap: 24px;
                            column-gap: 24px;
                            margin-bottom: 12px;
                        }

                        .formItem {
                            margin-bottom: 0;
                        }

                        .formInput {
                            color: $color-app-white;
                            background-color: #000;
                            font-size: 16px;
                            height: 40px;
                            border: 1px solid $color-separator-white-2;

                            &::placeholder {
                                color: $color-app-white;
                                opacity: 0.6;
                                /* Firefox */
                            }

                            &::-ms-input-placeholder {
                                /* Edge 12 -18 */
                                color: $color-app-white;
                                opacity: 0.6;
                            }
                        }

                        .formRadio {
                            margin-bottom: 6px;
                        }

                        .formInputArea {
                            color: $color-app-white;
                            background-color: #000;
                            font-size: 16px;
                            // height: 40px;
                            border: 1px solid $color-separator-white-2;

                            &::placeholder {
                                color: $color-app-white;
                                opacity: 0.6;
                                /* Firefox */
                            }

                            &::-ms-input-placeholder {
                                /* Edge 12 -18 */
                                color: $color-app-white;
                                opacity: 0.6;
                            }
                        }

                        .formSelect {
                            color: $color-app-white;
                            background-color: #000;
                            font-size: 16px;
                            // height: 40px;
                            border: 1px solid $color-separator-white-2;

                            &::placeholder {
                                color: $color-app-white;
                                opacity: 0.6;
                                /* Firefox */
                            }

                            &::-ms-input-placeholder {
                                /* Edge 12 -18 */
                                color: $color-app-white;
                                opacity: 0.6;
                            }
                        }


                        .formSubmitButton {
                            width: 100%;
                            background-color: $color-app-white;
                            color: $color-app-dark;
                            font-weight: bold;
                            height: 50px;
                        }
                    }

                    .actionButton {
                        width: 100%;
                        cursor: pointer;
                        padding: 10px 12px;
                        background-color: $color-app-white;
                        border-radius: 4px;
                        color: #000;
                        font-weight: bold;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        // justify-content: space-between;
                        column-gap: 12px;
                        font-size: 14px;

                        @include responsive('md') {
                            font-size: 16px;
                        }
                    }
                }
            }
        }
    }
}
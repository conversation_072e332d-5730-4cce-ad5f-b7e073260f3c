import { CustomerServiceFilled, MailFilled, MessageFilled } from '@ant-design/icons'
import { Button, Checkbox, Form, Input, Radio, Select } from 'antd'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_DISCORD_URL } from 'constants/app'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { useIntl } from 'react-intl'
import apiCore from 'utils/apiCore'
import { parseError } from 'utils/error'
import { tagContactFormEvent } from 'utils/gtag'
import { getLocaleMessages, getLocalisedField } from 'utils/locale'
import { showErrorPopupMessage, showSuccessPopupMessage } from 'utils/message'
import css from './ContactPage.module.scss'
import PRODUCT_CATEGORY from 'data/productCategory'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

const ContactPage = ({ pageTitle }: any) => {

    const intl = useIntl()
    const router = useRouter()
    const { locale } = router
    const [form] = Form.useForm()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const onFormFinish = async (values: any) => {

        const {
            first_name: firstName,
            last_name: lastName,
            company,
            company_email: companyEmail,
            job_title: jobTitle,
            phone_number: phoneNumber,
            country: country,
            monthly_authentication_count: monthlyAuthenticationCount,
            category_interested: categoryInterested,
            // subject,
            content,
        } = values

        // console.log('values', values)

        // return null

        if (!companyEmail) {
            showErrorPopupMessage(intl.formatMessage({ id: `contact_page_validation_message` }))
            return
        }

        if (!firstName || !lastName || !monthlyAuthenticationCount) {
            showErrorPopupMessage(intl.formatMessage({ id: `contact_page_required_message` }))
            return
        }

        const data = {
            first_name: firstName,
            last_name: lastName,
            company,
            job_title: jobTitle,
            phone_number: phoneNumber,
            company_email: companyEmail,
            monthly_authentication_count: monthlyAuthenticationCount,
            category_interested: (categoryInterested || []).join(','),
            // subject,
            content,
        }
        // console.log('data', data)
        try {
            setIsSubmitting(true)
            // search as tag id
            const result = await apiCore.post(null, `v1/contact`, data)
            setIsSubmitting(false)
            showSuccessPopupMessage(intl.formatMessage({ id: `contact_page_success_message` }))
            tagContactFormEvent()
            form?.resetFields([
                `first_name`,
                `last_name`,
                `company`,
                `job_title`,
                `phone_number`,
                `company_email`,
                `monthly_authentication_count`,
                `category_interested`,
                // `subject`,
                `content`,
            ])
        } catch (error) {
            setIsSubmitting(false)
            showErrorPopupMessage(parseError(error).message)
        }
    }

    return (
        <div className={css.ContactPage}>
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.contactSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={css.sectionHeader}
                            title={intl.formatMessage({ id: `contact_page_title` })}
                            subtitle={intl.formatMessage({ id: `contact_page_subtitle` })}
                            description={intl.formatMessage({ id: `contact_page_description` })}
                        />
                        <div className={css.contactCardGrid}>
                            <div className={clsx(css.contactCard, css.card1)}>
                                <div className={css.cardHeader}>
                                    <MailFilled />
                                    <h2 className={css.cardHeaderTitle}>
                                        {intl.formatMessage({ id: 'contact_page_item_1_title' })}
                                    </h2>
                                    <h3 className={css.cardHeaderSubtitle}>
                                        {intl.formatMessage({ id: 'contact_page_item_1_subtitle' })}
                                    </h3>
                                </div>
                                <div className={css.cardContent}>
                                    <Form
                                        rootClassName={css.contactForm}
                                        onFinish={onFormFinish}
                                        // onFinishFailed={onFormFinishFailed}
                                        autoComplete='off'
                                        layout='vertical'
                                        className={css.form}
                                        requiredMark={false}
                                        form={form}
                                    >
                                        <div className={css.formLayout}>
                                            <div className={css.formItemGrid}>
                                                {
                                                    locale === `zh-Hant` || locale === `zh-Hans`
                                                        ? (
                                                            <>
                                                                <Form.Item
                                                                    rootClassName={css.formItem}
                                                                    name='last_name'
                                                                    label={intl.formatMessage({ id: `contact_page_last_name` })}
                                                                >
                                                                    <Input
                                                                        rootClassName={css.formInput}
                                                                        placeholder={intl.formatMessage({ id: 'contact_page_required' })}
                                                                    />
                                                                </Form.Item>
                                                                <Form.Item
                                                                    rootClassName={css.formItem}
                                                                    name='first_name'
                                                                    label={intl.formatMessage({ id: `contact_page_first_name` })}
                                                                >
                                                                    <Input
                                                                        rootClassName={css.formInput}
                                                                        placeholder={intl.formatMessage({ id: 'contact_page_required' })}
                                                                    />
                                                                </Form.Item>
                                                            </>
                                                        )
                                                        : (
                                                            <>
                                                                <Form.Item
                                                                    rootClassName={css.formItem}
                                                                    name='first_name'
                                                                    label={intl.formatMessage({ id: `contact_page_first_name` })}
                                                                >
                                                                    <Input
                                                                        rootClassName={css.formInput}
                                                                        placeholder={intl.formatMessage({ id: 'contact_page_required' })}
                                                                    />
                                                                </Form.Item>
                                                                <Form.Item
                                                                    rootClassName={css.formItem}
                                                                    name='last_name'
                                                                    label={intl.formatMessage({ id: `contact_page_last_name` })}
                                                                >
                                                                    <Input
                                                                        rootClassName={css.formInput}
                                                                        placeholder={intl.formatMessage({ id: 'contact_page_required' })}
                                                                    />
                                                                </Form.Item>
                                                            </>
                                                        )
                                                }
                                            </div>
                                            <div className={css.formItemGrid}>
                                                <Form.Item
                                                    rootClassName={css.formItem}
                                                    name='company'
                                                    label={intl.formatMessage({ id: `contact_page_company` })}
                                                >
                                                    <Input
                                                        rootClassName={css.formInput}
                                                    // placeholder={intl.formatMessage({ id: 'search_certificate_page_search_input_placeholder' })}
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    rootClassName={css.formItem}
                                                    name='job_title'
                                                    label={intl.formatMessage({ id: `contact_page_job_title` })}
                                                >
                                                    <Input
                                                        rootClassName={css.formInput}
                                                    // placeholder={intl.formatMessage({ id: 'search_certificate_page_search_input_placeholder' })}
                                                    />
                                                </Form.Item>
                                            </div>
                                            <div className={css.formItemGrid}>
                                                <Form.Item
                                                    rootClassName={css.formItem}
                                                    name='phone_number'
                                                    label={intl.formatMessage({ id: `contact_page_phone` })}
                                                >
                                                    <Input
                                                        rootClassName={css.formInput}
                                                    // placeholder={intl.formatMessage({ id: 'search_certificate_page_search_input_placeholder' })}
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    rootClassName={css.formItem}
                                                    name='company_email'
                                                    label={intl.formatMessage({ id: `contact_page_company_email` })}
                                                >
                                                    <Input
                                                        rootClassName={css.formInput}
                                                        placeholder={intl.formatMessage({ id: 'contact_page_required' })}
                                                    />
                                                </Form.Item>
                                            </div>
                                            <Form.Item
                                                rootClassName={css.formItem}
                                                style={{ marginBottom: `12px` }}
                                                name='monthly_authentication_count'
                                                label={intl.formatMessage({ id: `contact_page_monthly_authentication_count` })}
                                            >
                                                <Input
                                                    rootClassName={css.formInput}
                                                    placeholder={intl.formatMessage({ id: 'contact_page_required' })}
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                rootClassName={css.formItem}
                                                style={{ marginBottom: `12px` }}
                                                name='category_interested'
                                                label={intl.formatMessage({ id: `contact_page_category_interested` })}
                                            >
                                                <Checkbox.Group
                                                    options={
                                                        PRODUCT_CATEGORY.map(
                                                            categoryItem => `${getLocalisedField(categoryItem, 'title', locale)}`
                                                        ) as any
                                                    }
                                                />
                                                {/* <Checkbox.Group>
                                                    {
                                                        PRODUCT_CATEGORY.map(
                                                            categoryItem => (
                                                                <Radio
                                                                    rootClassName={css.formRadio}
                                                                    value={getLocalisedField(categoryItem, 'title', locale)}
                                                                    key={`category-interested-${categoryItem.title}`}
                                                                >
                                                                    {getLocalisedField(categoryItem, 'title', locale)}
                                                                </Radio>
                                                            )) as any
                                                    }
                                                </Checkbox.Group> */}
                                            </Form.Item>
                                            {/* <Form.Item
                                                rootClassName={css.formItem}
                                                style={{ marginBottom: `12px` }}
                                                name='subject'
                                                label={intl.formatMessage({ id: `contact_page_subject` })}
                                            >
                                                <Input
                                                    rootClassName={css.formInput}
                                                // placeholder={intl.formatMessage({ id: 'search_certificate_page_search_input_placeholder' })}
                                                />
                                            </Form.Item> */}
                                            <Form.Item
                                                rootClassName={css.formItem}
                                                style={{ marginBottom: `24px` }}
                                                name='content'
                                                label={intl.formatMessage({ id: `contact_page_how_can_we_help` })}
                                            >
                                                <Input.TextArea
                                                    rows={3}
                                                    rootClassName={css.formInputArea}
                                                // placeholder={intl.formatMessage({ id: 'search_certificate_page_search_input_placeholder' })}
                                                />
                                            </Form.Item>
                                            <Form.Item rootClassName={css.formItem}>
                                                <Button
                                                    className={css.actionButton}
                                                    type='primary'
                                                    htmlType='submit'
                                                    size='large'
                                                    loading={isSubmitting}
                                                >
                                                    {intl.formatMessage({ id: 'contact_page_subtitle' })}
                                                </Button>
                                            </Form.Item>
                                        </div>
                                    </Form >
                                </div>
                            </div>
                            <div className={css.contactCard}>
                                <div className={css.cardHeader}>
                                    <CustomerServiceFilled />
                                    <h2 className={css.cardHeaderTitle}>
                                        {intl.formatMessage({ id: 'contact_page_item_2_title' })}
                                    </h2>
                                    <h3 className={css.cardHeaderSubtitle}>
                                        {intl.formatMessage({ id: 'contact_page_item_2_subtitle' })}
                                    </h3>
                                </div>
                                <div className={css.cardContent}>
                                    <a
                                        href={'mailto:<EMAIL>'}
                                        className={css.actionButton}
                                        target='_top'
                                    >
                                        <EMAIL>
                                    </a>
                                </div>
                            </div>
                            <div className={css.contactCard}>
                                <div className={css.cardHeader}>
                                    <MessageFilled />
                                    <h2 className={css.cardHeaderTitle}>
                                        {intl.formatMessage({ id: 'contact_page_item_3_title' })}
                                    </h2>
                                    <h3 className={css.cardHeaderSubtitle}>
                                        {intl.formatMessage({ id: 'contact_page_item_3_subtitle' })}
                                    </h3>
                                </div>
                                <div className={css.cardContent}>
                                    <a
                                        target='_blank'
                                        rel='noopener noreferrer'
                                        href={APP_DISCORD_URL}
                                        className={css.actionButton}
                                    >
                                        {intl.formatMessage({ id: 'contact_page_item_3_button' })}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </AppContainer>
                </div>
                <AppFooter />
            </AppLayout >
        </div >
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`contact_page_subtitle`]} ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}


export default ContactPage


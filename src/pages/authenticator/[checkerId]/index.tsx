// import getConfig from 'next/config'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import apiCore from 'utils/apiCore'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './CheckerProfilePage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

// const { publicRuntimeConfig } = getConfig()
// const { API_URL } = publicRuntimeConfig

const CheckerProfilePage = ({ item, pageTitle }: any) => {

    const intl = useIntl()
    const router = useRouter()
    // const dispatch = useAppDispatch();
    // const articleDetailState: any = ({} = useAppSelector(
    //   (state: RootState) => state.articleDetail
    // ));
    // const {
    //   item,
    //   isFetchItemLoading,
    //   fetchItemErrors,
    // } = articleDetailState
    const { locale = '' } = router
    // const articleSlug = _.get(router, 'query.articleSlug')

    // const refreshItem = () => {
    //   dispatch(fetchItem({
    //     slug: articleSlug,
    //   }))
    // }

    // useEffect(() => {
    //   if (router.isReady) {
    //     if (articleSlug) {
    //       refreshItem()
    //     }
    //   }
    // }, [router.isReady])

    // useEffect(() => {
    //   if (router.isReady && !!articleSlug && !!item && item.slug !== articleSlug) {
    //     props.reset()
    //     props.fetchItem({ slug: articleSlug })
    //   }
    // }, [articleSlug])

    const getPageContent = () => {
        if (!item) {
            return null
        }

        return (
            <>
                <HomePageSectionHeader
                    className={css.sectionHeader}
                    subtitle={item.headline.en}
                    title={`${item.name}`}
                    description={`${intl.formatMessage({ id: `request_detail_page_authenticator` })} #${item.id}`}
                // description={<>
                // <div
                //     className={css.avatar}
                //     style={{
                //         backgroundImage: `url(${item.profile_image_url})`,
                //     }}
                // />
                // <br/>
                // {item.description.en}
                // </>}
                />
                <div className={css.profileCard}>
                    <div
                        className={css.avatar}
                        style={{
                            backgroundImage: `url(${item.profile_image_url})`,
                        }}
                    />
                    <br />
                    {/* <div className={css.profileTitle}>
                        {item.name}
                    </div> */}
                    {/* <div className={css.profileSubtitle}>
                        {item.headline.en}
                    </div> */}
                    <div className={css.profileDescription}>
                        {item.description.en}
                    </div>
                </div>
                {
                    !!item.category && (
                        <div className={css.categorySection}>
                            <div className={css.sectionHeader}>
                                <div className={css.headerTitle}>
                                    {intl.formatMessage({ id: `checker_profiel_page_category_section_title` }, { name: item.name })}
                                </div>
                            </div>
                            <div className={css.categoryItemGrid}>
                                {
                                    item.category.map((categoryItem: any) => (
                                        //     <Link
                                        //     href={`/what-we-authenticate/${brandItem.slug}`}
                                        //     key={`what-we-authenticate-product-category-section-${productCategory.id}-brand-${brandItem.id}`}
                                        // >
                                        <div
                                            className={css.catgoryItem}
                                            key={`checker-profile-page-category-section-${item.checker_id}-brand-${categoryItem.title}`}
                                        >
                                            <div
                                                className={css.catgoryImage}
                                                style={{
                                                    backgroundImage: `url(${resizeImageUrl(getImageUrl(categoryItem?.icon_image_url), { width: 100 })})`,
                                                }}
                                            />
                                            <div className={css.catgoryTitle}>
                                                {categoryItem?.title}
                                            </div>
                                            {/* <div className={css.link}>
                                                <CaretRightFilled className={css.arrowIcon} />
                                            </div> */}
                                        </div>
                                        // </Link>
                                    ))
                                }
                            </div>
                        </div>
                    )
                }
                {
                    !!item.brand && (
                        <div className={css.brandSection}>
                            <div className={css.sectionHeader}>
                                <div className={css.headerTitle}>
                                    {intl.formatMessage({ id: `checker_profiel_page_brand_section_title` }, { name: item.name })}
                                </div>
                            </div>
                            <div className={css.brandItemGrid}>
                                {
                                    item.brand.map((brandItem: any) => (
                                        //     <Link
                                        //     href={`/what-we-authenticate/${brandItem.slug}`}
                                        //     key={`what-we-authenticate-product-category-section-${productCategory.id}-brand-${brandItem.id}`}
                                        // >
                                        <div
                                            className={css.brandItem}
                                            key={`checker-profile-page-brand-section-${item.checker_id}-brand-${brandItem.title}`}
                                        >
                                            <div
                                                className={css.brandImage}
                                                style={{
                                                    backgroundImage: `url(${resizeImageUrl(getImageUrl(brandItem?.icon_image_url), { width: 100 })})`,
                                                }}
                                            />
                                            <div className={css.brandTitle}>
                                                {brandItem?.title}
                                            </div>
                                            {/* <div className={css.link}>
                                                <CaretRightFilled className={css.arrowIcon} />
                                            </div> */}
                                        </div>
                                        // </Link>
                                    ))
                                }
                            </div>
                        </div>
                    )
                }
            </>
        )
    }

    return (
        <div className={css.CheckerProfilePage}>
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <AppContainer className={css.pageContainer}>
                    {getPageContent()}
                </AppContainer>
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const { checkerId } = params;
    if (checkerId && checkerId !== "undefined") {
        try {
            const item = await apiCore.get(null, `v1/checker/${checkerId}`)
            if (item) {
                const {
                    name,
                    headline,
                    description,
                    profile_image_url: profileImageUrl,
                } = item
                const pageTitle = `${name}, ${headline?.en} ${getLocaleMessages(locale)?.[`app_title`]}`
                const pageMetaTagItemMap: any = {
                    'og-type': {
                        property: "og:type",
                        content: "article",
                    },
                    'og-title': {
                        property: "og:title",
                        content: pageTitle,
                    }
                }
                if (description?.en) {
                    pageMetaTagItemMap['og-description'] = {
                        property: "og:description",
                        content: description?.en,
                    }
                }
                if (profileImageUrl) {
                    pageMetaTagItemMap['og-image'] = {
                        property: "og:image",
                        content: profileImageUrl,
                    }
                }
                return {
                    props: {
                        pageTitle,
                        pageMetaTagItemMap,
                        item,
                    },
                };
            }
        } catch (error) {
            console.log(`error for v1/checker/${checkerId}`, error);
        }
    }
    return { props: {} };
}

export async function getStaticPaths() {
    return { paths: [], fallback: "blocking" };
}

export default CheckerProfilePage

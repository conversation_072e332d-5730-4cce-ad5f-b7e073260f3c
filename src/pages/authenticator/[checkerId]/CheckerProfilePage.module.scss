.CheckerProfilePage {

    .pageContainer {
        // padding: 48px 0;
        max-width: 800px;
        padding-top: 48px;
        padding-bottom: 48px;

        @include responsive('md') {
            padding-top: 48px;
            padding-bottom: 48px;
        }
    }

    .sectionHeader {
        margin-bottom: 24px;
    }

    .profileCard {
        background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
        border-radius: $border-radius-theme-2;
        padding: 24px;
        color: $color-app-white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;

        @include responsive('md') {
            padding: 40px;
        }

        .avatar {
            width: 100px;
            height: 100px;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
            border-radius: 50%;
            overflow: hidden;
            border: 1px solid $color-separator-white-2;
        }

        .profileTitle {
            width: fit-content;
            color: #fff;
            font-size: 20px;
            text-align: center;
            font-weight: 800;
            text-transform: uppercase;
            margin-bottom: 12px;

            @include responsive('md') {
                font-size: 40px;
                line-height: 40px;
            }
        }

        .profileSubtitle {
            width: fit-content;
            color: #fff;
            font-size: 12px;
            line-height: 22px;
            text-align: center;

            @include responsive('md') {
                margin-bottom: 12px;
                font-size: 20px;
            }
        }

        .profileDescription {
            width: fit-content;
            color: #fff;
            font-size: 14px;
            opacity: 0.6;
            max-width: 700px;
            line-height: 24px;

            @include responsive('md') {
                font-size: 16px;
                line-height: 26px;
            }
        }
    }

    .categorySection {
        width: 100%;
        margin-bottom: 24px;

        .sectionHeader {
            padding: 24px 0;

            .headerTitle {
                width: 100%;
                color: #fff;
                font-size: 20px;
                font-weight: bold;
                text-transform: uppercase;
            }
        }

        .categoryItemGrid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            width: 100%;

            @include responsive('md') {
                grid-template-columns: repeat(4, 1fr);
            }

            .catgoryItem {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 24px;
                padding-top: 48px;
                row-gap: 24px;
                width: 100%;
                height: 100%;
                border: 0.5px solid $color-separator-white-1;
                transition: all 0.25s ease-in;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                border-radius: $border-radius-theme-2;
                position: relative;

                // &:hover {
                //     background-color: $color-app-header-dropdown-menu-item-hover-background;

                //     .link {
                //         opacity: 1;
                //         right: 16px;
                //     }
                // }

                .catgoryImage {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-size: contain;
                    background-position: center;
                    height: 100px;
                    width: 100px;
                    // background-color: red;
                }

                .catgoryTitle {
                    width: 100%;
                    color: #fff;
                    font-size: 10px;
                    text-align: center;
                    font-weight: bold;
                    text-transform: uppercase;

                    @include responsive('md') {
                        font-size: 14px;
                    }
                }

                .link {
                    font-size: 12px;
                    background-color: $color-app-white;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                    position: absolute;
                    top: 16px;
                    right: 24px;
                    opacity: 0;
                    transition: all 0.25s ease-in;

                    .arrowIcon {
                        font-weight: bold;
                        color: $color-app-gray-900;
                        font-size: 12px;
                    }
                }
            }
        }
    }

    .brandSection {
        width: 100%;
        margin-bottom: 24px;

        .sectionHeader {
            padding: 24px 0;

            .headerTitle {
                width: 100%;
                color: #fff;
                font-size: 20px;
                font-weight: bold;
                text-transform: uppercase;
            }
        }

        .brandItemGrid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            width: 100%;

            @include responsive('md') {
                grid-template-columns: repeat(4, 1fr);
            }

            .brandItem {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 24px;
                padding-top: 48px;
                row-gap: 24px;
                width: 100%;
                height: 100%;
                border: 0.5px solid $color-separator-white-1;
                transition: all 0.25s ease-in;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                border-radius: $border-radius-theme-2;
                position: relative;

                // &:hover {
                //     background-color: $color-app-header-dropdown-menu-item-hover-background;

                //     .link {
                //         opacity: 1;
                //         right: 16px;
                //     }
                // }

                .brandImage {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-size: contain;
                    background-position: center;
                    height: 100px;
                    width: 100px;
                    // background-color: red;
                }

                .brandTitle {
                    width: 100%;
                    color: #fff;
                    font-size: 10px;
                    text-align: center;
                    font-weight: bold;
                    text-transform: uppercase;

                    @include responsive('md') {
                        font-size: 14px;
                    }
                }

                .link {
                    font-size: 12px;
                    background-color: $color-app-white;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                    position: absolute;
                    top: 16px;
                    right: 24px;
                    opacity: 0;
                    transition: all 0.25s ease-in;

                    .arrowIcon {
                        font-weight: bold;
                        color: $color-app-gray-900;
                        font-size: 12px;
                    }
                }
            }
        }
    }
}
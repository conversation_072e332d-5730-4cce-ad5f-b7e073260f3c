import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import termsContent from 'utils/companyTerms.html'
import css from './TermsPage.module.scss'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import { getLocaleMessages } from 'utils/locale'

const CompanyTerms = () => (
  <div className={css.TermsPage}>
    <AppLayout>
      <AppHeader />
      <AppContainer>
        <div
          className={css.content}
          dangerouslySetInnerHTML={{ __html: termsContent }}
        />
      </AppContainer>
      <AppFooter />
    </AppLayout>
  </div>
)

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`app_footer_about_section_item_company_term`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}

export default CompanyTerms

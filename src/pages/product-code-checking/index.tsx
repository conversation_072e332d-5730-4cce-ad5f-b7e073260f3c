import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import ProductCodeCheckingContent from 'components/ProductCodeCheckingContent'
import { useIntl } from 'react-intl'
import { getLocaleMessages } from 'utils/locale'
import css from './ProductCodeCheckingPage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

const ProductCodeCheckingPage = ({ pageTitle }: any) => {
  const intl = useIntl()

  return (
    <div className={css.ProductCodeCheckingPage}>
      <AppPageHiddenH1
        title={pageTitle}
      />
      <AppLayout>
        <AppHeader />
        <div className={css.contentContainer}>
          <ProductCodeCheckingContent />
        </div>
        <DownloadSection />
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const pageTitle = `${getLocaleMessages(locale)?.[`product_code_checking_page_subtitle`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
  const pageDescription = getLocaleMessages(locale)?.[`product_code_checking_page_description`]
  const pageMetaTagItemMap: any = {
    'og-type': {
      property: "og:type",
      content: "article",
    },
    'og-title': {
      property: "og:title",
      content: pageTitle,
    },
    'og-description': {
      property: 'og:description',
      content: pageDescription,
    },
  }
  return {
    props: {
      pageTitle,
      pageMetaTagItemMap,
    }
  }
}

export default ProductCodeCheckingPage

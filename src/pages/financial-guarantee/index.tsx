import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import FinancialGuaranteeContent from 'components/FinancialGuaranteeContent'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import { getLocaleMessages } from 'utils/locale'
import css from './FinancialGuaranteePage.module.scss'

const FinancialGuaranteePage = ({ pageTitle }: any) => {
  return (
    <div className={css.ProtectionPlusPage}>
      <AppPageHiddenH1
        title={`${pageTitle}`}
      />
      <AppLayout>
        <AppHeader />
        <div className={css.contentContainer}>
          <FinancialGuaranteeContent />
        </div>
        <DownloadSection />
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const pageTitle = `${getLocaleMessages(locale)?.[`financial_guarantee_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
  const pageDescription = getLocaleMessages(locale)?.[`financial_guarantee_page_description`]
  const pageMetaTagItemMap: any = {
    'og-type': {
      property: "og:type",
      content: "article",
    },
    'og-title': {
      property: "og:title",
      content: pageTitle,
    },
    'og-description': {
      property: 'og:description',
      content: pageDescription,
    },
  }
  return {
    props: {
      pageTitle,
      pageMetaTagItemMap,
    }
  }
}

export default FinancialGuaranteePage

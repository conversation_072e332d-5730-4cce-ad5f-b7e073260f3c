import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_APP_STORE_URL, APP_GOOGLE_PLAY_URL } from 'constants/app'
import { useIntl } from 'react-intl'
import { tagDownloadAndroidAppEvent, tagDownloadiOSAppEvent } from 'utils/gtag'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './HowItWorksPage.module.scss'
import Link from 'next/link'
import { ArrowRightOutlined } from '@ant-design/icons'
import getImageUrl from 'utils/imageUrl'

const HowItWorksPage = ({ pageTitle }: any) => {
    const intl = useIntl()

    return (
        <div className={css.HowItWorksPage}>
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.solutionSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'how_it_works_page_introduction_section_title' })}
                            subtitle={intl.formatMessage({ id: 'how_it_works_page_introduction_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'how_it_works_page_introduction_section_description' })}
                            </>}
                        />
                        <div className={css.solutionCardGrid}>
                            <div
                                className={css.solutionCard}
                            >
                                <div className={css.cover}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-howitworks-solution-1.png`, { width: 1000 })})`,
                                    }}
                                />
                                <div className={css.content}>
                                    <div className={css.textPart}>
                                        <h2 className={css.title}>
                                            {intl.formatMessage({ id: 'how_it_works_page_introduction_item_1_title' })}
                                        </h2>
                                        <div className={css.subtitle}>
                                            {intl.formatMessage({ id: 'how_it_works_page_introduction_item_1_description' })}
                                        </div>
                                    </div>
                                    <Link
                                        href='/products/app-authentication'
                                        className={css.actionButton}
                                    >
                                        {intl.formatMessage({ id: 'how_it_works_page_learn_more' })} <ArrowRightOutlined />
                                    </Link>
                                </div>
                            </div>
                            <div
                                className={css.solutionCard}
                            >
                                <div className={css.cover}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-howitworks-solution-2.png`, { width: 1000 })})`,
                                    }}
                                />
                                <div className={css.content}>
                                    <div className={css.textPart}>
                                        <h2 className={css.title}>
                                            {intl.formatMessage({ id: 'how_it_works_page_introduction_item_2_title' })}
                                        </h2>
                                        <div className={css.subtitle}>
                                            {intl.formatMessage({ id: 'how_it_works_page_introduction_item_2_description' })}
                                        </div>
                                    </div>
                                    <Link
                                        href='/products/api-authentication'
                                        className={css.actionButton}
                                    >
                                        {intl.formatMessage({ id: 'how_it_works_page_learn_more' })} <ArrowRightOutlined />
                                    </Link>
                                </div>
                            </div>
                            <div
                                className={css.solutionCard}
                            >
                                <div className={css.cover}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(getImageUrl('https://authclass-static.oss-cn-hongkong.aliyuncs.com/website/home-download-section-background-character.png'), { width: 1000 })})`
                                    }}
                                />
                                <div className={css.content}>
                                    <div className={css.textPart}>
                                        <h2 className={css.title}>
                                            {intl.formatMessage({ id: 'how_it_works_page_introduction_item_3_title' })}
                                        </h2>
                                        <div className={css.subtitle}>
                                            {intl.formatMessage({ id: 'how_it_works_page_introduction_item_3_description' })}
                                        </div>
                                    </div>
                                    <Link
                                        href='/products/authclass-courses'
                                        className={css.actionButton}
                                    >
                                        {intl.formatMessage({ id: 'how_it_works_page_learn_more' })} <ArrowRightOutlined />
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </AppContainer>
                </div>
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`how_it_works_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}

export default HowItWorksPage

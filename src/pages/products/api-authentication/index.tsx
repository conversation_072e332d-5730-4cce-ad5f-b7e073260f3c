import { ArrowRightOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_OG_IMAGE_URL, APP_URL } from 'constants/app'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import Link from 'next/link'
import { useRouter } from 'next/router'
import Script from 'next/script'
import { useIntl } from 'react-intl'
import { BreadcrumbList, Product, WithContext } from 'schema-dts'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './HowItWorksPage.module.scss'
import AppBreadcrumb from 'components/AppBreadcrumb'

const HowItWorksPage = ({ pageTitle }: any) => {
    const intl = useIntl()
    const router = useRouter()
    const { locale, defaultLocale } = router
    const canonicalUrl = useCanonicalUrl()

    const pageSchema: WithContext<Product> = {
        "@context": "https://schema.org",
        "@type": "Product",
        url: canonicalUrl,
        name: `${pageTitle}`,
        description: intl.formatMessage({ id: 'how_it_works_page_offline_section_description' }),
        image: APP_OG_IMAGE_URL,
        potentialAction: SCHEMA_POTENTIAL_ACTION,
        // review: [
        //     ...SCHEMA_PRODUCT_REVIEWS,
        // ],
    };

    const breadCrumbSchema: WithContext<BreadcrumbList> = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                "position": 1,
                "name": intl.formatMessage({ id: 'home_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                ].filter(path => !!path)
                    .join('/')
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": intl.formatMessage({ id: 'how_it_works_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'products',
                ].filter(path => !!path)
                    .join('/'),
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": pageTitle,
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'products',
                    'api-authentication',
                ].filter(path => !!path)
                    .join('/'),
            },
        ],
    };

    return (
        <div className={css.HowItWorksPage}>
            <Script
                id='product-page-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(pageSchema),
                }}
            />
            <Script
                id='breadcrumb-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(breadCrumbSchema),
                }}
            />
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.offlineSection}>
                    <AppContainer className={css.pageBreadcrumbContainer}>
                        <AppBreadcrumb
                            items={[
                                {
                                    title: <Link href='/products'>{intl.formatMessage({ id: 'how_it_works_page_title' })}</Link>,
                                },
                                {
                                    title: intl.formatMessage({ id: 'how_it_works_page_offline_section_title' }),
                                }
                            ]}
                            className={css.pageBreadcrumb}
                        />
                    </AppContainer>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'how_it_works_page_offline_section_title' })}
                            subtitle={intl.formatMessage({ id: 'how_it_works_page_offline_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'how_it_works_page_offline_section_description' })}
                            </>}
                        />
                        <Link
                            href={'/contact'}
                            className={css.sectionActionButton}
                        >
                            {intl.formatMessage({ id: `pricing_page_business_section_action_button_title` })}<ArrowRightOutlined />
                        </Link>
                        <div className={css.tutorialCardGrid}>
                            {/* <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-offline-1.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 1
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_item_1_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_item_1_description' })}
                                    </div>
                                </div>
                            </div> */}
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-offline-2.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 1
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_item_2_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_item_2_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-offline-4.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 2
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_item_3_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_item_3_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-offline-3.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 3
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_item_4_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_offline_item_4_description' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </AppContainer>
                </div >
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`how_it_works_page_introduction_item_2_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageDescription = `${getLocaleMessages(locale)?.[`how_it_works_page_offline_section_description`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
        'og-desciption': {
            property: 'og:description',
            content: pageDescription,
        }
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}

export default HowItWorksPage

import { ArrowRightOutlined } from '@ant-design/icons'
import Vimeo from '@u-wave/react-vimeo'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import CourseListCard from 'components/CourseListCard'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_OG_IMAGE_URL, APP_URL } from 'constants/app'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import { useRouter } from 'next/router'
import Script from 'next/script'
import { useIntl } from 'react-intl'
import { BreadcrumbList, Product, WithContext } from 'schema-dts'
import apiAuthClass from 'utils/apiAuthClass'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './AuthClassPage.module.scss'
import AppBreadcrumb from 'components/AppBreadcrumb'
import Link from 'next/link'

const AuthClassPage = ({ pageTitle, item }: any) => {
    const intl = useIntl()
    const router = useRouter()
    const { locale, defaultLocale } = router
    const canonicalUrl = useCanonicalUrl()
    const requestResult = item
    const {
        total,
        data,
    } = requestResult || {}


    const getVimeoPlayer = () => {
        try {
            return (
                <Vimeo
                    video={`1046636641`}
                    className={css.videoPlayer}
                    loop={false}
                    showPortrait={false}
                    showTitle={false}
                    showByline={false}
                />
            )
        } catch {
            // Empty
        }
        return null
    }

    const pageSchema: WithContext<Product> = {
        "@context": "https://schema.org",
        "@type": "Product",
        url: canonicalUrl,
        name: `${pageTitle}`,
        description: intl.formatMessage({ id: 'authclass_page_description' }),
        image: APP_OG_IMAGE_URL,
        // aggregateRating: {
        //     "@type": "AggregateRating",
        //     "ratingValue": "4.9",
        //     "reviewCount": "20000"
        // },
        potentialAction: SCHEMA_POTENTIAL_ACTION,
        // review: [
        //     ...SCHEMA_PRODUCT_REVIEWS,
        // ],
    };

    const breadCrumbSchema: WithContext<BreadcrumbList> = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                "position": 1,
                "name": intl.formatMessage({ id: 'home_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                ].filter(path => !!path)
                    .join('/')
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": intl.formatMessage({ id: 'how_it_works_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'products',
                ].filter(path => !!path)
                    .join('/'),
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": pageTitle,
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'products',
                    'authclass-courses',
                ].filter(path => !!path)
                    .join('/'),
            },
        ],
    };

    return (
        <div className={css.HowItWorksPage}>
            <Script
                id='product-page-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(pageSchema),
                }}
            />
            <Script
                id='breadcrumb-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(breadCrumbSchema),
                }}
            />
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.introductionSection}>
                    <AppContainer className={css.pageBreadcrumbContainer}>
                        <AppBreadcrumb
                            items={[
                                {
                                    title: <Link href='/products'>{intl.formatMessage({ id: 'how_it_works_page_title' })}</Link>,
                                },
                                {
                                    title: intl.formatMessage({ id: 'authclass_page_title' }),
                                }
                            ]}
                            className={css.pageBreadcrumb}
                        />
                    </AppContainer>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'authclass_page_title' })}
                            subtitle={intl.formatMessage({ id: 'authclass_page_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'authclass_page_description' })}
                            </>}
                        />
                        <div className={css.videoPlayerContainer}>
                            <div className={css.videoPlayerPart}>
                                <div className={css.playerWrapper}>
                                    {getVimeoPlayer()}
                                </div>
                            </div>
                        </div>
                        <a className={css.sectionActionButton} href={`https://authclass.com`} target='_blank' rel='noopener noreferrer'>
                            {intl.formatMessage({ id: `how_it_works_page_learn_more` })} AuthClass.com<ArrowRightOutlined />
                        </a>
                    </AppContainer>
                </div >
                <div className={css.howItWorksSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'authclass_page_how_it_works_section_title' })}
                            subtitle={intl.formatMessage({ id: 'authclass_page_how_it_works_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'authclass_page_how_it_works_section_description' })}
                            </>}
                        />
                        <div className={css.downloadButtonContainer}>
                            <div className={css.downloadButtonGrid}>
                            </div>
                        </div>
                        <div className={css.tutorialCardGrid}>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(getImageUrl('https://authclass-static.oss-cn-hongkong.aliyuncs.com/website/illustration-home-page-introduction-item-1.png'), { width: 400 })})`
                                        }}
                                    />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'authclass_page_how_it_works_section_item_1_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'authclass_page_how_it_works_section_item_1_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(getImageUrl('https://authclass-static.oss-cn-hongkong.aliyuncs.com/website/illustration-home-page-introduction-item-2.png'), { width: 400 })})`
                                        }}
                                    />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'authclass_page_how_it_works_section_item_2_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'authclass_page_how_it_works_section_item_2_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(getImageUrl('https://authclass-static.oss-cn-hongkong.aliyuncs.com/website/illustration-home-page-introduction-item-3.png'), { width: 400 })})`
                                        }}
                                    />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'authclass_page_how_it_works_section_item_3_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'authclass_page_how_it_works_section_item_3_description' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </AppContainer>
                </div >
                <div className={css.courseListSection}>
                    <AppContainer>
                        <HomePageSectionHeader
                            className={css.sectionHeader}
                            title={intl.formatMessage({ id: 'authclass_page_course_list_section_title' })}
                            subtitle={intl.formatMessage({ id: 'authclass_page_course_list_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'authclass_page_course_list_section_description' })}
                            </>}
                        />
                        <div className={css.courseListCardGrid}>
                            {
                                data && (
                                    data
                                        // .filter((courseItem: any) => courseItem.status === 'live')
                                        .map((courseItem: any) => (
                                            <CourseListCard item={courseItem} key={`latest-course-${courseItem.id}-col`} />
                                        ))
                                )
                            }
                        </div>
                    </AppContainer>
                </div>
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`authclass_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageDescription = `${getLocaleMessages(locale)?.[`authclass_page_description`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
        'og-desciption': {
            property: 'og:description',
            content: pageDescription,
        }
    }
    try {
        const query = {
            $offset: 0,
            $limit: 100,
            $sort: { published_at: -1 },
            public: 1,
            enabled: 1,
        }
        const item = await apiAuthClass.get(null, `v1/course`, query)
        // console.log('item', item)
        if (item) {
            return {
                props: {
                    pageTitle,
                    pageMetaTagItemMap,
                    item,
                },
            };
        }
    } catch (error) {
        console.log('error', error)
        // console.log(`error for v1/product_brand/slug/${itemId}`, error);
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}
export default AuthClassPage

.HowItWorksPage {

    .pageBreadcrumbContainer {
        padding: 24px;
    }
    
    .courseListSection {
        // padding: 48px 0;
        margin-bottom: 48px;

        @include responsive('md') {
            // padding: 96px 0;
            margin-bottom: 96px;
        }

        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;
        
            @include responsive('md') {
              padding: 96px 0 60px 0;
            }
          }

        .courseListCardGrid {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            margin-bottom: 24px;
            align-items: stretch;
            justify-content: stretch;

            @include responsive('sm') {
                grid-template-columns: repeat(2, 1fr);
            }

            @include responsive('md') {
                grid-template-columns: repeat(4, 1fr);
                row-gap: 24px;
            }
        }

    }

    .introductionSection {
        // padding: 48px 0;
        margin-bottom: 48px;

        @include responsive('md') {
            // padding: 96px 0;
            margin-bottom: 96px;
        }

        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .sectionHeader {
            padding: 48px 0 24px 0;

            @include responsive('md') {
                padding: 96px 0 24px 0;
            }
        }

        .videoPlayerContainer {
            margin: 24px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .videoPlayerPart {
            background-color: #000;
            width: 100%;
            max-width: 800px;
            border: 1px solid $color-separator-white-1;
            border-radius: 20px;
            -webkit-backface-visibility: hidden;
            -moz-backface-visibility: hidden;
            -webkit-transform: translate3d(0, 0, 0);
            -moz-transform: translate3d(0, 0, 0);
            border-radius: $border-radius-theme-1;
            overflow: hidden;
            margin: 24px;
            // box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.3);
            box-shadow: 0 4px 30px rgba(0, 0, 0, 1);
            // margin-bottom: 48px;

            @include responsive('md') {
                // padding: unset;
                // min-width: 600px;
                // width: 100%;
                // max-width: 1000px;
                // margin-bottom: 48px;
            }


            .playerWrapper {
                width: 100%;
                // height: 200px;
                padding-top: 56.25%;
                position: relative;
                // align-items: center;
                // justify-content: center;
                // display: none;

                @include responsive('md') {
                    // display: flex;
                    // width: 100%;
                }

                .videoPlayer {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;

                    >iframe {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }

        .sectionActionButton {
            cursor: pointer;
            padding: 10px 12px;
            background-color: $color-app-white;
            border-radius: 4px;
            color: #000;
            font-weight: bold;
            text-align: center;
            // max-width: 300px;
            display: flex;
            width: fit-content;
            column-gap: 6px;
        }
    }

    .howItWorksSection {
        // padding: 48px 0;
        margin-bottom: 48px;

        @include responsive('md') {
            // padding: 96px 0;
            margin-bottom: 96px;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .tutorialCardGrid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            margin-bottom: 48px;

            @include responsive('md') {
                grid-template-columns: repeat(3, 1fr);
            }

            .tutorialCard {
                // min-height: 400px;
                color: $color-app-white;
                row-gap: 6px;
                border: 1px solid $color-separator-white-2;
                color: $color-app-white;
                border-radius: $border-radius-theme-1;
                overflow: hidden;
                position: relative;

                @include responsive('md') {
                    min-height: 500px;
                }

                .cardBackground {
                    // position: absolute;
                    width: 100%;
                    min-height: 300px;
                    // background-color: red;

                    .coverImage {
                        min-height: 300px;
                        width: 100%;
                        height: 100%;
                        background-size: cover;
                        background-repeat: no-repeat;
                        background-position: center;
                        // background-color: blue;
                    }
                }

                .cardContent {
                    padding: 24px;
                    position: relative;
                    display: flex;
                    flex-direction: column;

                    @include responsive('md') {
                        padding: 40px;
                    }

                    .step {
                        width: 100%;
                        opacity: 0.7;
                        font-weight: bold;
                        margin-bottom: 3px;
                        font-size: 14px;
                        line-height: 26px;

                        @include responsive('md') {
                            font-size: 16px;
                            line-height: 26px;
                        }
                    }

                    .title {
                        font-weight: bold;
                        margin-bottom: 6px;
                        font-size: 16px;
                        line-height: 26px;

                        @include responsive('md') {
                            font-size: 20px;
                            line-height: 30px;
                        }
                    }

                    .instruction {
                        opacity: 0.6;
                        font-size: 14px;
                        line-height: 24px;

                        @include responsive('md') {
                            font-size: 16px;
                            line-height: 26px;
                        }
                    }
                }
            }
        }
    }
}
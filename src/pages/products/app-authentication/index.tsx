import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_APP_STORE_URL, APP_GOOGLE_PLAY_URL, APP_OG_IMAGE_URL, APP_URL } from 'constants/app'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import { useRouter } from 'next/router'
import Script from 'next/script'
import { useIntl } from 'react-intl'
import { BreadcrumbList, Product, WithContext } from 'schema-dts'
import { tagDownloadAndroidAppEvent, tagDownloadiOSAppEvent } from 'utils/gtag'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './HowItWorksPage.module.scss'
import Link from 'next/link'
import AppBreadcrumb from 'components/AppBreadcrumb'

const HowItWorksPage = ({ pageTitle }: any) => {
    const intl = useIntl()
    const router = useRouter()
    const { locale, defaultLocale } = router
    const canonicalUrl = useCanonicalUrl()

    const pageSchema: WithContext<Product> = {
        "@context": "https://schema.org",
        "@type": "Product",
        url: canonicalUrl,
        name: `${pageTitle}`,
        description: intl.formatMessage({ id: 'how_it_works_page_online_section_description' }),
        image: APP_OG_IMAGE_URL,
        // aggregateRating: {
        //     "@type": "AggregateRating",
        //     "ratingValue": "4.9",
        //     "reviewCount": "20000"
        // },
        potentialAction: SCHEMA_POTENTIAL_ACTION,
        // review: [
        //     ...SCHEMA_PRODUCT_REVIEWS,
        // ],
    };

    const breadCrumbSchema: WithContext<BreadcrumbList> = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                "position": 1,
                "name": intl.formatMessage({ id: 'home_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                ].filter(path => !!path)
                    .join('/')
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": intl.formatMessage({ id: 'how_it_works_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'products',
                ].filter(path => !!path)
                    .join('/'),
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": pageTitle,
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'products',
                    'app-authentication',
                ].filter(path => !!path)
                    .join('/'),
            },
        ],
    };

    return (
        <div className={css.HowItWorksPage}>
            <Script
                id='product-page-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(pageSchema),
                }}
            />
            <Script
                id='breadcrumb-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(breadCrumbSchema),
                }}
            />
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.onlineSection}>
                    <AppContainer className={css.pageBreadcrumbContainer}>
                        <AppBreadcrumb
                            items={[
                                {
                                    title: <Link href='/products'>{intl.formatMessage({ id: 'how_it_works_page_title' })}</Link>,
                                },
                                {
                                    title: intl.formatMessage({ id: 'how_it_works_page_online_section_title' }),
                                }
                            ]}
                            className={css.pageBreadcrumb}
                        />
                    </AppContainer>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'how_it_works_page_online_section_title' })}
                            subtitle={intl.formatMessage({ id: 'how_it_works_page_online_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'how_it_works_page_online_section_description' })}
                            </>}
                        />
                        <div className={css.downloadButtonContainer}>
                            <div className={css.downloadButtonGrid}>
                                <a onClick={tagDownloadiOSAppEvent} href={APP_APP_STORE_URL} target='_blank' rel='noopener noreferrer'>
                                    <img className={css.downloadButton} src='https://legitapp-static.oss-accelerate.aliyuncs.com/badge-app-store-blue.svg' alt='App Store' />
                                </a>
                                <a onClick={tagDownloadAndroidAppEvent} href={APP_GOOGLE_PLAY_URL} target='_blank' rel='noopener noreferrer'>
                                    <img className={css.downloadButton} src='https://legitapp-static.oss-accelerate.aliyuncs.com/badge-google-play-blue.svg' alt='Google Play' />
                                </a>
                            </div>
                        </div>
                        <div className={css.tutorialCardGrid}>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-online-1.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 1
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_1_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_1_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-online-2.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 2
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_2_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_2_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-online-3.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 3
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_3_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_3_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-online-4.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 4
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_4_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_4_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-online-5.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 5
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_5_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_5_description' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.tutorialCard}>
                                <div className={css.cardBackground}>
                                    <div
                                        className={css.coverImage}
                                        style={{
                                            backgroundImage: `url(${resizeImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-how-it-works-online-6.jpg`, { width: 1000 })})`,
                                        }}
                                    />
                                </div>
                                <div className={css.cardBackground}>
                                    <div className={css.overlayGradient} />
                                </div>
                                <div className={css.cardContent}>
                                    <div className={css.step}>
                                        {intl.formatMessage({ id: 'how_it_works_page_step' })} 6
                                    </div>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_6_title' })}
                                    </div>
                                    <div className={css.instruction}>
                                        {intl.formatMessage({ id: 'how_it_works_page_online_item_6_description' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </AppContainer>
                </div >
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`how_it_works_page_online_section_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageDescription = `${getLocaleMessages(locale)?.[`how_it_works_page_online_section_description`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
        'og-desciption': {
            property: 'og:description',
            content: pageDescription,
        }
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}

export default HowItWorksPage

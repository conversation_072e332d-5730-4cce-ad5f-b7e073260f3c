.HowItWorksPage {

    .solutionSection {

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .solutionCardGrid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            margin-bottom: 48px;
            width: 100%;

            @include responsive('md') {
                grid-template-columns: repeat(1, 1fr);
                margin-bottom: 90px;
            }

            .solutionCard {
                min-height: 500px;
                // padding: 24px;
                color: $color-app-white;
                row-gap: 6px;
                border: 1px solid $color-separator-white-1;
                color: $color-app-white;
                border-radius: $border-radius-theme-2;
                overflow: hidden;
                // display: flex;
                // flex-direction: column;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                background-repeat: no-repeat;
                background-size: cover;
                // background-position: 0 80px;
                position: relative;
                display: grid;

                @include responsive('md') {
                    align-items: center;
                    min-height: 600px;
                    grid-template-columns: repeat(2, 1fr);
                }

                .cover {
                    position: absolute;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                    background-repeat: no-repeat;
                    background-size: cover;
                    opacity: 0.6;
                    // background-position: 0 80px;

                    @include responsive('md') {
                        position: relative;
                        opacity: unset;
                    }
                }

                .content {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    row-gap: 48px;
                    padding: 24px;
                    justify-content: space-between;

                    @include responsive('md') {
                        justify-content: unset;
                        padding: 40px;
                        row-gap: 24px;
                    }

                    .title {
                        font-weight: bold;
                        margin-bottom: 6px;
                        font-size: 16px;
                        line-height: 26px;

                        @include responsive('md') {
                            font-size: 20px;
                            line-height: 30px;
                        }
                    }

                    .subtitle {
                        opacity: 0.6;
                        width: 100%;
                        font-size: 14px;
                        line-height: 24px;

                        @include responsive('md') {
                            font-size: 16px;
                            line-height: 26px;
                        }
                    }

                    .actionButton {
                        cursor: pointer;
                        // margin: 0 0 48px 0;
                        padding: 10px 12px;
                        background-color: $color-app-white;
                        border-radius: 4px;
                        color: #000;
                        font-weight: bold;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        // justify-content: space-between;
                        column-gap: 12px;
                        width: 100%;
                        font-size: 14px;

                        @include responsive('md') {
                            width: fit-content;
                            font-size: 16px;
                        }
                    }
                }
            }
        }
    }
}
import { CaretRightFilled } from '@ant-design/icons'
import clsx from 'clsx'
import AppBreadcrumb from 'components/AppBreadcrumb'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_LOCALES, APP_NAME, APP_OG_IMAGE_URL, APP_URL, PRODUCT_CATEGORY_OFFER_PRICE_STARTING_FROM } from 'constants/app'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'
import item from 'data/productAll.json'
import PRODUCT_CATEGORY from 'data/productCategory'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import Script from 'next/script'
import { useIntl } from 'react-intl'
import { BreadcrumbList, Product, WithContext } from 'schema-dts'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages, getLocalisedField } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './WeAuthenticateCategoryPage.module.scss'

const WhatWeAuthenticatePage = ({ categoryItem, pageTitle }: any) => {
    const intl = useIntl()
    const router = useRouter()
    const { locale, defaultLocale } = router
    const {
        product_brand: productBrandList,
        product_category_brand_map: productCategoryBrandMapList,
    } = item
    const productBrandMap = productBrandList.reduce((prev: any, current: any) => ({ [current.id]: current, ...prev }), {})
    const categoryBrandListMap = PRODUCT_CATEGORY
        .reduce((prev: any, current: any) => {
            const categoryId = current.id
            const categoryBrandList = productCategoryBrandMapList
                .filter((productCategoryBrandMap: any) => productCategoryBrandMap.category_id === categoryId)
                .map((productCategoryBrandMap: any) => productBrandMap[productCategoryBrandMap.brand_id])
                .filter((item: any) => !!item)
                .sort((a: any, b: any) => a.title.localeCompare(b.title))
            return {
                [current.id]: categoryBrandList,
                ...prev,
            }
        }, {})

    const offerPriceStartingFrom = PRODUCT_CATEGORY_OFFER_PRICE_STARTING_FROM[categoryItem.id]

    const getCategorySection = () => {
        return (
            <div className={css.categorySection}>
                <AppContainer>
                    <div className={css.topPart}>
                        <AppBreadcrumb
                            items={[
                                {
                                    title: <Link href='/what-we-authenticate'>{intl.formatMessage({ id: 'what_we_authenticate_page_title' })}</Link>,
                                },
                                {
                                    title: `${intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} ${getLocalisedField(categoryItem, 'title', locale)}`,
                                }
                            ]}
                            className={css.pageBreadcrumb}
                        />
                    </div>
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        title={`${intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} ${getLocalisedField(categoryItem, 'title', locale)}`}
                        subtitle={intl.formatMessage({ id: 'app_title' })}
                        description={<>
                            {intl.formatMessage({ id: 'what_we_authenticate_page_category_section_description' })}
                        </>}
                    />
                </AppContainer>
                <div className={css.productCategoryCardGrid}>
                    {
                        [categoryItem].map((categoryItem: any) => (
                            <div
                                className={clsx(css.categoryItemCard)}
                                key={`what-we-authenticate-product-category-card-${categoryItem.id}`}
                                style={{
                                    backgroundImage: `url(${resizeImageUrl(getImageUrl(categoryItem.cover_image_url), { width: 1000 })})`,
                                }}
                            >
                                <div className={css.categoryItemTitle}>
                                    {categoryItem.title.replace('Luxury', '').replace(/\s/g, '')}
                                </div>
                            </div>
                        ))
                    }
                </div>
            </div>
        )
    }

    const getBrandSection = () => {
        const {
            product_brand: productBrandList,
            // product_category: productCategoryList,
            product_category_brand_map: productCategoryBrandMapList,
        } = item
        if (!productBrandList || !productCategoryBrandMapList) {
            return null
        }

        return (
            <div className={css.brandSection}>
                {/* <AppContainer>
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        title={intl.formatMessage({ id: 'what_we_authenticate_page_brand_section_subtitle' })}
                        subtitle={intl.formatMessage({ id: 'what_we_authenticate_page_brand_section_title' })}
                        description={<>
                            {intl.formatMessage({ id: 'what_we_authenticate_page_brand_section_description' })}
                        </>}
                    />
                </AppContainer> */}
                {/* <div className={css.categoryTabBar}>
                    <AppContainer className={css.productCategoryButtonContainer}>
                        {
                            PRODUCT_CATEGORY.map((productCategory: any) => (
                                <div
                                    className={clsx(css.categoryTabBarButton, selectedCategoryId === productCategory.id ? css.selected : '')}
                                    key={`what-we-authenticate-product-category-button-${productCategory.id}`}
                                    onClick={() => categoryTabBarButtonOnClick(productCategory)}
                                >
                                    {productCategory.title.replace('Luxury', '').replace(/\s/g, '')}
                                </div>
                            ))
                        }
                    </AppContainer>
                </div> */}
                {
                    [categoryItem].map((productCategory: any) => (
                        <div
                            className={css.categoryBrandListSection}
                            key={`what-we-authenticate-product-category-section-${productCategory.id}`}
                            id={`category-section-${productCategory.id}`}
                        >
                            <div className={css.categoryBrandListSectionHeader}>
                                <AppContainer>
                                    <h3 className={css.headerTitle}>
                                        {intl.formatMessage({ id: 'what_we_authenticate_page_brand_section_subtitle' })}
                                    </h3>
                                </AppContainer>
                            </div>
                            <AppContainer>
                                <div className={css.brandItemGrid}>
                                    {
                                        categoryBrandListMap[productCategory.id]
                                            .map((brandItem: any) => (
                                                <Link
                                                    href={`/what-we-authenticate/${brandItem.slug}`}
                                                    key={`what-we-authenticate-product-category-section-${productCategory.id}-brand-${brandItem.id}`}
                                                >
                                                    <div className={css.brandItem}>
                                                        <div
                                                            className={css.brandImage}
                                                            style={{
                                                                backgroundImage: `url(${resizeImageUrl(getImageUrl(brandItem?.icon_image_url), { width: 100 })})`,
                                                            }}
                                                        />
                                                        <h3 className={css.brandTitle}>
                                                            <span style={{ display: 'none' }}>{intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} </span>{brandItem?.title}<span style={{ display: 'none' }}> {getLocalisedField(categoryItem, 'title', locale)}</span>
                                                        </h3>
                                                        <div className={css.link}>
                                                            <CaretRightFilled className={css.arrowIcon} />
                                                        </div>
                                                    </div>
                                                </Link>
                                            ))
                                    }
                                </div>
                            </AppContainer>
                        </div>
                    ))
                }
            </div>
        )
    }

    const getPageContent = () => {
        if (!item) {
            return null
        }
        return (
            <>
                {getCategorySection()}
                {getBrandSection()}
            </>
        )
    }

    const canonicalUrl = useCanonicalUrl()

    const pageSchema: WithContext<Product> = {
        "@context": "https://schema.org",
        "@type": "Product",
        url: canonicalUrl,
        name: `${getLocaleMessages(locale)?.[`what_we_authenticate_page_authenticate`]} ${getLocalisedField(categoryItem, 'title', locale)}`,
        description: intl.formatMessage({ id: 'app_og_description' }),
        image: categoryItem?.cover_image_url || APP_OG_IMAGE_URL,
        offers: {
            "@type": "Offer",
            "url": canonicalUrl,
            "priceCurrency": "USD",
            "price": `${offerPriceStartingFrom.toFixed(0)}`,
            // "priceValidUntil": "2023-12-31",
            // "itemCondition": "https://schema.org/NewCondition",
            "availability": "https://schema.org/InStock",
            "seller": {
                "@type": "Organization",
                name: APP_NAME,
                url: APP_URL,
            }
        },
        potentialAction: SCHEMA_POTENTIAL_ACTION,
        // review: [
        //     ...SCHEMA_PRODUCT_REVIEWS,
        // ],
    };

    const breadCrumbSchema: WithContext<BreadcrumbList> = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                "position": 1,
                "name": intl.formatMessage({ id: 'home_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                ].filter(path => !!path)
                    .join('/')
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": intl.formatMessage({ id: 'what_we_authenticate_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'what-we-authenticate',
                ].filter(path => !!path)
                    .join('/'),
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": pageTitle,
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'what-we-authenticate',
                    categoryItem.slug,
                ].filter(path => !!path)
                    .join('/'),
            },
        ],
    };

    // console.log('item', item)
    // console.log('breadCrumbSchema', breadCrumbSchema)

    return (
        <div className={css.WhatWeAuthenticatePage}>
            <Script
                id='product-page-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(pageSchema),
                }}
            />
            <Script
                id='breadcrumb-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(breadCrumbSchema),
                }}
            />
            <AppPageHiddenH1
                title={pageTitle}
            />
            <Head>
                <title>
                    {pageTitle}
                </title>
            </Head>
            <AppLayout>
                <AppHeader />
                {getPageContent()}
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const { categorySlug: itemId } = params
    if (!!itemId && itemId !== "undefined") {
        try {
            const item = PRODUCT_CATEGORY.find((category: any) => category.slug === itemId)
            // console.log('itemId', itemId)
            if (item) {
                const pageTitle = `${getLocaleMessages(locale)?.[`what_we_authenticate_page_authenticate`]} ${getLocalisedField(item, 'title', locale)} | ${getLocaleMessages(locale)?.[`app_title`]}`
                const pageMetaTagItemMap: any = {
                    'og-type': {
                        property: "og:type",
                        content: "article",
                    },
                    'og-title': {
                        property: "og:title",
                        content: pageTitle,
                    },
                }
                // if (productBrand?.description) {
                //     pageMetaTagItemMap['og-description'] = {
                //         property: 'og:description',
                //         content: productBrand?.description,
                //     }
                // }
                if (item?.cover_image_url) {
                    pageMetaTagItemMap['og-image'] = {
                        property: 'og:image',
                        content: resizeImageUrl(getImageUrl(item?.cover_image_url), { width: 600 }),
                    }
                }
                return {
                    props: {
                        pageTitle,
                        pageMetaTagItemMap,
                        categoryItem: item,
                    },
                };
            }
        } catch (error) {
            console.log(`error for v1/product_brand/slug/${itemId}`, error);
        }
    }
    return {
        notFound: true,
    }
}

export async function getStaticPaths() {

    const pageParams = PRODUCT_CATEGORY.map((categoryItem: any) => ({
        params: { categorySlug: categoryItem.slug },
    }))

    const paths: any = []

    APP_LOCALES.forEach((locale: any) => {
        if (locale === 'en') {
            pageParams.forEach((params: any) => {
                paths.push({ ...params })
            })
        } else {
            pageParams.forEach((params: any) => {
                paths.push({ ...params, locale })
            })
        }
    })

    return {
        paths,
        fallback: false,
    };
}


export default WhatWeAuthenticatePage

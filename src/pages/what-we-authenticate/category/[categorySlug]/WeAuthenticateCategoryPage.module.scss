.WhatWeAuthenticatePage {


    .categorySection {

        .topPart {
            padding: 24px 0;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .productCategoryItemGrid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            row-gap: 24px;
            column-gap: 24px;
            margin-bottom: 96px;

            @include responsive('md') {
                row-gap: 48px;
                column-gap: 48px;
                grid-template-columns: repeat(5, 1fr);
            }

            .productCategoryItemContainer {
                display: flex;
                flex-direction: column;
                align-items: center;
                row-gap: 12px;

                .productCategoryItemIcon {
                    img {
                        height: 50px;
                    }
                }

                .productCategoryItemTitle {
                    width: 100%;
                    color: #fff;
                    font-size: 10px;
                    text-align: center;
                    font-weight: bold;
                    text-transform: uppercase;

                    @include responsive('md') {
                        font-size: 14px;
                    }
                }
            }
        }

        .productCategoryCardGrid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 2px;
            row-gap: 2px;
            // border: px solid $color-separator-white-1;
            // background-color: $color-separator-white-1;

            // @include responsive('md') {
            //     grid-template-columns: repeat(5, 1fr);
            // }

            .categoryItemCard {
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 24px;
                min-height: 200px;

                @include responsive('md') {
                    min-height: 300px;
                }

                .categoryItemTitle {
                    display: none;
                    color: #fff;
                    font-size: 20px;
                    font-weight: bold;
                    text-transform: uppercase;
                }
            }
        }
    }

    .brandSection {
        color: $color-app-white;
        padding: 48px 0;
        margin-bottom: 48px;

        @include responsive('md') {
            padding: 96px 0;
            margin-bottom: 96px;
        }

        .sectionHeader {
            padding: 48px 0 24px 0;

            @include responsive('md') {
                padding: 96px 0 24px 0;
            }
        }

        .categoryTabBar {
            border-bottom: 1px solid $color-separator-white-1;
            overflow-y: scroll;

            .productCategoryButtonContainer {
                display: flex;
            }

            .categoryTabBarButton {
                padding: 12px 24px;
                color: $color-app-white;
                width: fit-content;
                font-weight: bold;
                text-transform: uppercase;
                cursor: pointer;

                &.selected {
                    color: $color-app-gray-900;
                    background-color: $color-app-white;
                }
            }
        }

        .categoryBrandListSection {
            margin-bottom: 48px;

            .categoryBrandListSectionHeader {
                padding: 24px 0;

                @include responsive('md') {
                    padding: 48px 0;
                }

                .headerTitle {
                    width: 100%;
                    color: #fff;
                    font-size: 20px;
                    font-weight: bold;
                    text-transform: uppercase;

                    @include responsive('md') {
                        font-size: 30px;
                        line-height: 40px;
                    }
                }
            }
        }

        .brandItemGrid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            width: 100%;

            @include responsive('md') {
                grid-template-columns: repeat(6, 1fr);
            }

            .brandItem {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 24px;
                padding-top: 48px;
                row-gap: 24px;
                width: 100%;
                height: 100%;
                border: 0.5px solid $color-separator-white-1;
                transition: all 0.25s ease-in;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                border-radius: $border-radius-theme-2;
                position: relative;

                &:hover {
                    background-color: $color-app-header-dropdown-menu-item-hover-background;

                    .link {
                        opacity: 1;
                        right: 16px;
                    }
                }

                .brandImage {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-size: contain;
                    background-position: center;
                    height: 100px;
                    width: 100px;
                    // background-color: red;
                }

                .brandTitle {
                    width: 100%;
                    color: #fff;
                    font-size: 10px;
                    text-align: center;
                    font-weight: bold;
                    text-transform: uppercase;
            
                    @include responsive('md') {
                      font-size: 14px;
                    }
                }

                .link {
                    font-size: 12px;
                    background-color: $color-app-white;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                    position: absolute;
                    top: 16px;
                    right: 24px;
                    opacity: 0;
                    transition: all 0.25s ease-in;

                    .arrowIcon {
                        font-weight: bold;
                        color: $color-app-gray-900;
                        font-size: 12px;
                    }
                }
            }
        }
    }
}
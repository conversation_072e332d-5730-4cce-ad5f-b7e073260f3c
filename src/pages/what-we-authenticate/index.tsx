import { CaretRightFilled } from '@ant-design/icons'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_URL } from 'constants/app'
import item from 'data/productAll.json'
import PRODUCT_CATEGORY from 'data/productCategory'
import Link from 'next/link'
import { useRouter } from 'next/router'
import Script from 'next/script'
import { useIntl } from 'react-intl'
import { BreadcrumbList, WithContext } from 'schema-dts'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages, getLocalisedField } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './WhatWeAuthenticatePage.module.scss'

const WhatWeAuthenticatePage = ({ pageTitle }: any) => {
    const intl = useIntl()
    const router = useRouter()
    const { locale, defaultLocale } = router
    const {
        product_brand: productBrandList,
        product_category_brand_map: productCategoryBrandMapList,
    } = item
    const productBrandMap = productBrandList.reduce((prev: any, current: any) => ({ [current.id]: current, ...prev }), {})
    const categoryBrandListMap = PRODUCT_CATEGORY
        .reduce((prev: any, current: any) => {
            const categoryId = current.id
            const categoryBrandList = productCategoryBrandMapList
                .filter((productCategoryBrandMap: any) => productCategoryBrandMap.category_id === categoryId)
                .map((productCategoryBrandMap: any) => productBrandMap[productCategoryBrandMap.brand_id])
                .filter((item: any) => !!item)
                .sort((a: any, b: any) => a.title.localeCompare(b.title))
            return {
                [current.id]: categoryBrandList,
                ...prev,
            }
        }, {})

    const getCategorySection = () => {
        return (
            <div className={css.categorySection}>
                <AppContainer>
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        title={intl.formatMessage({ id: 'what_we_authenticate_page_category_section_subtitle' })}
                        subtitle={intl.formatMessage({ id: 'what_we_authenticate_page_category_section_title' })}
                        description={<>
                            {intl.formatMessage({ id: 'what_we_authenticate_page_category_section_description' })}
                        </>}
                    />
                </AppContainer>
                <AppContainer>
                    <div className={css.productCategoryItemGrid}>
                        {
                            PRODUCT_CATEGORY.map(item => (
                                <Link
                                    href={`/what-we-authenticate/category/${item.slug}`}
                                    className={css.productCategoryItemContainer}
                                    key={`home-page-product-category-item-${item.id}-col`}
                                >
                                    <div className={css.productCategoryItemIcon}>
                                        <img
                                            src={resizeImageUrl(getImageUrl(item.icon_image_url), { width: 100 })}
                                            alt={getLocalisedField(item, 'title', locale)}
                                        />
                                    </div>
                                    <h3 className={css.productCategoryItemTitle}>
                                        <span style={{ display: 'none' }}>{intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} </span>{getLocalisedField(item, 'title', locale)}
                                    </h3>
                                </Link>
                            ))
                        }
                    </div>
                </AppContainer>
                <div className={css.productCategoryCardGrid}>
                    {
                        PRODUCT_CATEGORY.map((categoryItem: any) => (
                            <Link
                                href={`/what-we-authenticate/category/${categoryItem.slug}`}
                                className={clsx(css.categoryItemCard)}
                                key={`what-we-authenticate-product-category-card-${categoryItem.id}`}
                                style={{
                                    backgroundImage: `url(${resizeImageUrl(getImageUrl(categoryItem.cover_image_url), { width: 300 })})`,
                                }}
                            >
                                <div className={css.categoryItemTitle}>
                                    {categoryItem.title.replace('Luxury', '').replace(/\s/g, '')}
                                </div>
                            </Link>
                        ))
                    }
                </div>
            </div>
        )
    }

    const getBrandSection = () => {
        const {
            product_brand: productBrandList,
            // product_category: productCategoryList,
            product_category_brand_map: productCategoryBrandMapList,
        } = item
        if (!productBrandList || !productCategoryBrandMapList) {
            return null
        }

        return (
            <div className={css.brandSection}>
                <AppContainer>
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        title={intl.formatMessage({ id: 'what_we_authenticate_page_brand_section_subtitle' })}
                        subtitle={intl.formatMessage({ id: 'what_we_authenticate_page_brand_section_title' })}
                        description={<>
                            {intl.formatMessage({ id: 'what_we_authenticate_page_brand_section_description' })}
                        </>}
                    />
                </AppContainer>
                {/* <div className={css.categoryTabBar}>
                    <AppContainer className={css.productCategoryButtonContainer}>
                        {
                            PRODUCT_CATEGORY.map((productCategory: any) => (
                                <div
                                    className={clsx(css.categoryTabBarButton, selectedCategoryId === productCategory.id ? css.selected : '')}
                                    key={`what-we-authenticate-product-category-button-${productCategory.id}`}
                                    onClick={() => categoryTabBarButtonOnClick(productCategory)}
                                >
                                    {productCategory.title.replace('Luxury', '').replace(/\s/g, '')}
                                </div>
                            ))
                        }
                    </AppContainer>
                </div> */}
                {
                    PRODUCT_CATEGORY.map((productCategory: any) => (
                        <div
                            className={css.categoryBrandListSection}
                            key={`what-we-authenticate-product-category-section-${productCategory.id}`}
                            id={`category-section-${productCategory.id}`}
                        >
                            <div className={css.categoryBrandListSectionHeader}>
                                <AppContainer>
                                    <h3 className={css.headerTitle}>
                                        {getLocalisedField(productCategory, 'title', locale).replace('Luxury', '').replace(/\s/g, '')}
                                    </h3>
                                </AppContainer>
                            </div>
                            <AppContainer>
                                <div className={css.brandItemGrid}>
                                    {
                                        categoryBrandListMap[productCategory.id]
                                            .map((brandItem: any) => (
                                                <Link
                                                    href={`/what-we-authenticate/${brandItem.slug}`}
                                                    key={`what-we-authenticate-product-category-section-${productCategory.id}-brand-${brandItem.id}`}
                                                >
                                                    <div className={css.brandItem}>
                                                        <div
                                                            className={css.brandImage}
                                                            style={{
                                                                backgroundImage: `url(${resizeImageUrl(getImageUrl(brandItem?.icon_image_url), { width: 100 })})`,
                                                            }}
                                                        />
                                                        <h3 className={css.brandTitle}>
                                                            <span style={{ display: 'none' }}>{intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} </span>{brandItem?.title}
                                                        </h3>
                                                        <div className={css.link}>
                                                            <CaretRightFilled className={css.arrowIcon} />
                                                        </div>
                                                    </div>
                                                </Link>
                                            ))
                                    }
                                </div>
                            </AppContainer>
                        </div>
                    ))
                }
            </div>
        )
    }

    const getPageContent = () => {
        if (!item) {
            return null
        }
        return (
            <>
                {getCategorySection()}
                {getBrandSection()}
            </>
        )
    }

    const breadCrumbSchema: WithContext<BreadcrumbList> = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                "position": 1,
                "name": intl.formatMessage({ id: 'home_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                ].filter(path => !!path)
                    .join('/')
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": pageTitle,
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'what-we-authenticate',
                ].filter(path => !!path)
                    .join('/'),
            },
        ],
    };

    return (
        <div className={css.WhatWeAuthenticatePage}>
            <Script
                id='breadcrumb-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(breadCrumbSchema),
                }}
            />
            <AppPageHiddenH1
                title={pageTitle}
            />
            <AppLayout>
                <AppHeader />
                {getPageContent()}
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`what_we_authenticate_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap: any = {
        'og-type': {
            property: "og:type",
            content: "article",
        },
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        }
    }
}

export default WhatWeAuthenticatePage

import Vimeo from '@u-wave/react-vimeo'
import clsx from 'clsx'
import AppBreadcrumb from 'components/AppBreadcrumb'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_NAME, APP_OG_IMAGE_URL, APP_URL, PRODUCT_CATEGORY_OFFER_PRICE_STARTING_FROM } from 'constants/app'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'
import HOW_TO_AUTHENTICATE_VIDEO from 'data/howToAuthenticateVideo'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import Link from 'next/link'
import { useRouter } from 'next/router'
import Script from 'next/script'
import { useIntl } from 'react-intl'
import { BreadcrumbList, Product, WithContext } from 'schema-dts'
import apiCore from 'utils/apiCore'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages, getLocalisedField } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './WeAuthenticateBrandPage.module.scss'

const WeAuthenticateBrandPage = ({ item, pageTitle }: any) => {
    const intl = useIntl()
    const router = useRouter()
    const canonicalUrl = useCanonicalUrl()
    if (!item) {
        return null
    }
    const { locale, defaultLocale } = router
    const {
        product_brand: productBrand,
        product_category: productCategoryList,
        product_model: productModelList,
    } = item || {}

    const offerPriceStartingFrom = productCategoryList.reduce((prev: any = 0, current: any) => {
        const offerPrice = PRODUCT_CATEGORY_OFFER_PRICE_STARTING_FROM[current.id]
        return offerPrice < prev ? offerPrice : prev
    }, 1000)

    // const [selectedCategoryId, setSelectedCategoryId] = useState(defaultCategoryId)
    const categoryModelListMap = productCategoryList
        .reduce((prev: any, current: any) => {
            const categoryId = current.id
            const categoryModelList = productModelList
                .filter((productModel: any) => productModel.category_id === categoryId)
            return {
                [current.id]: categoryModelList,
                ...prev,
            }
        }, {})

    const getBrandSection = () => {
        return (
            <div className={css.brandSection}>
                <div className={css.backgroundContainer}>
                    <div />
                    <div className={css.coverImageContainer}>
                        <div
                            className={css.coverImage}
                            style={{
                                backgroundImage: `url(${productBrand.cover_image_url})`,
                            }}
                        />
                    </div>
                </div>
                <AppContainer className={css.sectionContainer}>
                    <div className={css.brandInformation}>
                        <div className={css.topPart}>
                            <AppBreadcrumb
                                items={[
                                    {
                                        title: <Link href='/what-we-authenticate'>{intl.formatMessage({ id: 'what_we_authenticate_page_title' })}</Link>,
                                    },
                                    {
                                        title: productBrand.title,
                                    }
                                ]}
                                className={css.pageBreadcrumb}
                            />
                        </div>
                        <div className={css.bottomPart}>
                            <div className={css.brandDescription}>
                                {productBrand.description}
                            </div>
                            <div className={css.brandName}>
                                {productBrand.title}
                            </div>
                            <div className={css.brandLogo}>
                                <img src={productBrand.icon_image_url} />
                            </div>
                        </div>
                    </div>
                    <div className={clsx(css.coverImageContainer, css.responsive)}>
                        <div
                            className={css.coverImage}
                            style={{
                                backgroundImage: `url(${productBrand.cover_image_url})`,
                            }}
                        />
                    </div>
                </AppContainer>
            </div>
        )
    }

    const getInformationSection = () => {
        return (
            <div className={css.informationSection}>
                <AppContainer>
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        subtitle={intl.formatMessage({ id: 'authenticate_brand_page_solution_section_subtitle' })}
                        title={intl.formatMessage({ id: 'authenticate_brand_page_solution_section_title' }, { brand_title: productBrand.title }) as string}
                    />
                </AppContainer>
                <AppContainer className={css.sectionContainer}>
                    <div className={css.informationCard}>
                        <h3 className={css.title}>
                            {intl.formatMessage({ id: 'authenticate_brand_page_solution_section_item_1_title' }, { brand_title: productBrand.title })}
                        </h3>
                        <div className={css.description}>
                            {intl.formatMessage({ id: 'authenticate_brand_page_solution_section_item_1_description' }, { brand_title: productBrand.title })}
                        </div>
                    </div>
                    <div className={css.informationCard}>
                        <h3 className={css.title}>
                            {intl.formatMessage({ id: 'authenticate_brand_page_solution_section_item_2_title' }, { brand_title: productBrand.title })}
                        </h3>
                        <div className={css.description}>
                            {intl.formatMessage({ id: 'authenticate_brand_page_solution_section_item_2_description' }, { brand_title: productBrand.title })}
                        </div>
                    </div>
                </AppContainer>
            </div>
        )
    }

    const getModelSection = () => {
        return (
            <div className={css.modelSection}>
                <AppContainer>
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        subtitle={intl.formatMessage({ id: 'authenticate_brand_page_model_section_subtitle' })}
                        title={intl.formatMessage({ id: 'authenticate_brand_page_model_section_title' }, { brand_title: productBrand.title }) as string}
                        description={intl.formatMessage({ id: 'authenticate_brand_page_model_section_description' }, { brand_title: productBrand.title })}
                    />
                </AppContainer>
                {
                    productCategoryList.map((productCategory: any) => (
                        <div
                            className={clsx(css.categoryModelSection)}
                            key={`what-we-authenticate-product-category-section-${productCategory.id}`}
                        >
                            <div className={css.categoryModelSectionHeader}>
                                <AppContainer>
                                    <h3
                                        className={css.categoryTitle}
                                    >
                                        <span style={{ display: 'none' }}>{intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} {productBrand.title} </span>
                                        {getLocalisedField(productCategory, 'title', locale).replace('Luxury', '').replace(/\s/g, '')}
                                    </h3>
                                </AppContainer>
                            </div>
                            <AppContainer>
                                <div className={css.modelItemGrid}>
                                    {
                                        categoryModelListMap[productCategory.id]
                                            .map((productModel: any) => (
                                                <div
                                                    // href={`/what-we-authenticate/${productBrand.slug}/${productModel.id}`}
                                                    className={css.modelItem}
                                                    key={`what-we-authenticate-category-${productCategory.id}-model-${productModel.id}`}
                                                >
                                                    <div
                                                        className={css.modelImage}
                                                        style={{
                                                            backgroundImage: `url(${productModel.icon_image_url})`,
                                                        }}
                                                    />
                                                    <h3 className={css.modelTitle}>
                                                        <span style={{ display: 'none' }}>{intl.formatMessage({ id: 'what_we_authenticate_page_authenticate' })} {productBrand.title} </span> {productModel.title}
                                                    </h3>
                                                </div>
                                            ))
                                    }
                                </div>
                            </AppContainer>
                        </div>
                    ))
                }
            </div>
        )
    }

    const getVimeoPlayer = () => {
        // console.log('HOW_TO_AUTHENTICATE_VIDEO as any)[productBrand.id]', (HOW_TO_AUTHENTICATE_VIDEO as any)[productBrand.id])
        try {
            return (
                <Vimeo
                    video={(HOW_TO_AUTHENTICATE_VIDEO as any)[productBrand.id]}
                    className={css.videoPlayer}
                    loop={false}
                    showPortrait={false}
                    showTitle={false}
                    showByline={false}
                />
            )
        } catch {
            // Empty
        }
        return null
    }

    const getVideoSection = () => {
        if (!productBrand || !(HOW_TO_AUTHENTICATE_VIDEO as any)[productBrand.id]) {
            return null
        }
        return (
            <div className={css.videoSection}>
                <AppContainer className={css.sectionContainer}>
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        title={intl.formatMessage({ id: 'authenticate_brand_page_video_section_title' }, { brand_title: productBrand.title }) as string}
                        subtitle={intl.formatMessage({ id: 'authenticate_brand_page_video_section_subtitle' })}
                        description={intl.formatMessage({ id: 'authenticate_brand_page_video_section_description' }, { brand_title: productBrand.title })}
                    />
                    <div className={css.videoPlayerPart}>
                        <div className={css.playerWrapper}>
                            {getVimeoPlayer()}
                        </div>
                    </div>
                </AppContainer>
            </div >
        )
    }

    const getPageContent = () => {
        if (!item) {
            return null
        }
        return (
            <>
                {getBrandSection()}
                {getInformationSection()}
                {getVideoSection()}
                {getModelSection()}
            </>
        )
    }

    const pageSchema: WithContext<Product> = {
        "@context": "https://schema.org",
        "@type": "Product",
        url: canonicalUrl,
        name: `${getLocaleMessages(locale)?.[`what_we_authenticate_page_authenticate`]} ${productBrand?.title}`,
        description: intl.formatMessage({ id: 'authenticate_brand_page_solution_section_item_1_description' }, { brand_title: productBrand.title }),
        image: productBrand?.cover_image_url || APP_OG_IMAGE_URL,
        brand: {
            "@type": "Brand",
            "name": productBrand?.title,
        },
        offers: {
            "@type": "Offer",
            "url": canonicalUrl,
            "priceCurrency": "USD",
            "price": `${offerPriceStartingFrom.toFixed(0)}`,
            // "priceValidUntil": "2023-12-31",
            // "itemCondition": "https://schema.org/NewCondition",
            "availability": "https://schema.org/InStock",
            "seller": {
                "@type": "Organization",
                name: APP_NAME,
                url: APP_URL,
            }
        },
        potentialAction: SCHEMA_POTENTIAL_ACTION,
        // review: [
        //     ...SCHEMA_PRODUCT_REVIEWS,
        // ],
    };

    // console.log('pageSchema', pageSchema)

    const breadCrumbSchema: WithContext<BreadcrumbList> = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                "position": 1,
                "name": intl.formatMessage({ id: 'home_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                ].filter(path => !!path)
                    .join('/')
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": intl.formatMessage({ id: 'what_we_authenticate_page_title' }),
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'what-we-authenticate',
                ].filter(path => !!path)
                    .join('/'),
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": pageTitle,
                "item": [
                    APP_URL,
                    locale === defaultLocale ? '' : locale,
                    'what-we-authenticate',
                    item.product_brand.slug,
                ].filter(path => !!path)
                    .join('/'),
            },
        ],
    };

    // console.log('item', item)
    // console.log('breadCrumbSchema', breadCrumbSchema)

    return (
        <div className={css.WeAuthenticateBrandPage}>
            <Script
                id='product-page-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(pageSchema),
                }}
            />
            <Script
                id='breadcrumb-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(breadCrumbSchema),
                }}
            />
            <AppPageHiddenH1
                title={pageTitle}
            />
            <AppLayout>
                <AppHeader />
                {getPageContent()}
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

const getPageDescription = ({ brandName, locale }: any) => {
    if (locale === 'zh-Hant') {
        return `使用 Legit App 鑒定您的 ${brandName} 產品 – 全球領先的鑒定服務。我們的專家團隊與尖端 AI 技術，確保為 ${brandName} 產品提供快速、準確的鑒定。立即信賴 Legit App，享受可靠的 ${brandName} 鑒定！`
    } else if (locale === 'zh-Hans') {
        return `使用 Legit App 鉴定您的 ${brandName} 产品 – 全球领先的鉴定服务。我们的专家团队与尖端 AI 技术，确保为 ${brandName} 产品提供快速、准确的鉴定。立即信赖 Legit App，享受可靠的 ${brandName} 鉴定！`
    }
    return `Authenticate your ${brandName} products with Legit App – the world’s leading authentication service. Our expert team and cutting-edge AI ensure fast, accurate verification for ${brandName} products. Trust Legit App for reliable ${brandName} authentication today!`
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const { brandSlug: itemId } = params
    if (!!itemId && itemId !== "undefined") {
        try {
            const item = await apiCore.get(null, `v1/product_brand/slug/${itemId}`)
            if (item) {
                const {
                    product_brand: productBrand,
                } = item
                const pageTitle = `${getLocaleMessages(locale)?.[`what_we_authenticate_page_authenticate`]} ${productBrand?.title} | ${getLocaleMessages(locale)?.[`app_title`]}`
                const pageMetaTagItemMap: any = {
                    'og-type': {
                        property: "og:type",
                        content: "article",
                    },
                    'og-title': {
                        property: "og:title",
                        content: pageTitle,
                    },
                    'og-description': {
                        property: 'og:description',
                        // content: productBrand?.description, 
                        content: getPageDescription({
                            brandName: productBrand?.title,
                            locale,
                        })
                    }
                }
                if (productBrand?.cover_image_url) {
                    pageMetaTagItemMap['og-image'] = {
                        property: 'og:image',
                        content: resizeImageUrl(getImageUrl(productBrand?.cover_image_url), { width: 600 }),
                    }
                }
                return {
                    props: {
                        pageTitle,
                        pageMetaTagItemMap,
                        item,
                    },
                };
            }
        } catch (error) {
            console.log(`error for v1/product_brand/slug/${itemId}`, error);
        }
    }
    return {
        notFound: true,
    }
}

export async function getStaticPaths() {
    return { paths: [], fallback: "blocking" };
}

export default WeAuthenticateBrandPage

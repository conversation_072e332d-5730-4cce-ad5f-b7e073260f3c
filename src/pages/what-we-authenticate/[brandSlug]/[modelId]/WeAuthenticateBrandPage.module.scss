.WeAuthenticateBrandPage {
    .brandSection {
        min-height: 500px;
        display: flex;
        align-items: stretch;
        position: relative;

        .backgroundContainer {
            display: none;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 48px;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            @include responsive('md') {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .sectionContainer {
            position: relative;
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 48px;

            @include responsive('md') {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .coverImageContainer {
            min-height: 200px;

            &.responsive {
                @include responsive('md') {
                    display: none;
                }
            }

            @include responsive('md') {
                padding: 12px;
            }

            .coverImage {
                border-radius: $border-radius-theme-1;
                height: 100%;
                width: 100%;
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;
                background-color: $color-separator-white-1;
            }
        }

        .brandInformation {
            padding: 24px 0;
            color: $color-app-white;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            row-gap: 24px;

            @include responsive('md') {
                justify-content: space-between;
            }

            .bottomPart {
                display: flex;
                flex-direction: column-reverse;
                row-gap: 12px;
            }

            .brandLogo {
                width: fit-content;
                border: 1px solid $color-separator-white-1;
                width: 80px;
                height: 80px;
                border-radius: $border-radius-theme-1;

                img {
                    width: 100%;
                }
            }

            .brandName {
                font-weight: bold;
                text-transform: uppercase;
                font-weight: bold;
                font-size: 16px;
                line-height: 26px;

                @include responsive('md') {
                    font-size: 20px;
                    line-height: 30px;
                }
            }

            .brandDescription {
                opacity: 0.6;
                font-size: 14px;
                line-height: 24px;

                @include responsive('md') {
                    font-size: 16px;
                    line-height: 26px;
                }
            }
        }
    }

    .informationSection {
        color: $color-app-white;
        padding: 48px 0;

        @include responsive('md') {
            padding: 96px 0;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .sectionContainer {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 24px;
            row-gap: 24px;

            @include responsive('md') {
                column-gap: 48px;
                row-gap: 48px;
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .informationCard {
            display: flex;
            flex-direction: column;
            row-gap: 12px;
            border: 1px solid $color-separator-white-1;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            overflow: hidden;
            padding: 24px;
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

            @include responsive('md') {
                padding: 40px;
                row-gap: 24px;
            }

            .title {
                font-weight: bold;
                font-size: 16px;
                line-height: 26px;

                @include responsive('md') {
                    font-size: 20px;
                    line-height: 30px;
                }
            }

            .description {
                opacity: 0.6;
                font-size: 14px;
                line-height: 24px;

                @include responsive('md') {
                    font-size: 16px;
                    line-height: 26px;
                }
            }
        }
    }

    .videoSection {
        min-height: 500px;
        background-image: radial-gradient(circle, $color-app-gray-600 0%, $color-app-gray-900 40%, #000 100%);
        padding: 48px 0;

        @include responsive('md') {
            padding: 96px 0;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .videoPlayerPart {
            background-color: #000;
            width: 100%;
            max-width: 800px;
            border: 1px solid $color-separator-white-1;
            border-radius: 20px;
            -webkit-backface-visibility: hidden;
            -moz-backface-visibility: hidden;
            -webkit-transform: translate3d(0, 0, 0);
            -moz-transform: translate3d(0, 0, 0);
            border-radius: $border-radius-theme-1;
            overflow: hidden;
            // box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.3);
            box-shadow: 0 4px 30px rgba(0, 0, 0, 1);
            margin-bottom: 48px;

            @include responsive('md') {
                padding: unset;
                min-width: 600px;
                width: 100%;
                max-width: 1000px;
                margin-bottom: 48px;
            }


            .playerWrapper {
                width: 100%;
                // height: 200px;
                padding-top: 56.25%;
                position: relative;
                // align-items: center;
                // justify-content: center;
                // display: none;

                @include responsive('md') {
                    // display: flex;
                    // width: 100%;
                }

                .videoPlayer {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;

                    >iframe {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }

    .modelSection {
        color: $color-app-white;
        padding: 48px 0;

        @include responsive('md') {
            padding: 96px 0;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .categoryTabBar {
            margin-bottom: 24px;
            overflow-y: scroll;

            .productCategoryButtonContainer {
                display: flex;
            }

            .categoryTabBarButton {
                padding: 12px 24px;
                color: $color-app-white;
                width: fit-content;
                font-weight: bold;
                text-transform: uppercase;
                cursor: pointer;

                &.selected {
                    color: $color-app-gray-900;
                    background-color: $color-app-white;
                }
            }
        }

        .categoryModelSection {
            margin-bottom: 48px;

            .categoryModelSectionHeader {
                margin-bottom: 24px;

                .categoryTitle {
                    width: fit-content;
                    font-weight: bold;
                    text-transform: uppercase;
                    font-size: 20px;
                    color: $color-app-white;
                }
            }
        }

        .modelItemGrid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            row-gap: 24px;
            column-gap: 24px;
            width: 100%;
            // border: 0.5px solid $color-separator-white-1;

            @include responsive('md') {
                grid-template-columns: repeat(6, 1fr);
            }

            .modelItem {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 24px;
                padding-top: 48px;
                row-gap: 24px;
                width: 100%;
                height: 100%;
                border: 0.5px solid $color-separator-white-1;
                transition: all 0.25s ease-in;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                border-radius: $border-radius-theme-2;
                position: relative;
                cursor: pointer;

                &:hover {
                    background-color: $color-app-header-dropdown-menu-item-hover-background;
                }

                .modelImage {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-size: contain;
                    background-position: center;
                    height: 100px;
                    width: 100px;
                }

                .modelTitle {
                    width: 100%;
                    color: #fff;
                    font-size: 10px;
                    text-align: center;
                    font-weight: bold;

                    @include responsive('md') {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}
import { APP_LOCALES } from "constants/app";
import productAll from 'data/productAll.json';
import PRODUCT_CATEGORY from "data/productCategory";
import getConfig from "next/config";
import apiCore from "utils/apiCore";
import removeTrailingSlash from "utils/removeTrailingSlash";

const {
  product_brand: productBrandList,
} = productAll

// pages/sitemap.xml.js 
const { publicRuntimeConfig } = getConfig()
const { APP_URL } = publicRuntimeConfig

const STATIC_PATHS = [
  { path: '/', priority: 1.0 },
  { path: '/products', priority: 0.9 },
  { path: '/products/app-authentication', priority: 0.9 },
  { path: '/search-certificate', priority: 0.9 },
  { path: '/what-we-authenticate', priority: 0.9 },
  { path: '/pricing', priority: 0.9 },
  { path: '/faq', priority: 0.9 },
  { path: '/products/api-authentication', priority: 0.8 },
  { path: '/products/authclass-courses', priority: 0.8 },
  { path: '/contact', priority: 0.8 },
  { path: '/about', priority: 0.8 },
  { path: '/standards', priority: 0.8 },
  { path: '/blog', priority: 0.8 },
  { path: '/legit-token', priority: 0.8 },
  { path: '/financial-guarantee', priority: 0.8 },
  { path: '/luxe-tags', priority: 0.8 },
  { path: '/kicks-tags', priority: 0.8 },
  { path: '/customers', priority: 0.7 },
  { path: '/terms', priority: 0.5 },
  { path: '/privacy', priority: 0.5 },
]

PRODUCT_CATEGORY
  .forEach((categoryItem: any) => {
    STATIC_PATHS.push({
      path: `/what-we-authenticate/category/${categoryItem.slug}`, priority: 0.7
    })
  })
productBrandList
  .map((brandItem: any) => {
    STATIC_PATHS.push({
      path: `/what-we-authenticate/${brandItem.slug}`, priority: 0.7
    })
  })

function generateSiteMap(paths: any) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xhtml="http://www.w3.org/1999/xhtml" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd http://www.w3.org/1999/xhtml http://www.w3.org/2002/08/xhtml/xhtml1-strict.xsd">
<!-- created with Free Online Sitemap Generator www.xml-sitemaps.com -->

${paths
      .map((pathItem: any) => ({ ...pathItem, path: pathItem.path.normalize("NFKD").replace(/[\u0300-\u036F]/g, "") }))
      .map((pathItem: any) => {
        return `${APP_LOCALES.map((locale: any) => (
          `
          <url>
          <loc>${`${APP_URL}${locale === 'en' ? '' : `/${locale}`}${removeTrailingSlash(pathItem.path)}`}</loc>
          <lastmod>2025-04-01T00:00:00+00:00</lastmod>
          <link rel="alternate" hreflang="en" href="${`${APP_URL}${removeTrailingSlash(pathItem.path)}`}" />
          <link rel="alternate" hreflang="zh-hant" href="${`${APP_URL}/zh-Hant${removeTrailingSlash(pathItem.path)}`}" />
          <link rel="alternate" hreflang="zh-hans" href="${`${APP_URL}/zh-Hans${removeTrailingSlash(pathItem.path)}`}" />
          <priority>${pathItem.priority}</priority>
          </url>
        `
        )).join('')
          }
   `
      })
      .join('')
    }
   </urlset>
 `;
}

export async function getServerSideProps({ res }: any) {
  // Generate the XML sitemap with the blog data
  try {
    const [
      articleSlugsResult,
      customerSlugResult,
    ] = await Promise.all([
      apiCore.get(null, `v1/article/slug`),
      apiCore.get(null, `v1/business_verified/slug`)
    ])

    const articleSlugPaths = articleSlugsResult.data.map((slug: any) => ({ path: `/blog/${slug}`, priority: 0.7 }))
    const customerSlugPaths = customerSlugResult.data.map((slug: any) => ({ path: `/customers/${slug}`, priority: 0.7 }))

    console.log('articleSlugPaths', articleSlugPaths)
    console.log('customerSlugPaths', customerSlugPaths)
    const paths = [
      ...STATIC_PATHS,
      ...articleSlugPaths,
      ...customerSlugPaths,
    ].filter((path: any) => !!path)

    // console.log('paths', paths)

    const sitemap = generateSiteMap(paths);
    res.setHeader("Content-Type", "text/xml");
    // Send the XML to the browser
    res.write(sitemap);
    res.end();
    return {
      props: {},
    };
  } catch (error) {
    console.log('error', error)
    return {
      props: {},
    }
  }
}

export default function SiteMap() { }
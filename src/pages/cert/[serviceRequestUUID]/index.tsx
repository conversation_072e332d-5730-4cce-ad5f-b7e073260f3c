import { ArrowRightOutlined, CheckCircleFilled, CheckOutlined, PrinterOutlined } from '@ant-design/icons'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppLayout from 'components/AppLayout'
import CertFooter from 'components/CertFooter'
import CertHeader from 'components/CertHeader'
import { APP_NAME } from 'constants/app'
import moment from 'moment'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import { Gallery, Item } from "react-photoswipe-gallery"
import QRCode from 'react-qr-code'
import apiCore from 'utils/apiCore'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages, getLocalisedField } from 'utils/locale'
import css from './CertificatePage.module.scss'
import resizeImageUrl from 'utils/resizeImageUrl'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import Link from 'next/link'
import { REPLICA_SCORE_ITEM_MAP } from 'constants/replicaScore'
import Head from 'next/head'
import { Tooltip } from 'antd'

// const { publicRuntimeConfig } = getConfig()
// const { API_URL } = publicRuntimeConfig

const getResultTitle = (result: any) => {
  if (result === 'pass') {
    return 'authentic'
  } else if (result === 'not_pass') {
    return 'replica'
  }
  return 'inconclusive'
}

const removeUuidStartingZero = (rawUuid: any) => {
  if (!rawUuid) {
    return null
  }
  let uuid = rawUuid
  while (uuid.charAt(0) === '0') {
    uuid = uuid.substring(1)
  }
  return uuid
}

const CertificateDetailPage = ({ item, pageTitle }: any) => {
  // console.log('item', item)
  const intl = useIntl()
  const router = useRouter()
  // const dispatch = useAppDispatch();
  // const serviceRequestDetailState: any = ({} = useAppSelector(
  //   (state: RootState) => state.serviceRequestDetail
  // ));
  // const {
  //   // item,
  //   // isFetchItemLoading,
  //   // fetchItemErrors,
  // } = serviceRequestDetailState
  const { locale = '' } = router
  // const requestUUID = _.get(router, 'query.serviceRequestUUID')
  const itemUuidStringWithoutZero = removeUuidStartingZero(item?.uuid)
  // const refreshItem = () => {
  //   dispatch(fetchItem({
  //     uuid: requestUUID,
  //   }))
  // }

  // useEffect(() => {
  //   if (router.isReady) {
  //     if (requestUUID) {
  //       refreshItem()
  //     }
  //   }
  // }, [router.isReady])

  // useEffect(async () => {
  //   if (router.isReady && !!serviceRequestUUID && !!item && +item.slug !== serviceRequestUUID) {
  //     await props.reset()
  //     props.fetchItem({ uuid: serviceRequestUUID })
  //   }
  // }, [serviceRequestUUID])

  // useEffect(() => {
  //   if (!isFetchItemLoading && !!fetchItemErrors) {
  //     const errorMessage = fetchItemErrors.map(error => error.message).join(', ')
  //     message.error(errorMessage)
  //   }
  // }, [fetchItemErrors])

  const getCaseResult = (result: any) => {
    if (result === `pass`) {
      return intl.formatMessage({ id: 'app_meta_data_authentication_result_authentic_title' })
    }
    if (result === `not_pass`) {
      return intl.formatMessage({ id: 'app_meta_data_authentication_result_replica_title' })
    }
  }

  const getCertifcateContent = () => {
    if (!item) {
      // return null
      return (
        <div className={''}>
          <HomePageSectionHeader
            className={css.sectionHeader}
            subtitle={intl.formatMessage({ id: `certificate_page_title` })}
            title={intl.formatMessage({ id: 'request_detail_page_not_found_title' })}
            description={intl.formatMessage({ id: 'request_detail_page_not_found_description' })}
          />
        </div>
      )
    }
    if (item.result !== 'pass' && item.result !== 'not_pass') {
      return null
    }
    // console.log('item', item)
    return (
      <div className={css.certificateContent}>
        <div className={css.certificateHeader}>
          <div className={css.certificateHeaderBackground}>
            {
              new Array(1000)
                .fill(`#${item.uuid}`)
                .map((value, index) => (<span key={`certificate-header-background-${index}`}>{value}</span>))
            }
          </div>
          <div className={css.certificateHeaderContent}>
            <img
              title={APP_NAME}
              className={css.appLogo}
              src='/logo-app-full.svg'
              alt={APP_NAME}
            />
            <div className={css.certificateTitle}>
              {intl.formatMessage({ id: `certificate_page_title` })}
            </div>
            <div className={css.certificateIssuedBy}>
              {intl.formatMessage({ id: `certificate_page_subtitle` })}
            </div>
            <div className={css.certificateCaseId}>
              #{item.uuid}
            </div>
            <div className={css.certificateResultContainer}>
              <div>
                <div
                  className={clsx(css.certificateResult, item.result === 'pass' ? css.authentic : css.replica)}
                >
                  {getCaseResult(item.result)}
                </div>
              </div>
            </div>
            <div className={css.certifacteDisclaimer}>
              {intl.formatMessage({ id: `certificate_page_disclaimer` })}
            </div>
            <div className={css.certificateQRCode}>
              <QRCode value={`https://legitapp.com/cert/${item.uuid}`} size={70} />
            </div>
          </div>
        </div>
        <div className={css.certificateInformation}>
          <div className={css.certificateInformationBackground}>
            {
              new Array(1000)
                .fill(`#${item.uuid}`)
                .map((value, index) => (<span key={`certificate-information-background-${index}`}>{value}</span>))
            }
          </div>
          <div className={css.certificateInformationItem}>
            <div className={css.informationItemTitle}>
              {intl.formatMessage({ id: `certificate_page_owner` })}
              {
                !!item.user.business_verified
                  ? (
                    <Tooltip
                      title={intl.formatMessage({ id: `certificate_page_owner_verified_message` })}
                    >
                      <div className={css.userVerified}>
                        <CheckCircleFilled />
                        {intl.formatMessage({ id: `certificate_page_owner_verified` })}
                      </div>
                    </Tooltip>
                  )
                  : null
              }
            </div>
            <div className={css.informationItemContent}>
              <div className={css.userName}>
                {`${item.certificate_owner_name || item.user.name}`}
              </div>
            </div>
          </div>
          <div className={css.certificateInformationItem}>
            <div className={css.informationItemTitle}>
              {intl.formatMessage({ id: `certificate_page_case_id` })}
            </div>
            <div className={css.informationItemContent}>
              {item.uuid}
            </div>
          </div>
          <div className={css.certificateInformationItem}>
            <div className={css.informationItemTitle}>
              {intl.formatMessage({ id: `certificate_page_item_category` })}
            </div>
            <div className={css.informationItemContent}>
              {getLocalisedField(item.product_category, 'title', locale)}
            </div>
          </div>
          <div className={css.certificateInformationItem}>
            <div className={css.informationItemTitle}>
              {intl.formatMessage({ id: `certificate_page_item_brand` })}
            </div>
            <div className={css.informationItemContent}>
              {getLocalisedField(item.product_brand, 'title', locale)}
            </div>
          </div>
          <div className={css.certificateInformationItem}>
            <div className={css.informationItemTitle}>
              {intl.formatMessage({ id: `certificate_page_case_submitted` })}
            </div>
            <div className={css.informationItemContent}>
              {moment(item.created_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).local().format('YYYY-MM-DD HH:mm')}
            </div>
          </div>
          <div className={css.certificateInformationItem}>
            <div className={css.informationItemTitle}>
              {intl.formatMessage({ id: `certificate_page_case_completed` })}
            </div>
            <div className={css.informationItemContent}>
              {moment(item.completed_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).local().format('YYYY-MM-DD HH:mm')}
            </div>
          </div>
          {
            item.legit_tag_uuid && (
              <>
                <div className={css.certificateInformationItem}>
                  <div className={css.informationItemTitle}>
                    {intl.formatMessage({ id: `certificate_page_attachement` })}
                  </div>
                  <div className={css.informationItemContent}>
                    {
                      item.product_category.title.toLowerCase() === `sneakers`
                        ? intl.formatMessage({ id: `tags_page_title` })
                        : intl.formatMessage({ id: `lux_tags_page_title` })
                    }
                  </div>
                </div>
                <div className={css.certificateInformationItem}>
                  <div className={css.informationItemTitle}>
                    {intl.formatMessage({ id: `certificate_page_physical_tag_id` })}
                  </div>
                  <div className={css.informationItemContent}>
                    {item.legit_tag_uuid}
                  </div>
                </div>
              </>
            )
          }
          {
            item.nft_certificate_minted
              && item.nft_certificate_image_url
              ? (
                <>
                  <div className={css.certificateInformationItem}>
                    <div className={css.informationItemTitle}>
                      {intl.formatMessage({ id: `certificate_page_blockchain_network` })}
                    </div>
                    <div className={css.informationItemContent}>
                      <a href={`https://polygonscan.com/nft/0x3bb83e704de6c1c6370232e75b3da15f3398d75d/${itemUuidStringWithoutZero}`} target='_blank' rel='noopener noreferrer'>
                        {intl.formatMessage({ id: `certificate_page_polygon` })}
                      </a>
                    </div>
                  </div>
                  <div className={css.certificateInformationItem}>
                    <div className={css.informationItemTitle}>
                      {intl.formatMessage({ id: `certificate_page_onchain_token_id` })}
                    </div>
                    <div className={css.informationItemContent}>
                      <a href={`https://opensea.io/assets/matic/0x3bb83e704de6c1c6370232e75b3da15f3398d75d/${itemUuidStringWithoutZero}`} target='_blank' rel='noopener noreferrer'>
                        {itemUuidStringWithoutZero}
                      </a>
                    </div>
                  </div>
                </>
              )
              : null
          }
          {(item.service_request_result || []).map((requestResult: any) => (
            <div className={css.certificateInformationItem} key={`item-service-request-result-${requestResult.id}`}>
              <div className={css.informationItemTitle}>
                {`${intl.formatMessage({ id: 'request_detail_page_authenticator' })} - ${requestResult.checker.name} #${requestResult.checker_id}`}
              </div>
              <div className={css.informationItemContent}>
                {getCaseResult(item.result)}
              </div>
              {
                !!requestResult.checker_remark && !(requestResult.checker_remark === `AUTHENTIC` || requestResult.checker_remark === `REPLICA`) && (
                  <div className={css.informationItemRemark}>
                    {requestResult.checker_remark}
                  </div>
                )
              }
            </div>
          ))}
          {
            item.result === 'not_pass'
              ? (
                <div className={css.certificateInformationItem}>
                  <div className={css.informationItemTitle}>
                    {intl.formatMessage({ id: `replica_score_bot` })}
                  </div>
                  <div className={css.informationItemContent}>
                    {getCaseResult(item.result)}
                  </div>
                  {
                    !!item.fake_rating
                      ? (
                        <div className={css.informationItemRemark}>
                          {intl.formatMessage({ id: `replica_score_title` })}: {item.fake_rating}, {(REPLICA_SCORE_ITEM_MAP as any)[item.fake_rating]?.title ? intl.formatMessage({ id: (REPLICA_SCORE_ITEM_MAP as any)[item.fake_rating]?.title }) : ''}
                        </div>
                      )
                      : null
                  }
                </div>
              ) : null
          }
        </div>
        <div
          className={clsx(css.certificateFinalResult, item.result === 'pass' ? css.authentic : css.replica)}
        >
          <div className={css.subtitle}>{intl.formatMessage({ id: 'certificate_page_authentication_result' })}</div>
          <div className={css.title}>{getCaseResult(item.result)}</div>
          <Link href='/standards' className={css.standardLink}>
            {intl.formatMessage({ id: 'certificate_page_standard_link' })} <ArrowRightOutlined />
          </Link>
        </div>
        <div className={css.certificateDocument}>
          <div className={css.certificateLetterContent}>
            {intl.formatMessage({ id: `certificate_page_letter_content` }, { result: getCaseResult(item.result), uuid: `#${item.uuid}` })}
          </div>
          {
            item.service_extra_service_insurance_purchased
              ? (
                <div className={css.certificateLetterContent}>
                  {intl.formatMessage({ id: `certificate_page_letter_content_financial_guarantee` })}
                </div>
              )
              : null
          }
          <div className={css.certificateLetterContent}>
            {intl.formatMessage({ id: `certificate_page_letter_content_footer` })}
          </div>
        </div>
        <div className={css.certificateImageAttachement}>
          <Gallery>
            {
              (item.service_request_image || [])
                .filter((serviceRequestImage: any) => !!serviceRequestImage.marker_image_url)
                .map((serviceRequestImage: any) => (
                  (serviceRequestImage.marker_image_url || '')
                    .split(',')
                    .filter((imageUrl: any) => !!imageUrl)
                    .map((imageUrl: any) => (
                      <Item
                        key={`item-request-image-${imageUrl}`}
                        original={
                          imageUrl.indexOf('legitapp-prod.oss-cn-hongkong.aliyuncs.com') > 0
                            ? `${getImageUrl(imageUrl)}/CC7V8TY6TvLpyPUmjPdBEDH6X2RToCDpPxHnm7ovt9vN2ysGjkdoUXc9kcBdBrA`
                            : `${getImageUrl(imageUrl)}/v9TdsLkXrciiuJ2ThTYA9fpmeZTo8WUm6e8RjFpFLXZwbxkR9XNAgGL4ya7bRKD`
                        }
                        width="1000"
                        height="1000"
                      >
                        {({ ref, open }) => (
                          <div className={css.itemRequestImage} ref={ref} onClick={open}>
                            <img
                              src={
                                imageUrl.indexOf('legitapp-prod.oss-cn-hongkong.aliyuncs.com') > 0
                                  ? `${getImageUrl(imageUrl)}/CC7V8TY6TvLpyPUmjPdBEDH6X2RToCDpPxHnm7ovt9vN2ysGjkdoUXc9kcBdBrA`
                                  : `${getImageUrl(imageUrl)}/v9TdsLkXrciiuJ2ThTYA9fpmeZTo8WUm6e8RjFpFLXZwbxkR9XNAgGL4ya7bRKD`
                              }
                              alt={pageTitle}
                            />
                          </div>
                        )}
                      </Item>
                    ))
                ))
            }
            {
              (item.service_request_image || []).map((serviceRequestImage: any) => (
                <Item
                  key={`item-request-image-${serviceRequestImage.id}`}
                  original={
                    serviceRequestImage.image_url.indexOf('legitapp-prod.oss-cn-hongkong.aliyuncs.com') > 0
                      ? `${getImageUrl(serviceRequestImage.image_url)}/CC7V8TY6TvLpyPUmjPdBEDH6X2RToCDpPxHnm7ovt9vN2ysGjkdoUXc9kcBdBrA`
                      : `${getImageUrl(serviceRequestImage.image_url)}/v9TdsLkXrciiuJ2ThTYA9fpmeZTo8WUm6e8RjFpFLXZwbxkR9XNAgGL4ya7bRKD`
                  }
                  width="1000"
                  height="1000"
                >
                  {({ ref, open }) => (
                    <div className={css.itemRequestImage} ref={ref} onClick={open}>
                      <img
                        src={
                          serviceRequestImage.image_url.indexOf('legitapp-prod.oss-cn-hongkong.aliyuncs.com') > 0
                            ? `${getImageUrl(serviceRequestImage.image_url)}/CC7V8TY6TvLpyPUmjPdBEDH6X2RToCDpPxHnm7ovt9vN2ysGjkdoUXc9kcBdBrA`
                            : `${getImageUrl(serviceRequestImage.image_url)}/v9TdsLkXrciiuJ2ThTYA9fpmeZTo8WUm6e8RjFpFLXZwbxkR9XNAgGL4ya7bRKD`
                        }
                        alt={pageTitle}
                      />
                    </div>
                  )}
                </Item>
              ))
            }
          </Gallery>
        </div>

      </div>
    )
  }

  return (
    <div className={css.CertificatePage}>
      <Head>
        <meta name="robots" content="noindex , nofollow" />
      </Head>
      <AppLayout>
        <CertHeader />
        <AppContainer className={css.pageContainer}>
          {/* <div className="website-url-banner">
            <div className="url-message">
              <WarningFilled /> Please check that you are visiting from the only official LEGIT APP website url.
            </div>
            <div className="url-box">
              <LockFilled />
              <div className="separator" />
              <span>
                <span style={{ color: 'green' }}>https</span>://legitapp.com
              </span>
            </div>
          </div> */}
          {getCertifcateContent()}
          {
            !!item && (
              <button className={css.printButton} onClick={() => window.print()}><PrinterOutlined /> {intl.formatMessage({ id: `certificate_page_print_certificate` })}</button>
            )
          }
        </AppContainer>
        <CertFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const { serviceRequestUUID: itemId } = params;
  if (itemId && itemId !== "undefined" && itemId.length === 16) {
    try {
      const item = await apiCore.get(null, `v2/service_feed/uuid/${itemId}`)
      if (item) {
        const {
          cover_image_url: coverImageUrl,
          result,
          uuid,
        } = item
        if (result !== 'pass' && result !== 'not_pass') {
          return { props: {} }
        }
        const pageTitle = `#${uuid} ${getResultTitle(result).toUpperCase()} | ${getLocaleMessages(locale)?.[`app_title`]} | ${getLocaleMessages(locale)?.[`certificate_page_title`]}`
        const pageMetaTagItemMap: any = {
          'og-type': {
            property: "og:type",
            content: "article",
          },
          'og-title': {
            property: "og:title",
            content: pageTitle,
          },
        }
        if (coverImageUrl) {
          pageMetaTagItemMap['og-image'] = {
            property: 'og:image',
            content: resizeImageUrl(getImageUrl(coverImageUrl), { width: 600 }),
          }
        }
        return {
          props: {
            pageTitle,
            pageMetaTagItemMap,
            item,
          },
        };
      }
    } catch (error) {
      console.log(`error for v2/service_feed/uuid/${itemId}`, error);
    }
  }
  return {
    notFound: true,
  }
}

export async function getStaticPaths() {
  return { paths: [], fallback: "blocking" };
}

export default CertificateDetailPage

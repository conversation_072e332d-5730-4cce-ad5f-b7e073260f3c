.CertificatePage {
  background: #000;
  min-height: 100vh;
  -webkit-user-select: none;
  user-select: none;

  .pageContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0px 12px;
    overflow: hidden;
    min-height: 100vh;

    @include responsive('md') {
      padding: 48px;
    }
  }

  .certificateContent {
    border: 1px solid $color-separator-white-2;
    max-width: 800px;
    border-radius: $border-radius-theme-1;
    overflow: hidden;
    width: 100%;

    @media print {
      border: none;
    }

    .certificateHeader {
      color: $color-app-white;
      border-radius: 3px;
      background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px;
      padding-top: 48px;
      position: relative;
      overflow: hidden;

      @media print {
        border: 1px solid #000;
        border-radius: 8px;
        margin-top: 5vh;
      }

      @include responsive('md') {
        padding: 48px;
      }

      .certificateHeaderBackground {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.03;
        display: flex;
        font-weight: bold;
        row-gap: 6px;
        column-gap: 24px;
        max-width: 800px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        pointer-events: none;
      }

      .certificateHeaderContent {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
      }

      .appLogo {
        margin-bottom: 24px;
        height: 20px;
        -webkit-user-drag: none;

        @media print {
          -webkit-filter: invert(100%);
          filter: invert(100%);
        }

        @include responsive('md') {
          height: 30px;
        }
      }

      .certificateTitle {
        width: fit-content;
        color: $color-app-white;
        font-size: 30px;
        line-height: 34px;
        text-align: center;
        font-weight: 800;
        text-transform: uppercase;
        margin-bottom: 6px;

        @media print {
          color: #000;
        }
      }

      .certificateIssuedBy {
        color: $color-app-white;
        opacity: 0.6;
        margin-bottom: 24px;

        @media print {
          color: #000;
          opacity: 0.7;
        }
      }

      .certificateCaseId {
        font-size: 16px;
        text-align: center;
        font-weight: bold;
        margin-bottom: 12px;

        @media print {
          color: #000;
        }
      }

      .certificateResultContainer {
        .certificateResult {
          padding: 6px 12px;
          border-radius: 3px;
          color: #fff;
          text-transform: uppercase;
          font-size: 24px;
          line-height: 28px;
          font-weight: 900;
          margin-bottom: 12px;
          margin-right: 16px;
          margin-left: 16px;

          &.authentic {
            background: linear-gradient(121deg, #5977FF 0%, #31FFD7 100%);
          }

          &.replica {
            background: linear-gradient(121deg, #FF5F46 0%, #FF1E60 100%);
          }

          @include responsive('md') {
            margin-right: unset;
            margin-left: unset;
            font-size: 20px;
          }
        }
      }

      .certifacteDisclaimer {
        font-size: 14px;
        text-align: center;
        opacity: 0.6;
        max-width: 600px;

        @media print {
          color: #555;
          opacity: 1;
        }
      }

      .certificateQRCode {
        width: fit-content;
        text-align: center;
        padding: 12px;
        // padding-bottom: 0;
        border-radius: 5px;
        background-color: $color-app-white;
        margin: 24px 0 0 0;
      }
    }

    .certificateInformation {
      background-color: $color-app-white;
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      row-gap: 24px;
      column-gap: 24px;
      padding: 24px;
      position: relative;

      @media print {
        padding-top: 5vh;
      }

      @include responsive('md') {
        padding: 40px;
        // grid-template-columns: repeat(2, 1fr);
      }

      .certificateInformationBackground {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.03;
        display: flex;
        font-weight: bold;
        row-gap: 6px;
        column-gap: 24px;
        max-width: 800px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        pointer-events: none;
      }

      .certificateInformationItem {
        position: relative;
        display: flex;
        flex-direction: column;
        flex-shrink: 1;
        flex-grow: 0;
        // overflow-wrap: break-word;

        .informationItemTitle {
          font-size: 12px;
          opacity: 0.8;
          font-weight: bold;
          display: flex;
          align-items: center;
          column-gap: 6px;
          // text-transform: uppercase;

          @include responsive('md') {
            font-size: 14px;
          }

          .userVerified {
            background-color: $color-app-green;
            background: linear-gradient(121deg, #5977FF 0%, #31FFD7 100%);
            padding: 3px 8px;
            height: 18px;
            border-radius: $border-radius-theme-2;
            display: flex;
            column-gap: 3px;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: $color-app-white;
          }
        }

        .informationItemContent {
          font-size: 14px;
          font-weight: bold;
          flex-grow: 0;
          flex-shrink: 1;

          @include responsive('md') {
            font-size: 20px;
          }

          .userName {
            display: flex;
            column-gap: 6px;
            // inline-size: 100px;
            // overflow-wrap: break-word;
            word-break: break-all;
          }
        }

        .informationItemRemark {
          font-size: 12px;
          opacity: 0.8;
          white-space: pre-line;
        }
      }
    }

    .certificateFinalResult {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px 0;
      row-gap: 6px;

      @media print {
        padding-top: 10vh;
        page-break-before: always;
      }

      &.authentic {
        background: linear-gradient(121deg, #5977FF 0%, #31FFD7 100%);
      }

      &.replica {
        background: linear-gradient(121deg, #FF5F46 0%, #FF1E60 100%);
      }

      .subtitle {
        color: $color-app-white;
        font-weight: bold;
        opacity: 0.9;
        text-transform: uppercase;
      }

      .title {
        width: fit-content;
        color: $color-app-white;
        font-size: 30px;
        line-height: 30px;
        text-align: center;
        font-weight: 800;
        text-transform: uppercase;
        margin-bottom: 12px;
      }

      .standardLink {
        color: $color-app-white;
        font-size: 12px;
        height: 30px;
        border: 1px solid $color-app-white;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
        border-radius: 15px;
        column-gap: 6px;
      }
    }

    .certificateDocument {
      padding: 24px;
      background-color: $color-app-white;
      display: grid;
      row-gap: 24px;
      column-gap: 24px;
      border-bottom: 1px solid $color-app-gray-300;

      @media print {
        border: none;
        padding-bottom: 10vh;
      }

      @include responsive('md') {
        padding: 40px;
      }

      .certificateLetterContent {
        opacity: 0.8;
        white-space: pre-line;
      }
    }

    .certificateImageAttachement {
      padding: 24px;
      background-color: $color-app-white;
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      row-gap: 24px;
      column-gap: 24px;

      @media print {
        page-break-before: always;
        padding: 0 !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 0 !important;

        .itemRequestImage {
          flex: 0 0 33%;
          padding: 8px;
          box-sizing: border-box;
        }
      }

      @include responsive('md') {
        grid-template-columns: repeat(4, 1fr);
        padding: 40px;
      }

      .itemRequestImage {
        border-radius: 3px;
        overflow: hidden;
        cursor: pointer;

        img {
          border-radius: 3px;
          overflow: hidden;
          width: 100%;
          height: 100%;
          pointer-events: none;
          object-fit: cover;
          aspect-ratio: 1;
        }
      }
    }
  }
}

.printButton {
  max-width: 800px;
  margin-top: 32px;
  margin-bottom: 24px;
  width: 100%;
  cursor: pointer;
  padding: 10px 12px;
  background-color: $color-app-white;
  border-radius: 4px;
  color: #000;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  // justify-content: space-between;
  column-gap: 12px;
  font-size: 14px;

  @media print {
    display: none;
  }
}
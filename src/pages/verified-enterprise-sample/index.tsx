import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { ZENDESK_KEY, ZENDESK_SETTINGS } from 'constants/app'
import { useIntl } from 'react-intl'
import Zendesk from 'react-zendesk'
import css from './VerifiedEnterpriseSamplePage.module.scss'

// https://nextjs.org/docs/pages/building-your-application/routing/custom-error

const VerifiedEnterpriseSamplePage = () => {
    const intl = useIntl()

    return (
        <div className={css.VerifiedEnterpriseSamplePage}>
            <AppLayout>
                <AppHeader />
                <AppContainer className={css.pageContainer}>
                    <img className={css.verifiedBanner} src='/banner-verified-enterprise-1.png' />
                    <img className={css.verifiedBanner} src='/banner-verified-enterprise-2.png' />
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        // subtitle={}
                        title={intl.formatMessage({ id: 'Legit App Verified Enterprise' })}
                        description={<div>
                            ABC Limited is proud to be a Legit App Verified Enterprise, a distinction that ensures our customers can shop with confidence. Items we offer are authenticated by Legit App Authentication, the world’s leading authentication solution, guaranteeing authenticity and trust in every purchase.
                        </div>}
                    />
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        // subtitle={}
                        title={intl.formatMessage({ id: 'What is Legit App Authentication?' })}
                        description={<div>
                            Legit App Authentication is the global leader in luxury authentication services, specializing in handbags, sneakers, and designer goods. Legit App combines cutting-edge AI with expert authenticators to deliver 99.999% accurate results. With over 2 million users and 3 million cases completed, processing up to thousands of items daily, Legit App sets the standard for fast, reliable, and independent authentication. For more details, please visit: https://legitapp.com.
                        </div>}
                    />
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        // subtitle={}
                        title={intl.formatMessage({ id: 'What It Means to Be Legit App Verified' })}
                        description={<div>
                            As a Legit App Verified Enterprise, we uphold the highest standards of authenticity. This partnership means items we sell undergo Legit App’s rigorous authentication process, ensuring it is 100% genuine before reaching you. Our commitment to transparency and quality, backed by Legit App’s trusted technology and expertise, gives you peace of mind in the transaction.
                        </div>}
                    />
                </AppContainer>
                <AppFooter />
            </AppLayout>
            <Zendesk
                defer
                zendeskKey={ZENDESK_KEY}
                {...ZENDESK_SETTINGS}
            // onLoaded={() => { }}
            />
        </div>
    )
}

export default VerifiedEnterpriseSamplePage

import Image from "next/image";
import Head from "next/head";
import Header from "components/Login/Header";

export default function VerifyEmail() {
  return (
    <div className="h-screen bg-black text-white flex flex-col items-center">
      <Head>
        <title>EMAIL CONFIRMED | LEGIT APP</title>
      </Head>

      {/* Header */}
      <Header />

      {/* Content */}
      <div className="w-full max-w-xl flex flex-col items-center mt-[65px] sm:px-0 px-6">
        <div className="sm:mb-10 mb-8">
          <Image
            src="/login/email-confirmed.svg"
            alt="Email"
            width={160}
            height={160}
            className="object-contain sm:w-40 sm:h-40 w-32 h-32"
          />
        </div>

        <h1 className="sm:text-[52px] text-3xl font-[900] text-center sm:mb-10 mb-8">
          EMAIL CONFIRMED
        </h1>

        <div className="mb-6 text-base">
          Now you can continue opening your account.
        </div>

        <button className="w-full bg-btn-gradient font-bold px-4 py-3 rounded-full">
          Continue
        </button>
      </div>
    </div>
  );
}

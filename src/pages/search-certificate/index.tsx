// import getConfig from 'next/config'
import { Button, Form, Input } from 'antd'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import _ from 'lodash'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { useIntl } from 'react-intl'
import apiCore from 'utils/apiCore'
import { parseError } from 'utils/error'
import { getLocaleMessages } from 'utils/locale'
import { showErrorPopupMessage } from 'utils/message'
import css from './SearchCertificatePage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

// const { publicRuntimeConfig } = getConfig()
// const { API_URL } = publicRuntimeConfig

const SearchCertificatePage = ({ item, pageTitle }: any) => {

    const intl = useIntl()
    const router = useRouter()
    const [form] = Form.useForm()
    const [isSearching, setIsSearching] = useState(false)

    const onFormFinish = async (values: any) => {
        const {
            caseId,
        } = values
        if (!caseId) {
            showErrorPopupMessage(intl.formatMessage({ id: `search_certificate_page_search_input_error` }))
        }
        try {
            setIsSearching(true)
            // search as tag id
            const tagItem = await apiCore.get(null, `v2/service_feed/legit_tag_uuid/${caseId}`)
            // console.log('tagItem', tagItem)
            if (tagItem.service_request_uuid) {
                setIsSearching(false)
                router.push(`/cert/${tagItem.service_request_uuid}`)
                return
            }
            // search as case id
            const caseItem = await apiCore.get(null, `v2/service_feed/uuid/${caseId}`)
            if (caseItem) {
                setIsSearching(false)
                router.push(`/cert/${caseId}`)
                return
            }
        } catch (error) {
            setIsSearching(false)
            const errorCode = _.get(error, `[0].code`)
            if (errorCode === 404) {
                showErrorPopupMessage(intl.formatMessage({ id: `search_certificate_page_search_no_result` }))
                return
            }
            showErrorPopupMessage(parseError(error).message)
        }
    }

    return (
        <div className={css.SearchCertificatePage}>
            <AppPageHiddenH1
                title={pageTitle}
            />
            <AppLayout>
                <AppHeader />
                <div
                    className={css.searchSection}
                    style={{
                        backgroundImage: `url(${`/background-search-certificate.png`})`,
                    }}
                >
                    <AppContainer className={css.sectionContainer}>
                        <div className={css.searchCardContainer}>
                            <div className={css.searchCard}>
                                <HomePageSectionHeader
                                    className={css.cardHeader}
                                    subtitle={intl.formatMessage({ id: `search_certificate_page_subtitle` })}
                                    title={intl.formatMessage({ id: `search_certificate_page_title` })}
                                    description={intl.formatMessage({ id: `search_certificate_page_description` })}
                                />
                                <Form
                                    rootClassName={css.searchForm}
                                    onFinish={onFormFinish}
                                    // onFinishFailed={onFormFinishFailed}
                                    autoComplete='off'
                                    layout='vertical'
                                    className={css.form}
                                    requiredMark={false}
                                    form={form}
                                >
                                    <div className={css.formLayout}>
                                        <Form.Item
                                            name='caseId'
                                        >
                                            <Input
                                                rootClassName={css.formInput}
                                                size='large'
                                                placeholder={intl.formatMessage({ id: 'search_certificate_page_search_input_placeholder' })}
                                            />
                                        </Form.Item>
                                        <Form.Item style={{ marginBottom: '0' }}>
                                            <Button
                                                className={css.formSubmitButton}
                                                type='primary'
                                                htmlType='submit'
                                                size='large'
                                                loading={isSearching}
                                            >
                                                {intl.formatMessage({ id: 'search_certificate_page_search_button_title' })}
                                            </Button>
                                        </Form.Item>
                                    </div>
                                </Form >
                            </div>
                        </div>
                    </AppContainer>
                </div>
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`search_certificate_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
        'og-description': {
            property: "og:description",
            content: getLocaleMessages(locale)?.['search_certificate_page_description'],
        }
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        }
    }
}


export default SearchCertificatePage

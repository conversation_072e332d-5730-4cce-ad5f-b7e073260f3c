.SearchCertificatePage {
    .searchSection {
        min-height: calc(100vh - $app-header-height);
        padding: 48px 0 36px 0;
        display: flex;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: top;
        // flex-direction: column;
        // align-items: center;

        @include responsive('md') {
            padding: 96px 0 60px 0;
        }

        .sectionContainer {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .searchCardContainer {
            max-width: 800px;
            background: linear-gradient(270deg,
                    rgb(89, 119, 255) 0%,
                    rgb(49, 255, 215) 101.39%);
            border-radius: $border-radius-theme-2;
            padding: 2px;
        }

        .searchCard {
            background-color: #000;
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: $border-radius-theme-2;
            padding: 40px;

            .cardHeader {
                margin-bottom: 24px;
            }

            .searchForm {

                .formLayout {
                    // display: grid;
                    // grid-template-columns: 1fr 100px;
                    // column-gap: 24px;
                }

                .formInput {
                    color: $color-app-white;
                    background-color: #000;
                    font-size: 16px;
                    height: 50px;

                    &::placeholder {
                        color: $color-app-white;
                        opacity: 0.6;
                        /* Firefox */
                    }

                    &::-ms-input-placeholder {
                        /* Edge 12 -18 */
                        color: $color-app-white;
                        opacity: 0.6;
                    }
                }


                .formSubmitButton {
                    width: 100%;
                    background-color: $color-app-white;
                    color: $color-app-dark;
                    font-weight: bold;
                    height: 50px;
                }
            }
        }
    }
}
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import FAQContent from 'components/FAQContent'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import { APP_NAME, APP_URL, ZENDESK_KEY, ZENDESK_SETTINGS } from 'constants/app'
import Zendesk from 'react-zendesk'
import { getLocaleMessages } from 'utils/locale'
import css from './FAQPage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import { useIntl } from 'react-intl'
import { WithContext, FAQPage } from 'schema-dts'
import { locale } from 'moment'
import Script from 'next/script'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'

const FAQAppPage = ({ pageTitle }: any) => {
    const intl = useIntl()

    const FAQ_ITEM_LIST_APP = [
        // about legit app
        {
            question: `faq_item_1_question`,
            answer: `faq_item_1_answer`,
        },
        {
            question: `faq_item_2_question`,
            answer: `faq_item_2_answer`,
        },
        {
            question: `faq_item_3_question`,
            answer: `faq_item_3_answer`,
        },
        {
            question: `faq_item_4_question`,
            answer: `faq_item_4_answer`,
        },
        {
            question: `faq_item_5_question`,
            answer: `faq_item_5_answer`,
        },
        {
            question: `faq_item_6_question`,
            answer: `faq_item_6_answer`,
        },
    ]

    const FAQ_ITEM_LIST_PAYMENT = [
        // about token
        {
            question: `faq_item_7_question`,
            answer: `faq_item_7_answer`,
        },
        {
            question: `faq_item_8_question`,
            answer: `faq_item_8_answer`,
        },
        {
            question: `faq_item_9_question`,
            answer: `faq_item_9_answer`,
        },
        {
            question: `faq_item_10_question`,
            answer: `faq_item_10_answer`,
        },
        {
            question: `faq_item_11_question`,
            answer: `faq_item_11_answer`,
        },
        {
            question: `faq_item_12_question`,
            answer: `faq_item_12_answer`,
        },
        {
            question: `faq_item_13_question`,
            answer: `faq_item_13_answer`,
        },
        {
            question: `faq_item_14_question`,
            answer: `faq_item_14_answer`,
        },
    ]

    const FAQ_ITEM_LIST_AUTHENTICATION = [
        // about result
        {
            question: `faq_item_15_question`,
            answer: `faq_item_15_answer`,
        },
        {
            question: `faq_item_16_question`,
            answer: `faq_item_16_answer`,
        },
        {
            question: `faq_item_17_question`,
            answer: `faq_item_17_answer`,
        },
        {
            question: `faq_item_18_question`,
            answer: `faq_item_18_answer`,
        },
        {
            question: `faq_item_19_question`,
            answer: `faq_item_19_answer`,
        },
        {
            question: `faq_item_20_question`,
            answer: `faq_item_20_answer`,
        },
        {
            question: `faq_item_21_question`,
            answer: `faq_item_21_answer`,
        },


        // about submission
        {
            question: `faq_item_22_question`,
            answer: `faq_item_22_answer`,
        },
        {
            question: `faq_item_23_question`,
            answer: `faq_item_23_answer`,
        },
        {
            question: `faq_item_24_question`,
            answer: `faq_item_24_answer`,
        },
        {
            question: `faq_item_25_question`,
            answer: `faq_item_25_answer`,
        },
        {
            question: `faq_item_26_question`,
            answer: `faq_item_26_answer`,
        },

        // about additional photos
        {
            question: `faq_item_27_question`,
            answer: `faq_item_27_answer`,
        },
        {
            question: `faq_item_28_question`,
            answer: `faq_item_28_answer`,
        },
        {
            question: `faq_item_29_question`,
            answer: `faq_item_29_answer`,
        },
        {
            question: `faq_item_30_question`,
            answer: `faq_item_30_answer`,
        },
    ]

    const canonicalUrl = useCanonicalUrl()

    const pageSchema: WithContext<FAQPage> = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        url: canonicalUrl,
        mainEntity: [...FAQ_ITEM_LIST_APP, ...FAQ_ITEM_LIST_PAYMENT, ...FAQ_ITEM_LIST_AUTHENTICATION].map((faqItem: any) => ({
            "@type": "Question",
            name: intl.formatMessage({ id: faqItem.question }),
            acceptedAnswer: {
                "@type": "Answer",
                text: intl.formatMessage({ id: faqItem.answer }),
            },
        })),
        headline: pageTitle,
        description: intl.formatMessage({ id: 'faq_page_faq_section_description' }),
        potentialAction: SCHEMA_POTENTIAL_ACTION,
        // image: "",
        // datePublished: "2023-12-10",
        // dateModified: "2023-12-28",
    };

    return (
        <div className={css.FAQPage}>
            <Script
                id='faq-page-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(pageSchema),
                }}
            />
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <FAQContent />
                <DownloadSection />
                <AppFooter />
                <Zendesk
                    defer
                    zendeskKey={ZENDESK_KEY}
                    {...ZENDESK_SETTINGS}
                // onLoaded={() => { }}
                />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`faq_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap: any = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        },
    };
}

export default FAQAppPage

import AppLayout from 'components/AppLayout'
import KicksTagsIntroductionContent from 'components/KicksTagsIntroductionContent'

import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './TagsPage.module.scss'

const TagsPage = ({ pageTitle }: any) => {
  return (
    <div className={css.TagsPage}>
      <AppPageHiddenH1
        title={`${pageTitle}`}
      />
      <AppLayout>
        <AppHeader />
        <KicksTagsIntroductionContent />
        <DownloadSection />
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const pageTitle = `${getLocaleMessages(locale)?.[`tags_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
  const pageDescription = getLocaleMessages(locale)?.[`tags_page_tutorial_item_1_subtitle`]
  const pageMetaTagItemMap: any = {
    'og-type': {
      property: "og:type",
      content: "article",
    },
    'og-title': {
      property: "og:title",
      content: pageTitle,
    },
    'og-description': {
      property: 'og:description',
      content: pageDescription,
    },
    'og-image': {
      property: 'og:image',
      content: resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/og-kicks-tag.png`), { width: 600 }),
    }
  }
  return {
    props: {
      pageTitle,
      pageMetaTagItemMap,
    }
  }
}

export default TagsPage

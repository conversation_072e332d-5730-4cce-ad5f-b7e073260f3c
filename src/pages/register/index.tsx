import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import { LoadingOutlined, CheckOutlined } from "@ant-design/icons";
import { PATH_ROUTE } from "constants/app";
import Header from "components/Login/Header";
import apiCore from "utils/apiCore";
import { parseError } from "utils/error";

export default function Register() {
  const [email, setEmail] = useState("");
  const [isValidatingEmail, setIsValidatingEmail] = useState(false);
  const [emailValidationStatus, setEmailValidationStatus] = useState<
    "idle" | "valid" | "invalid" | "taken"
  >("idle");
  const [emailError, setEmailError] = useState("");
  const router = useRouter();

  const isValidEmailFormat = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const checkEmailAvailability = async (emailToCheck: string) => {
    if (!isValidEmailFormat(emailToCheck)) {
      setEmailValidationStatus("invalid");
      setEmailError("Please input a valid email address");
      return;
    }

    setIsValidatingEmail(true);
    setEmailError("");

    try {
      await apiCore.post(null, "/v1/utility/check_email", {
        email: emailToCheck,
      });
      setEmailValidationStatus("valid");
    } catch (error: any) {
      setEmailValidationStatus("taken");
      setEmailError(parseError(error).message);
    } finally {
      setIsValidatingEmail(false);
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    if (emailValidationStatus !== "idle") {
      setEmailValidationStatus("idle");
      setEmailError("");
    }
  };

  const handleEmailBlur = () => {
    if (email.trim()) {
      checkEmailAvailability(email.trim());
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    if (emailValidationStatus === "idle") {
      await checkEmailAvailability(email.trim());
      return;
    }

    if (emailValidationStatus === "valid") {
      router.push(PATH_ROUTE.CREATE_PASSWORD + "?email=" + email);
    }
  };

  const isNextButtonDisabled =
    emailValidationStatus !== "valid" || isValidatingEmail;

  return (
    <div className="h-screen bg-black text-white flex flex-col items-center">
      <Head>
        <title>Create your LEGIT APP account</title>
      </Head>

      {/* Header */}
      <Header />

      {/* Content */}
      <div className="w-full max-w-[526px] sm:px-0 px-6 flex flex-col items-center justify-center sm:mt-24 mt-16">
        <h1 className="sm:text-2xl text-xl font-bold mb-4">
          Create your LEGIT APP account
        </h1>

        <div className="sm:mb-10 mb-8">
          <span className="text-sm">Already have an account? </span>
          <Link href={PATH_ROUTE.LOGIN} className="underline font-semibold">
            Log in
          </Link>
        </div>

        <form onSubmit={handleSubmit} className="w-full">
          <p className="mb-1 text-sm">First, enter your email address</p>
          <div className="relative mb-8">
            <input
              value={email}
              onChange={handleEmailChange}
              onBlur={handleEmailBlur}
              className="outline-none w-full bg-transparent border border-gray-600 rounded-lg px-4 py-2 pr-12"
              required
              type="email"
              placeholder="Enter your email"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {isValidatingEmail && (
                <LoadingOutlined className="text-gray-400 animate-spin" />
              )}
              {!isValidatingEmail && emailValidationStatus === "valid" && (
                <CheckOutlined className="!text-green-500" />
              )}
            </div>

            {emailError && (
              <p className="absolute text-red-500 text-sm">{emailError}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={isNextButtonDisabled}
            className={`w-full font-bold py-2 px-4 rounded-full mb-8 transition-all duration-200 ${
              isNextButtonDisabled
                ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                : "bg-btn-gradient text-white"
            }`}
          >
            Next
          </button>
        </form>

        <div className="mb-8 w-full">
          <p className="mb-4 text-sm">Or sign up with</p>
          <div className="flex justify-between gap-4">
            <button className="flex-1 border border-gray-300 rounded-full py-2 flex justify-center items-center">
              <Image
                src="/login/google-icon.svg"
                alt="Google"
                width={24}
                height={24}
              />
            </button>
            <button className="flex-1 border border-gray-300 rounded-full py-2 flex justify-center items-center">
              <Image
                src="/login/facebook-icon.svg"
                alt="Facebook"
                width={24}
                height={24}
              />
            </button>
            <button className="flex-1 border border-gray-300 rounded-full py-2 flex justify-center items-center">
              <Image
                src="/login/apple-icon.svg"
                alt="Apple"
                width={24}
                height={24}
              />
            </button>
          </div>
        </div>

        <p className="text-center text-sm">
          By registering, you accept our{" "}
          <Link href="/terms" className="underline">
            Terms of use
          </Link>{" "}
          and{" "}
          <Link href="/privacy" className="underline">
            Privacy Policy
          </Link>
        </p>
      </div>
    </div>
  );
}

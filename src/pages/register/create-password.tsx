import { useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import bowser from "bowser";
import { v4 as uuidv4 } from "uuid";
import { PATH_ROUTE } from "constants/app";
import Header from "components/Login/Header";
import Password from "components/Login/Password";
import apiCore from "utils/apiCore";
import { fetchUser, userSignIn } from "actions/app";
import useAppDispatch from "hooks/useAppDispatch";
import { parseError } from "utils/error";
import { showErrorPopupMessage } from "utils/message";

export default function CreatePassword() {
  const router = useRouter();
  const { email } = router.query;
  const { locale } = useRouter();
  const dispatch = useAppDispatch();

  const [password, setPassword] = useState("");
  const [isValid, setIsValid] = useState({
    hasLetter: false,
    hasNumber: false,
    hasLength: false,
  });
  const [isCreating, setIsCreating] = useState(false);

  const validatePassword = (pass: string) => {
    setIsValid({
      hasLetter: /[a-zA-Z]/.test(pass),
      hasNumber: /[0-9]/.test(pass),
      hasLength: pass.length >= 9,
    });
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    validatePassword(newPassword);
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid.hasLetter && isValid.hasNumber && isValid.hasLength) {
      try {
        setIsCreating(true);
        await apiCore.post(null, "v1/user", {
          password,
          registration_uuid: uuidv4(),
          language: locale,
          name: email,
          email,
        });
        const browserInfo = bowser
          .getParser(window.navigator.userAgent)
          .getBrowser();

        const loginResult = await apiCore.post(null, `v1/authentication`, {
          email,
          password,
          app: `client`,
          app_version: `0.0.0`,
          device: `web`,
          device_version: `${browserInfo.name} ${browserInfo.version}`,
        });
        const { token } = loginResult;
        dispatch(userSignIn({ accessToken: token }));
        dispatch(fetchUser({ accessToken: token }));
        router.push(PATH_ROUTE.REGISTER_SUCCESS);
      } catch (error) {
        showErrorPopupMessage(parseError(error).message);
      } finally {
        setIsCreating(false);
      }
    }
  };
  return (
    <div className="h-screen bg-black text-white flex flex-col items-center">
      <Head>
        <title>Create your password | LEGIT APP</title>
      </Head>

      {/* Header */}
      <Header />

      {/* Content */}
      <div className="w-full max-w-[526px] sm:px-0 px-6 flex flex-col items-center justify-center sm:mt-24 mt-16">
        <h1 className="sm:text-2xl text-xl font-bold mb-10">
          Create your password
        </h1>

        <form onSubmit={handleSubmit} className="w-full">
          <Password
            handlePasswordChange={handlePasswordChange}
            password={password}
          />

          {isValid.hasLetter && isValid.hasNumber && isValid.hasLength ? (
            <p className="text-center text-sm mb-8">
              Now, that’s a secure password.
            </p>
          ) : (
            <p className="text-center text-sm mb-8">
              Password must contain a{" "}
              <span className="font-semibold">letter</span> and a{" "}
              <span className="font-semibold">number</span>, and be minimum of{" "}
              <span className="font-semibold">9 characters</span>.
            </p>
          )}

          <button
            type="submit"
            disabled={
              !isValid.hasLetter ||
              !isValid.hasNumber ||
              !isValid.hasLength ||
              isCreating
            }
            className={`w-full ${
              isValid.hasLetter &&
              isValid.hasNumber &&
              isValid.hasLength &&
              !isCreating
                ? "bg-btn-gradient"
                : "bg-pink-500/50 cursor-not-allowed"
            } text-white font-bold py-3 px-4 rounded-full mb-8`}
          >
            {isCreating ? "Creating..." : "Continue"}
          </button>
        </form>
      </div>
    </div>
  );
}

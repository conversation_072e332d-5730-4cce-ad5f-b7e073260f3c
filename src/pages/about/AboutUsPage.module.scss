.AboutUsPage {

    .founderSection1 {

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }
    }

    .founderSection2 {
        background-color: $color-card-background;
        padding: 24px 0;

        .founderBannerContent {
            display: grid;
            padding: 24px 0;
            grid-template-columns: 1fr;
            row-gap: 24px;
            align-items: center;
            column-gap: 24px;

            @include responsive('md') {
                grid-template-columns: 2fr 3fr;
            }

            .founderQuote {
                display: flex;
                flex-direction: column;
                row-gap: 24px;
                width: fit-content;
                color: #fff;
                font-size: 20px;
                font-weight: 600;

                .linkedinButton {
                    margin-top: 12px;
                    cursor: pointer;
                    padding: 10px 12px;
                    background-color: $color-linkedin;
                    border-radius: 4px;
                    font-weight: bold;
                    text-align: center;
                    color: $color-app-white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: fit-content;
                    column-gap: 6px;

                    img {
                        width: 20px;
                    }

                    column-gap: 12px;
                    width: 100%;
                    font-size: 14px;

                    @include responsive('md') {
                        width: fit-content;
                        font-size: 16px;
                    }
                }
            }

            .founderCoverImage {
                padding-top: 50%;
                border-radius: $border-radius-theme-2;
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;
            }
        }
    }

    .aboutAppSection {
        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        @include responsive('md') {
            // padding: 96px 0;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .coverImage {
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            border-radius: $border-radius-theme-2;
            width: 100%;
            padding-bottom: 50%;
            margin-bottom: 28px;

            @include responsive('md') {
                padding-bottom: 30%;
                margin-bottom: 48px;
            }
        }


        .storyCardGrid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 24px;
            row-gap: 24px;

            @include responsive('md') {
                grid-template-columns: repeat(2, 1fr);
                column-gap: 48px;
                row-gap: 48px;
            }
        }

        .informationCard {
            display: flex;
            flex-direction: column;
            border: 1px solid $color-separator-white-1;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            overflow: hidden;
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

            .informationPart {
                padding: 24px;
                display: flex;
                flex-direction: column;
                // align-items: center;
                row-gap: 12px;
                min-height: 130px;

                @include responsive('md') {
                    padding: 40px;
                }

                .title {
                    font-weight: bold;
                    font-size: 16px;
                    line-height: 26px;

                    @include responsive('md') {
                        font-size: 20px;
                        line-height: 30px;
                    }
                }

                .subtitle {
                    width: 100%;
                    opacity: 0.7;
                    font-weight: bold;
                    margin-bottom: 3px;
                    font-size: 14px;
                    line-height: 26px;

                    @include responsive('md') {
                        font-size: 16px;
                        line-height: 26px;
                    }
                }

                .description {
                    opacity: 0.6;
                    font-size: 14px;
                    line-height: 24px;

                    @include responsive('md') {
                        font-size: 16px;
                        line-height: 26px;
                    }
                }
            }

            .coverImage {
                background-position: center;
                background-repeat: no-repeat;
                background-size: contain;
                width: 100%;
                padding-bottom: 50%;
            }
        }
    }

    .centerSection {
        padding: 96px 0 24px 0;
        // background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

        .sectionContainer {
            display: grid;
            grid-template-columns: 1fr;
            flex-direction: column;
            column-gap: 48px;
            // border: 1px solid $color-separator-white-1;
            color: $color-app-white;
            // border-radius: $border-radius-theme-2;
            overflow: hidden;

            @include responsive('md') {
                grid-template-columns: 2fr 2fr;
            }
        }

        .informationPart {
            padding: 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            row-gap: 12px;
            min-height: 130px;

            @include responsive('md') {
                padding: 40px;
                align-items: flex-start;
            }

            .title {
                width: fit-content;
                color: #fff;
                font-size: 20px;
                font-weight: 800;
                text-transform: uppercase;
                // margin-bottom: 24px;

                @include responsive('md') {
                    text-align: center;
                    font-size: 40px;
                    line-height: 40px;
                }
            }

            .subtitle {
                width: fit-content;
                color: #fff;
                font-size: 12px;
                line-height: 22px;
                font-weight: bold;
                text-align: center;
                background: linear-gradient(270deg,
                        rgb(89, 119, 255) 0%,
                        rgb(49, 255, 215) 101.39%);
                background-clip: text;
                -webkit-text-fill-color: transparent;

                @include responsive('md') {
                    margin-bottom: 12px;
                    font-size: 20px;
                }
            }

            .description {
                opacity: 0.6;
                font-size: 14px;
                line-height: 24px;
                text-align: center;

                @include responsive('md') {
                    font-size: 16px;
                    line-height: 26px;
                    text-align: left;
                }
            }
        }

        .coverImage {
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
            width: 100%;
            padding-bottom: 50%;
        }
    }


    .achievementSection {
        background: #000000;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        overflow: hidden;
        position: relative;
        overflow: hidden;
        padding: 48px 0;

        @include responsive('md') {
            padding: 96px 0;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .highlightCardGrid {
            display: grid;
            row-gap: 24px;
            column-gap: 24px;
            margin-bottom: 24px;
            grid-template-columns: repeat(1, 1fr);

            @include responsive('md') {
                grid-template-columns: repeat(2, 1fr);
            }

            .highlightCard {
                border: 1px solid $color-separator-white-2;
                color: $color-app-white;
                border-radius: $border-radius-theme-2;
                overflow: hidden;
                min-height: 120px;
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background-repeat: no-repeat;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

                @include responsive('md') {
                    min-height: 180px;
                }

                .cardBackground {
                    position: absolute;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-position: center;
                    background-size: cover;
                    background-repeat: no-repeat;
                }

                .cardContent {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    .cardTitle {
                        font-weight: bold;
                        font-size: 20px;
                        width: 100%;
                        display: flex;
                        column-gap: 10px;
                        text-align: center;
                        justify-content: center;

                        @include responsive('md') {
                            font-size: 20px;
                        }

                        .reviewStars {
                            font-size: 16px;
                            color: #ffc107;
                            display: flex;
                            column-gap: 2px;

                            @include responsive('md') {
                                font-size: 20px;
                            }
                        }
                    }

                    .cardSubtitle {
                        font-size: 14px;
                        font-weight: bold;
                        width: 100%;
                        text-align: center;
                        max-width: 240px;
                        line-height: 20px;

                        @include responsive('md') {
                            max-width: unset;
                            font-size: 16px;
                            line-height: 26px;
                        }
                    }
                }
            }
        }
    }

    .reviewSection {
        background: #000000;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        overflow: hidden;
        position: relative;
        overflow: hidden;
        padding: 48px 0;
        margin-bottom: 48px;

        @include responsive('md') {
            padding: 96px 0;
            margin-bottom: 96px;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .userReviewItemGrid {
            grid-template-columns: repeat(1, 1fr);
            width: 100%;
            column-gap: 24px;
            row-gap: 24px;
            display: grid;

            @include responsive('md') {
                grid-template-columns: repeat(4, 1fr);
            }

            .userReviewItem {
                display: flex;
                flex-direction: column;
                height: 100%;
                width: 100%;
                padding: 12px;
                border: 1px solid $color-separator-white-1;
                color: $color-app-white;
                border-radius: $border-radius-theme-2;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                overflow: hidden;


                @include responsive('md') {
                    padding: 24px;
                }


                a.userReviewLink {
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    height: 100%;
                    width: 100%;

                    @include responsive('md') {
                        align-items: flex-start;
                    }
                }

                .userReviewHeader {
                    width: 100%;
                    display: flex;
                    align-content: center;
                    column-gap: 16px;
                    margin-bottom: 12px;
                    justify-content: space-between;

                    .leftPart {
                        display: flex;
                        align-items: center;
                        column-gap: 10px;

                        @include responsive('md') {
                            column-gap: 16px;
                        }
                    }

                    .rightPart {
                        width: 24px;
                        height: 24px;
                        border-radius: 50%;
                        overflow: hidden;
                        padding: 3px;
                        background-color: $color-app-gray-600;
                    }

                    .arrow {
                        width: 100%;
                        height: 100%;
                        background-image: url('/icon-home-review-arrow.svg');
                        background-size: contain;
                    }

                    .userAvatar {
                        background-repeat: no-repeat;
                        background-position: center;
                        background-size: cover;
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        overflow: hidden;

                        @include responsive('md') {
                            width: 40px;
                            height: 40px;
                        }
                    }

                    .userProfile {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;

                        .userName {
                            color: #fff;
                            font-size: 14px;
                            font-weight: bold;
                            overflow: hidden;
                        }

                        .userSubtitle {
                            color: #fff;
                            font-size: 12px;
                            opacity: 0.6;
                        }
                    }
                }

                .userReviewContent {
                    width: 100%;
                    color: #fff;
                    font-size: 16px;
                    opacity: 0.6;
                    font-weight: 300;
                    opacity: 0.6;
                    font-size: 14px;
                    line-height: 24px;

                    @include responsive('md') {
                        font-size: 16px;
                        line-height: 26px;
                    }
                }
            }
        }
    }
}
import { StarFilled } from '@ant-design/icons'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_NAME, APP_OG_IMAGE_URL, APP_URL } from 'constants/app'
import Head from 'next/head'
import { useIntl } from 'react-intl'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './AboutUsPage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import Script from 'next/script'
import { AboutPage, WithContext } from 'schema-dts'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'

const ITEMS = [
    {
        title: 'Use LEGIT App to Authenticate Handbags, Sneakers and Streetwear in 12 hours.',
        user: 'HYPEBEAST',
        media: 'Press',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-hypebeast.png',
        url: 'https://hypebeast.com/2020/12/legit-app-digital-authenticator-info',
    },
    {
        title: 'Don’t Get Duped: LEGIT APP Offers Rapid Sneaker Authentication',
        user: 'SneakerFreaker',
        media: 'Press',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-sneakerfreaker.png',
        url: 'https://www.sneakerfreaker.com/news/dont-get-duped-legit-app-offers-rapid-sneaker-authentication',
    },
    {
        title: 'Authenticate with LEGIT APP | Descargar la aplicacion',
        user: 'iByre',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-ibyre.png',
        url: 'https://www.youtube.com/watch?v=uld4ujtS40k',
    },
    {
        title: 'BEST & WORST LUXURY BAGS Feat. LEGIT APP Authentication',
        user: 'Lailli Mirza',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-lailli.png',
        url: 'https://www.youtube.com/watch?v=e4vxS4NpxAY&t=969s',
    },
    {
        title: `I got scammed for FAKE Sneakers! Here's what I did...`,
        user: 'ConnorTV',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-connortv.png',
        url: 'https://www.youtube.com/watch?v=ex_teeemD4I',
    },
    // {
    //     title: '讓女友不看價錢買名牌！一天花了接近5萬？！波鞋潮牌鑒定!',
    //     user: 'arhoTV',
    //     media: 'YouTuber',
    //     cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-sunny.png',
    //     url: 'https://www.youtube.com/watch?v=h3Fba-mhveI',
    // },
    {
        title: 'Hypebeast Pickups Haul Đồ Tết ( Nike, Jordan, Offwhite, Palm Angels, Versace, Givenchy...)',
        user: 'Fabo Nguyen',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-fabo.png',
        url: 'https://www.youtube.com/watch?v=2pFAKmogalM',
    },
    {
        title: 'How to know if your sneakers are real or fake ? | Legit checking India',
        user: 'Karan Khatri',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-karan.png',
        url: 'https://www.youtube.com/watch?v=NA6XKLAIAZI',
    },
    {
        title: '超保值！精品包投資教學，百萬包現身！ LEGIT APP 奢侈品鑒定【王思佳】',
        user: '王思佳',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-beauty-wang.png',
        url: 'https://www.youtube.com/watch?v=fS6Q8OZJGtc&t=94s',
    },
    {
        title: 'They legit check your shoes and they do it really well.',
        user: 'WearTesters',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-weartesters.png',
        url: 'https://www.youtube.com/watch?v=MPcJMgaVKlU',
    },
    {
        title: 'How I legit Check Sneakers!!! *LEGIT APP* I started to use this great app!',
        user: 'Cam’s Kicks',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-camskicks.png',
        url: 'https://www.youtube.com/watch?v=ellhSK4cN3Q',
    },
    {
        title: 'FAKE OR REAL ?? BEST WAY TO LEGIT CHECK SNEAKERS *LEGIT APP*',
        user: 'JumperMan Kris',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-jumpermankris.png',
        url: 'https://www.youtube.com/watch?v=aZFa5mu6llY',
    },
    {
        title: 'How To Legit Check Your Sneaker Collection With Legit App',
        user: 'DNA Show',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-dnashow.png',
        url: 'https://www.youtube.com/watch?v=WdMWlwrTeqk',
    },
    {
        title: 'TESTING OUT SNEAKER LEGIT CHECKING APPS! ',
        user: 'RealorFakeOfficial',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-realorfake.png',
        url: 'https://www.youtube.com/watch?v=BwSlooDI8WM',
    },
    // {
    //     title: 'G-Dragon PEACEMINUSONE X NIKE AF1 PARA-NOISE Checked by LEGIT APP',
    //     user: 'Sammicci',
    //     media: 'YouTuber',
    //     cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-sammicci.png',
    //     url: 'https://www.youtube.com/watch?v=HIboXCeCs90',
    // },
    {
        title: '買鞋真假難分辨 靠這個方法就對啦～｜線上檢驗平台 LEGIT APP 分享',
        user: 'THE SHORTY 那個矮子',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-theshorty.png',
        url: 'https://www.youtube.com/watch?v=rW8MpVwDX7Q',
    },
    {
        title: '網購精品買到假貨?APP線上辨別真假!送 Legit 鑒定結果',
        user: 'tramy崔咪',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-tramy.png',
        url: 'https://www.youtube.com/watch?v=Y3rheLGkryM',
    },
    {
        title: '老師不藏私分享如何分辨真假精品包！JUNJUN SQUARE X LEGIT APP',
        user: 'JUN JUN SQUARE',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-junjunsquare.png',
        url: 'https://www.youtube.com/watch?v=qRXb75PNLSA',
    },
    {
        title: 'DID I GET FINESSED?? LEGIT CHECKING MY TRAVIS SCOTT 1s WITH THE LEGIT APP',
        user: 'JAY THE SNEAKER GUY',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-jaythesneaker.png',
        url: 'https://www.youtube.com/watch?v=OGJ_wH0TPO0',
    },
    {
        title: 'How To Legit Check Your ENTIRE Sneaker Collection With Legit App! *TUTORIAL*',
        user: 'DEVINTAGE',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-devintage.png',
        url: 'https://youtu.be/9OrTnvnxyqs',
    },
    {
        title: 'If you want to make sure your pair is authentic, make sure you download LEGIT APP.',
        user: 'Josh Dominic',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-joshdominic.png',
        url: 'https://www.youtube.com/watch?v=rj8U0Iyy6Wg',
    },
    {
        title: 'Best Way To Legit Check Your Sneakers "GUARANTEED" -> LEGIT APP',
        user: 'Sniper J0nes',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-sniper.png',
        url: 'https://www.youtube.com/watch?v=zP2nXEuekTU',
    },
    {
        title: 'Get Supreme and sneakers authenticated with LEGIT APP before you cop or sell.',
        user: 'Strictlypreme',
        media: 'Instagram',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-strictlypremei.png',
        url: 'https://www.instagram.com/p/CHsauteFw5d/',
    },
    {
        title: '兩萬塊不翼而飛的痛 我...買到假鞋了? ft. LEGIT APP',
        user: '布萊恩的世界 BryantChao',
        media: 'YouTuber',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-bryantchao.png',
        url: 'https://www.youtube.com/watch?v=nQxqbRx-06Q',
    },
    {
        title: 'LEGIT APP，專業鑒定球鞋、潮流時裝、奢侈品手袋等時尚產品。透過手機上載照片後，12 小時內可得到鑒定師給予的鑒定結果。',
        user: 'HYPEBEAST ZH',
        media: 'Press',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-hypebeastzh.png',
        url: 'https://hypebeast.com/zh/2020/12/legit-app',
    },
    {
        title: '方便易用的波鞋、手袋鑒定App！以LEGIT APP快速鑒定各式時尚單品與潮物',
        user: 'ELLE MAGAZINE',
        media: 'Press',
        cover_image_url: 'https://legitapp-static.oss-accelerate.aliyuncs.com/icon-ellehk.png',
        url: 'https://www.elle.com.hk/fashion/legit-app-instant-authentication-of-fashion-items',
    },
]

const AboutUsPage = ({ pageTitle }: any) => {

    const intl = useIntl()

    const canonicalUrl = useCanonicalUrl()

    const pageSchema: WithContext<AboutPage> = {
        "@context": "https://schema.org",
        "@type": "AboutPage",
        url: canonicalUrl,
        name: intl.formatMessage({ id: 'app_title' }),
        description: intl.formatMessage({ id: 'app_og_description' }),
        image: APP_OG_IMAGE_URL,
        potentialAction: SCHEMA_POTENTIAL_ACTION,
    };

    return (
        <div className={css.AboutUsPage}>
            <Script
                id='about-page-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(pageSchema),
                }}
            />
            <AppPageHiddenH1
                title={`${pageTitle}`}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.aboutAppSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={css.sectionHeader}
                            title={intl.formatMessage({ id: 'about_page_app_section_title' })}
                            subtitle={intl.formatMessage({ id: 'about_page_app_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'about_page_app_section_description_1' })} {intl.formatMessage({ id: 'about_page_app_section_description_2' })}
                            </>}
                        />
                        <div
                            className={css.coverImage}
                            style={{
                                backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-1.png`), { width: 1000 })})`,
                            }}
                        />
                    </AppContainer>
                    {/* <AppContainer className={css.sectionContainer}>
                        <div className={css.storyCardGrid}>
                            <div className={css.informationCard}>
                                <div className={css.informationPart}>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'about_page_founder_section_title' })}
                                    </div>
                                    <div className={css.subtitle}>
                                        {intl.formatMessage({ id: 'about_page_founder_section_subtitle' })}
                                    </div>
                                    <div className={css.description}>
                                        {intl.formatMessage({ id: 'about_page_founder_section_description_1' })}
                                        <br />
                                        <br />
                                        {intl.formatMessage({ id: 'about_page_founder_section_description_2' })}
                                    </div>
                                    <a href={'https://www.linkedin.com/in/kkch/'} className={css.resourceCard} target='_blank' rel='noopener noreferrer'>
                                        <div className={css.linkedinButton}>
                                            <img src='/social/icon-social-linkedin.svg' /> {intl.formatMessage({ id: 'about_page_founder_section_action_button_title' })}
                                        </div>
                                    </a>
                                </div>
                                <div
                                    className={css.coverImage}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-3.png`), { width: 1000 })})`,
                                    }}
                                />
                            </div>
                            <div className={css.informationCard}>
                                <div className={css.informationPart}>
                                    <div className={css.title}>
                                        {intl.formatMessage({ id: 'about_page_team_section_title' })}
                                    </div>
                                    <div className={css.subtitle}>
                                        {intl.formatMessage({ id: 'about_page_team_section_subtitle' })}
                                    </div>
                                    <div className={css.description}>
                                        {intl.formatMessage({ id: 'about_page_team_section_description_1' })}
                                        <br />
                                        <br />
                                        {intl.formatMessage({ id: 'about_page_team_section_description_2' })}
                                        <br />
                                        <br />
                                        {intl.formatMessage({ id: 'about_page_team_section_description_3' })}
                                    </div>
                                </div>
                                <div
                                    className={css.coverImage}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-2.png`), { width: 1000 })})`,
                                    }}
                                />
                            </div>
                        </div>
                    </AppContainer> */}
                </div>
                <div className={css.founderSection1}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={css.sectionHeader}
                            title={intl.formatMessage({ id: 'about_page_founder_section_title' })}
                            subtitle={intl.formatMessage({ id: 'about_page_founder_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'about_page_founder_section_description_1' })} {intl.formatMessage({ id: 'about_page_founder_section_description_2' })}
                            </>}
                        />
                        <div
                            className={css.coverImage}
                            style={{
                                backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-1.png`), { width: 1000 })})`,
                            }}
                        />
                    </AppContainer>
                </div>
                <div className={css.founderSection2}>
                    <AppContainer className={css.founderBannerContainer}>
                        <div className={css.founderBannerContent}>
                            <div className={css.founderQuote}>
                                {intl.formatMessage({ id: 'about_page_founder_section_quote' })}
                                <br />
                                <br />
                                {intl.formatMessage({ id: 'about_page_founder_section_quote_from' })}
                                <a href={'https://www.linkedin.com/in/kkch/'} className={css.resourceCard} target='_blank' rel='noopener noreferrer'>
                                    <div className={css.linkedinButton}>
                                        <img src='/social/icon-social-linkedin.svg' /> {intl.formatMessage({ id: 'about_page_founder_section_action_button_title' })}
                                    </div>
                                </a>
                            </div>
                            <div
                                className={css.founderCoverImage}
                                style={{
                                    backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/founder-cover.png`), { width: 1000 })})`,
                                }}
                            />
                        </div>
                    </AppContainer>
                </div>
                <div className={css.centerSection}>
                    <AppContainer className={css.sectionContainer}>
                        <div
                            className={css.coverImage}
                            style={{
                                backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-2.png`), { width: 1000 })})`,
                            }}
                        />
                        <div className={css.informationPart}>
                            <div className={css.subtitle}>
                                {intl.formatMessage({ id: 'about_page_team_section_subtitle' })}
                            </div>
                            <div className={css.title}>
                                {intl.formatMessage({ id: 'about_page_team_section_title' })}
                            </div>
                            <div className={css.description}>
                                {intl.formatMessage({ id: 'about_page_team_section_description_1' })}
                                <br />
                                <br />
                                {intl.formatMessage({ id: 'about_page_team_section_description_2' })}
                                <br />
                                <br />
                                {intl.formatMessage({ id: 'about_page_team_section_description_3' })}
                            </div>
                        </div>
                    </AppContainer>
                </div>
                <div className={css.achievementSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={css.sectionHeader}
                            title={intl.formatMessage({ id: 'about_page_achievement_section_title' })}
                            subtitle={intl.formatMessage({ id: 'about_page_achievement_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'about_page_achievement_section_description' })}
                            </>}
                        />
                    </AppContainer>
                    <AppContainer>
                        <div className={css.highlightCardGrid}>
                            <div className={css.highlightCard}>
                                <div className={css.cardBackground}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-achievements-1.png`), { width: 1000 })})`,
                                    }}
                                />
                                <div className={css.cardContent}>
                                    <div className={css.cardTitle}>
                                        <div className={css.reviewRate}>4.9</div>
                                        <div className={css.reviewStars}><StarFilled /><StarFilled /><StarFilled /><StarFilled /><StarFilled /></div>
                                    </div>
                                    <div className={css.cardSubtitle}>
                                        {intl.formatMessage({ id: 'about_page_achievement_section_item_1_subtitle' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.highlightCard}>
                                <div className={css.cardBackground}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-achievements-2.png`), { width: 1000 })})`,
                                    }}
                                />
                                <div className={css.cardContent}>
                                    <div className={css.cardTitle}>
                                        {intl.formatMessage({ id: 'about_page_achievement_section_item_2_title' })}
                                    </div>
                                    <div className={css.cardSubtitle}>
                                        {intl.formatMessage({ id: 'about_page_achievement_section_item_2_subtitle' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.highlightCard}>
                                <div className={css.cardBackground}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-achievements-3.png`), { width: 1000 })})`,
                                    }}
                                />
                                <div className={css.cardContent}>
                                    <div className={css.cardTitle}>
                                        {intl.formatMessage({ id: 'about_page_achievement_section_item_3_title' })}
                                    </div>
                                    <div className={css.cardSubtitle}>
                                        {intl.formatMessage({ id: 'about_page_achievement_section_item_3_subtitle' })}
                                    </div>
                                </div>
                            </div>
                            <div className={css.highlightCard}>
                                <div className={css.cardBackground}
                                    style={{
                                        backgroundImage: `url(${resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/background-about-achievements-4.png`), { width: 1000 })})`,
                                    }}
                                />
                                <div className={css.cardContent}>
                                    <div className={css.cardTitle}>
                                        {intl.formatMessage({ id: 'about_page_achievement_section_item_4_title' })}
                                    </div>
                                    <div className={css.cardSubtitle}>
                                        {intl.formatMessage({ id: 'about_page_achievement_section_item_4_subtitle' })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </AppContainer>
                </div>
                <div className={css.reviewSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            subtitle={intl.formatMessage({ id: 'about_page_review_section_subtitle' })}
                            title={intl.formatMessage({ id: 'about_page_review_section_title' })}
                            description={<>
                                {intl.formatMessage({ id: 'about_page_review_section_description' })}
                            </>}
                        />
                    </AppContainer>
                    <AppContainer>
                        <div className={css.userReviewItemGrid}>
                            {
                                ITEMS.map(item => (
                                    <a
                                        href={item.url}
                                        target='_blank'
                                        rel='noopener noreferrer'
                                        className={css.userReviewItem}
                                        key={`home-page-user-review-item-${item.title}-${item.user}`}
                                    >
                                        <div className={css.userReviewHeader}>
                                            <div className={css.leftPart}>
                                                <div
                                                    className={css.userAvatar}
                                                    style={{
                                                        backgroundImage: `url('${resizeImageUrl(item.cover_image_url, { width: 80 })}')`,
                                                    }}
                                                />
                                                <div className={css.userProfile}>
                                                    <div className={css.userName}>
                                                        {item.user}
                                                    </div>
                                                    <div className={css.userSubtitle}>
                                                        {item.media}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className={css.rightPart}>
                                                <div className={css.arrow} />
                                            </div>
                                        </div>
                                        <div className={css.userReviewContent}>
                                            {item.title}
                                        </div>
                                    </a>
                                ))
                            }
                        </div>
                    </AppContainer>
                </div>
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`about_page_title`]} ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap = {
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            // pageMetaTagItems,
            pageMetaTagItemMap
        },
    };
}


export default AboutUsPage


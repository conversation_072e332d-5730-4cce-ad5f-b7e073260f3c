.UserForgotPasswordPage {
    position: relative;

    .backButton {
        display: flex;
        align-items: center;
        height: 40px;
        border: 1px solid $color-separator-white-1;
        // box-shadow: $box-shadow-1;
        border-radius: $border-radius-theme-1;
        position: fixed;
        top: 24px;
        left: 24px;
        z-index: 1000;
        background-color: $color-app-white;
        padding: 14px 24px 14px 24px;
        cursor: pointer;
    }

    .pageLayoutGrid {
        min-height: 100vh;
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        position: relative;
    }

    .formSection {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 120px 0;
        // justify-content: center;

        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .appLogo {
            height: 24px;
        }

        @include responsive('md') {
            // padding: 96px 0;
        }

        .sectionHeader {
            padding: 24px 0 24px 0;

            @include responsive('md') {
                padding: 24px 0 24px 0;
            }
        }

        .formCard {
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: $border-radius-theme-2;
            display: flex;
            row-gap: 24px;
            flex-direction: column;
            justify-content: space-between;
            width: 100%;
            max-width: 500px;

            .cardContent {
                width: 100%;

                .contactForm {
                    width: 100%;


                    label {
                        font-size: 12px;
                        font-weight: bold;
                    }

                    a {
                        color: $color-theme-blue;
                    }

                    .formLayout {
                        // display: grid;
                        // grid-template-columns: 1fr 100px;
                        // column-gap: 24px;
                        width: 100%;
                    }

                    .formItemGrid {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        row-gap: 24px;
                        column-gap: 24px;
                        margin-bottom: 12px;
                    }

                    .formItem {
                        margin-bottom: 0;
                        width: 100%;
                    }

                    .formInput {
                        font-size: 16px;
                        height: 40px;
                        // border: 1px solid $color-separator-white-2;

                        &::placeholder {

                            opacity: 0.6;
                            /* Firefox */
                        }

                        &::-ms-input-placeholder {
                            /* Edge 12 -18 */

                            opacity: 0.6;
                        }
                    }


                    .formSubmitButton {
                        width: 100%;
                        color: $color-app-dark;
                        font-weight: bold;
                        height: 50px;
                    }
                }

                .actionButton {
                    width: 100%;
                    cursor: pointer;
                    padding: 10px 12px;
                    border-radius: 4px;
                    background-color: $color-theme-blue;
                    color: $color-app-white;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    column-gap: 12px;
                    font-size: 14px;
                    height: 46px;
                    border-radius: 23px;
                }
            }
        }

        .sectionFooter {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 24px 0;
            row-gap: 48px;

            .agreement {
                text-align: center;
            }

            a {
                color: $color-theme-blue;
            }
        }
    }
}
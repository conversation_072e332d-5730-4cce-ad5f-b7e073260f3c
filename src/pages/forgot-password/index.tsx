import { Button, Form, Input } from 'antd'
import bowser from 'bowser'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { APP_NAME } from 'constants/app'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { useIntl } from 'react-intl'
import apiCore from 'utils/apiCore'
import { parseError } from 'utils/error'
import getImageUrl from 'utils/imageUrl'
import { showErrorPopupMessage, showSuccessPopupMessage } from 'utils/message'
import css from './UserForgotPasswordPage.module.scss'

const UserForgotPasswordPage = () => {

    const intl = useIntl()
    const router = useRouter()
    const { locale } = router
    const [form] = Form.useForm()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const sendButtonOnClick = async () => {
        if (!form) {
            return
        }
        const {
            email,
        } = form.getFieldsValue(['email'])
        const browserInfo = bowser.getParser(window.navigator.userAgent).getBrowser()
        const requestParams = {
            email,
            app: `client`,
            app_version: `0.0.0`,
            device: `web`,
            device_version: `${browserInfo.name} ${browserInfo.version}`,
        }
        try {
            setIsSubmitting(true)
            // search as tag id
            const result = await apiCore.post(null, `v1/utility/forgot_password`, requestParams)
            setIsSubmitting(false)
            showSuccessPopupMessage(`We've sent you an email to reset the password.`)
            form?.resetFields([
                `email`,
            ])
        } catch (error) {
            console.log('error', error)
            setIsSubmitting(false)
            showErrorPopupMessage(parseError(error).message)
        }
    }

    const backButtonOnClick = () => {
        router.back()
    }

    return (
        <div className={css.UserForgotPasswordPage}>
            <div
                className={css.backButton}
                onClick={backButtonOnClick}
            >
                Back
            </div>
            <div className={css.pageLayoutGrid}>
                <div className={css.formSection}>
                    <AppContainer className={css.sectionContainer}>
                        <img
                            className={css.appLogo}
                            src={getImageUrl('https://authclass-static.oss-cn-hongkong.aliyuncs.com/website/logo-app-full.png')}
                            alt={APP_NAME}
                        />
                        <HomePageSectionHeader
                            className={css.sectionHeader}
                            title={`Forgot Password`}
                            description={`Enter the email address for your account so we can send you reset instructions.`}
                        />
                        <div className={clsx(css.formCard)}>
                            <div className={css.cardContent}>
                                <Form
                                    rootClassName={css.contactForm}
                                    // onFinishFailed={onFormFinishFailed}
                                    autoComplete='off'
                                    layout='vertical'
                                    className={css.form}
                                    requiredMark={false}
                                    form={form}
                                >
                                    <div className={css.formLayout}>
                                        <Form.Item
                                            rootClassName={css.formItem}
                                            name='email'
                                            label={`Email Address`}
                                        >
                                            <Input
                                                rootClassName={css.formInput}
                                                type='email'
                                                autoComplete='email'
                                            // placeholder={intl.formatMessage({ id: 'search_certificate_page_search_input_placeholder' })}
                                            />
                                        </Form.Item>
                                        <div
                                            className={css.register}
                                            style={{ marginBottom: `24px`, padding: `6px 0` }}
                                        >
                                            <Link href='/login'>Wait, I remember my password</Link>
                                        </div>
                                        <Form.Item rootClassName={css.formItem}>
                                            <Button
                                                className={css.actionButton}
                                                type='primary'
                                                onClick={sendButtonOnClick}
                                                size='large'
                                                loading={isSubmitting}
                                            >
                                                Send Reset Instructions
                                            </Button>
                                        </Form.Item>
                                    </div>
                                </Form >
                            </div>
                        </div>
                    </AppContainer>
                </div>
            </div>
        </div >
    )
}

export default UserForgotPasswordPage


import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { ZENDESK_KEY, ZENDESK_SETTINGS } from 'constants/app'
import { useIntl } from 'react-intl'
import Zendesk from 'react-zendesk'
import css from './NotFoundPage.module.scss'

// https://nextjs.org/docs/pages/building-your-application/routing/custom-error

const NotFoundPage = () => {
    const intl = useIntl()

    return (
        <div className={css.NotFoundPage}>
            <AppLayout>
                <AppHeader />
                <AppContainer className={css.pageContainer}>
                    <HomePageSectionHeader
                        className={css.sectionHeader}
                        subtitle={intl.formatMessage({ id: `error_404_page_subtitle` })}
                        title={intl.formatMessage({ id: 'error_404_page_title' })}
                        description={intl.formatMessage({ id: 'error_404_page_description' })}
                    />
                </AppContainer>
                <DownloadSection />
                <AppFooter />
            </AppLayout>
            <Zendesk
                defer
                zendeskKey={ZENDESK_KEY}
                {...ZENDESK_SETTINGS}
            // onLoaded={() => { }}
            />
        </div>
    )
}

export default NotFoundPage

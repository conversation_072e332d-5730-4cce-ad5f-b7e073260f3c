import AppLayout from 'components/AppLayout'
import LuxeTagsIntroductionContent from 'components/LuxeTagsIntroductionContent'

import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import { getLocaleMessages } from 'utils/locale'
import css from './TagsPage.module.scss'
import resizeImageUrl from 'utils/resizeImageUrl'
import getImageUrl from 'utils/imageUrl'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

const TagsPage = ({ pageTitle }: any) => {
  return (
    <div className={css.TagsPage}>
      <AppPageHiddenH1
        title={pageTitle}
      />
      <AppLayout>
        <AppHeader />
        <LuxeTagsIntroductionContent />
        <DownloadSection />
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const pageTitle = `${getLocaleMessages(locale)?.[`lux_tags_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
  const pageDescription = getLocaleMessages(locale)?.[`lux_tags_page_tutorial_item_1_subtitle`]
  const pageMetaTagItemMap: any = {
    'og-type': {
      property: "og:type",
      content: "article",
    },
    'og-title': {
      property: "og:title",
      content: pageTitle,
    },
    'og-description': {
      property: 'og:description',
      content: pageDescription,
    },
    'og-image': {
      property: 'og:image',
      content: resizeImageUrl(getImageUrl(`https://legitapp-static.oss-accelerate.aliyuncs.com/og-luxe-tag.png`), { width: 600 }),
    }
  }
  return {
    props: {
      pageTitle,
      pageMetaTagItemMap,
    }
  }
}


export default TagsPage

import AppLayout from 'components/AppLayout'
import moment from 'moment'
import { useRouter } from 'next/router'
import { useIntl } from 'react-intl'
import QRCode from 'react-qr-code'
// import ImageLogo from '../../../components/ImageLogo'
import { ArrowRightOutlined, CaretUpOutlined } from '@ant-design/icons'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import { REPLICA_1_COLOR, REPLICA_2_COLOR, REPLICA_3_COLOR, REPLICA_4_COLOR, REPLICA_5_COLOR, REPLICA_SCORE_ITEM_MAP } from 'constants/replicaScore'
import Head from 'next/head'
import Link from 'next/link'
import { Gallery, Item } from 'react-photoswipe-gallery'
import apiCore from 'utils/apiCore'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages, getLocalisedField } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './CaseDetailPage.module.scss'

// const { publicRuntimeConfig }m = getConfig()
// const { API_URL } = publicRuntimeConfig

const getResultTitle = (result: any) => {
  if (result === 'pass') {
    return 'authentic'
  } else if (result === 'not_pass') {
    return 'replica'
  }
  return 'inconclusive'
}

const ServiceRequestDetailPage = ({ item, pageTitle }: any) => {
  const intl = useIntl()
  const router = useRouter()
  // const dispatch = useAppDispatch();
  // const serviceRequestDetailState: any = ({} = useAppSelector(
  //   (state: RootState) => state.serviceRequestDetail
  // ));
  // const {
  //   // item,
  //   // isFetchItemLoading,
  //   // fetchItemErrors,
  // } = serviceRequestDetailState
  const { locale = '' } = router
  // console.log('item', item)
  // const requestUUID = _.get(router, 'query.serviceRequestUUID')

  // const refreshItem = () => {
  //   dispatch(fetchItem({
  //     uuid: requestUUID,
  //   }))
  // }

  // useEffect(() => {
  //   if (router.isReady) {
  //     if (requestUUID) {
  //       refreshItem()
  //     }
  //   }
  // }, [router.isReady])

  // useEffect(async () => {
  //   if (router.isReady && !!serviceRequestUUID && !!item && +item.slug !== serviceRequestUUID) {
  //     console.log('serviceRequestUUID change')
  //     await props.reset()
  //     props.fetchItem({ uuid: serviceRequestUUID })
  //   }
  // }, [serviceRequestUUID])

  // useEffect(() => {
  //   if (!isFetchItemLoading && !!fetchItemErrors) {
  //     const errorMessage = fetchItemErrors.map((error) => error.message).join(', ')
  //     message.error(errorMessage)
  //   }
  // }, [fetchItemErrors])

  const getServiceRequestItem = () => {
    if (!item) {
      return (
        <div className={css.serviceRequesItem}>
          <HomePageSectionHeader
            className={css.sectionHeader}
            subtitle={intl.formatMessage({ id: 'request_detail_page_case' })}
            title={intl.formatMessage({ id: 'request_detail_page_not_found_title' })}
            description={intl.formatMessage({ id: 'request_detail_page_not_found_description' })}
          />
        </div>
      )
    }
    return (
      <div className={css.serviceRequesItem}>
        {/* <div className={css.itemTitle}>
        {`${intl.formatMessage({ id: 'request_detail_page_case' })} #${item.uuid}`}
      </div> */}
        <HomePageSectionHeader
          className={css.sectionHeader}
          subtitle={intl.formatMessage({ id: 'request_detail_page_case' })}
          title={`#${item.uuid}`}
          description={intl.formatMessage({ id: 'request_detail_page_description' })}
        />
        <div className={css.itemInformationSection}>
          <div className={css.itemInformationSectionBackground}>
            {
              new Array(1000)
                .fill(`#${item.uuid}`)
                .map((value, index) => (<span key={`certificate-header-background-${index}`}>{value}</span>))
            }
          </div>
          <div className={css.itemInformationContent}>
            <div className={css.itemInformationImage}>
              {
                item.product_brand && (
                  <div className={css.itemProductBrandCoverImage}>
                    <img src={item.product_brand.icon_image_url} alt={item.product_brand.title} />
                  </div>
                )
              }
              {
                item.product_brand && (
                  <div className={css.itemProductModelCoverImage}>
                    <img src={item.product_model.icon_image_url} alt={item.product_brand.title} />
                  </div>
                )
              }
            </div>
            <div className={css.itemProductCategoryTitle}>
              {getLocalisedField(item.product_category, 'title', locale)}
            </div>
            <div className={css.itemProductBrandTitle}>
              {getLocalisedField(item.product_brand, 'title', locale)} {getLocalisedField(item.product_model, 'title', locale)}
            </div>
            {/* <div className={css.itemProductModelTitle}>
          {getLocalisedField(item.product_model, 'title', locale)}
        </div> */}
            {/* <div className={css.itemSubmitDateGrid}>
          <div className={css.itemSubmitDateTitle}>
            {intl.formatMessage({ id: 'request_detail_page_submitted_at' })}
          </div>
          <div className={css.itemSubmitDate}>
            {moment(item.created_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).local().format('l hh:mm A')}
          </div>
          <div className={css.itemSubmitDateTitle}>
            {intl.formatMessage({ id: 'request_detail_page_service_type' })}
          </div>
          <div className={css.itemSubmitDate}>
            {item.service_level_minute / 60 < 1 ? `${item.service_level_minute} Minutes` : `${item.service_level_minute / 60} Hours`}
          </div>
        </div> */}
            {/* <Divider
              style={{
                background: '#6A7180',
              }}
            >
              <img
                style={{
                  height: '30px',
                }}
                className='app-logo'
                src='/logo-app-icon.svg'
                alt='LEGIT APP'
              />
            </Divider> */}
            <div className={css.itemResultSection}>
              <div className={css.itemResult}>
                {item.result === 'pass' && (
                  <div>
                    {/* <img src='/result-authentic.png' alt='AUTHENTIC' /> */}
                    <div
                      className={css.itemResultTitle}
                      style={{
                        background: 'linear-gradient(121deg, #5977FF 0%, #31FFD7 100%)',
                      }}
                    >
                      {intl.formatMessage({ id: 'app_meta_data_authentication_result_authentic_title' })}
                    </div>
                  </div>
                )}
                {item.result === 'not_pass' && (
                  <div>
                    {/* <img src='/result-replica.png' alt='REPLICA' /> */}
                    <div
                      className={css.itemResultTitle}
                      style={{
                        background: 'linear-gradient(121deg, #FF5F46 0%, #FF1E60 100%)',
                      }}
                    >
                      {intl.formatMessage({ id: 'app_meta_data_authentication_result_replica_title' })}
                    </div>
                  </div>
                )}
                {item.result === 'unable_to_verify' && (
                  <div>
                    {/* <img src='/result-inconclusive.png' alt='INCONCLUSIVE' /> */}
                    <div
                      className={css.itemResultTitle}
                    >
                      {intl.formatMessage({ id: 'app_meta_data_authentication_result_inconclusive_title' })}
                    </div>
                  </div>
                )}
              </div>
              <div className={css.itemQrcode}>
                <QRCode value={`https://legitapp.com/cert/${item.uuid}`} size={100} />
              </div>
              <div className={css.itemCompletedAt}>
                {/* {`#${item.uuid}`} */}
                {/* <br /> */}
                {`${intl.formatMessage({ id: 'request_detail_page_completed_at' })} ${moment(item.completed_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).local().format('YYYY-MM-DD HH:mm')}`}
              </div>
              <Link href='/standards' className={css.standardLink}>
                {intl.formatMessage({ id: 'certificate_page_standard_link' })} <ArrowRightOutlined />
              </Link>
            </div>
          </div>
        </div>
        <div className={css.itemAuthenitcatorCommentSection}>
          {/* <div className={css.sectionTitle}>
          {intl.formatMessage({ id: 'request_detail_page_authenticator_comments' })}
        </div> */}
          <div className={css.itemAuthenitcatorCommentGrid}>
            {(item.service_request_result || []).map((requestResult: any) => (
              <div className={css.itemAuthenitcatorComment} key={`item-service-request-result-${requestResult.id}`}>
                <div className={css.itemAuthenitcatorCommentAuthenticator}>
                  <Link href={`/authenticator/${requestResult.checker_id}`}>
                    <div className={css.itemAuthenitcatorCommentAuthenticatorHeader}>
                      <div
                        className={css.itemAuthenitcatorCommentAuthenticatorAvatar}
                        style={{
                          backgroundImage: `url(${requestResult.checker.profile_image_url})`,
                        }}
                      />
                      <div
                        className={css.itemAuthenitcatorCommentAuthenticatorInformation}
                      >
                        <div className={css.itemAuthenitcatorCommentAuthenticatorTitle}>
                          {[requestResult.checker.name, requestResult.checker.headline.en].filter(title => !!title).join(', ')}
                        </div>
                        <div className={css.itemAuthenitcatorCommentAuthenticatorId}>
                          #{requestResult.checker_id}
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
                <div className={css.itemAuthenitcatorCommentContent}>
                  {requestResult.checker_remark}
                </div>
                <div className={css.itemAuthenitcatorCommentDate}>
                  {moment(requestResult.created_at).locale(locale.replace('Hant', 'hk').replace('Hans', 'cn')).local().format('YYYY-MM-DD HH:mm')}
                </div>
              </div>
            ))}
          </div>
        </div>
        {
          item.result === 'not_pass' && !!item.fake_rating && (
            <div className={css.itemReplicaScoreSection}>
              <div className={css.replicaScoreHeader}>
                <div className={css.botPart}>
                  <div
                    className={css.botAvatar}
                    style={{
                      backgroundImage: `url(/icon-ai-bot.png)`,
                    }}
                  />
                  <div className={css.botTitle}>
                    {intl.formatMessage({ id: `replica_score_bot` })}
                  </div>
                </div>
                <div className={css.replicaScorePart}>
                  <div className={css.replicaScoreHeader}>
                    <div
                      className={css.replicaScoreLabel}
                      style={{
                        backgroundColor: (REPLICA_SCORE_ITEM_MAP as any)[item.fake_rating].color,
                      }}
                    >
                      <div className={css.replicaScore}>{item.fake_rating}</div>
                    </div>
                    <div className={css.replicaScoreTitle}>
                      {intl.formatMessage({ id: (REPLICA_SCORE_ITEM_MAP as any)[item.fake_rating].title })}
                    </div>
                  </div>
                  <div className={css.replicaScoreBar}>
                    <div className={css.replicaScoreBackground}>
                      <div
                        className={css.replicaScoreBarComponent}
                        style={{
                          backgroundColor: REPLICA_1_COLOR,
                        }}
                      />
                      <div
                        className={css.replicaScoreBarComponent}
                        style={{
                          backgroundColor: REPLICA_2_COLOR,
                        }}
                      />
                      <div
                        className={css.replicaScoreBarComponent}
                        style={{
                          backgroundColor: REPLICA_3_COLOR,
                        }}
                      />
                      <div
                        className={css.replicaScoreBarComponent}
                        style={{
                          backgroundColor: REPLICA_4_COLOR,
                        }}
                      />
                      <div
                        className={css.replicaScoreBarComponent}
                        style={{
                          backgroundColor: REPLICA_5_COLOR,
                        }}
                      />
                    </div>
                    <div className={css.replicaArrowGrid}>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 1 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 2 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 3 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 4 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 5 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 6 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 7 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 8 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 9 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                      <div
                        className={css.replicaArrowContainer}
                        style={{
                          display: item.fake_rating === 10 ? `flex` : `none`,
                        }}
                      >
                        <CaretUpOutlined className={css.replicaArrow} />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className={css.replicaScoreDescription}>
                {intl.formatMessage({ id: `replica_score_description` })}
              </div>
            </div>
          )
        }
        <div className={css.itemImageSection}>
          <div className={css.itemRequestImageGrid}>
            <Gallery>
              {
                item.result === 'pass' || item.result === 'not_pass'
                  ? (item.service_request_image || [])
                    .filter((serviceRequestImage: any) => !!serviceRequestImage.marker_image_url)
                    .map((serviceRequestImage: any) => (
                      (serviceRequestImage.marker_image_url || '')
                        .split(',')
                        .filter((imageUrl: any) => !!imageUrl)
                        .map((imageUrl: any) => (
                          <Item
                            key={`item-request-image-${imageUrl}`}
                            original={
                              imageUrl.indexOf('legitapp-prod.oss-cn-hongkong.aliyuncs.com') > 0
                                ? `${getImageUrl(imageUrl)}/CC7V8TY6TvLpyPUmjPdBEDH6X2RToCDpPxHnm7ovt9vN2ysGjkdoUXc9kcBdBrA`
                                : `${getImageUrl(imageUrl)}/v9TdsLkXrciiuJ2ThTYA9fpmeZTo8WUm6e8RjFpFLXZwbxkR9XNAgGL4ya7bRKD`
                            }
                            width="1000"
                            height="1000"
                          >
                            {({ ref, open }) => (
                              <div className={css.itemRequestImage} ref={ref} onClick={open}>
                                <img
                                  src={
                                    imageUrl.indexOf('legitapp-prod.oss-cn-hongkong.aliyuncs.com') > 0
                                      ? `${getImageUrl(imageUrl)}/CC7V8TY6TvLpyPUmjPdBEDH6X2RToCDpPxHnm7ovt9vN2ysGjkdoUXc9kcBdBrA`
                                      : `${getImageUrl(imageUrl)}/v9TdsLkXrciiuJ2ThTYA9fpmeZTo8WUm6e8RjFpFLXZwbxkR9XNAgGL4ya7bRKD`
                                  }
                                />
                              </div>)}
                          </Item>
                        ))
                    ))
                  : null
              }
              {
                (item.service_request_image || []).map((serviceRequestImage: any) => (
                  <Item
                    key={`item-request-image-${serviceRequestImage.id}`}
                    original={
                      serviceRequestImage.image_url.indexOf('legitapp-prod.oss-cn-hongkong.aliyuncs.com') > 0
                        ? `${getImageUrl(serviceRequestImage.image_url)}/CC7V8TY6TvLpyPUmjPdBEDH6X2RToCDpPxHnm7ovt9vN2ysGjkdoUXc9kcBdBrA`
                        : `${getImageUrl(serviceRequestImage.image_url)}/v9TdsLkXrciiuJ2ThTYA9fpmeZTo8WUm6e8RjFpFLXZwbxkR9XNAgGL4ya7bRKD`
                    }
                    width="1000"
                    height="1000"
                  >
                    {({ ref, open }) => (
                      <div className={css.itemRequestImage} ref={ref} onClick={open}>
                        <img
                          src={
                            serviceRequestImage.image_url.indexOf('legitapp-prod.oss-cn-hongkong.aliyuncs.com') > 0
                              ? `${getImageUrl(serviceRequestImage.image_url)}/CC7V8TY6TvLpyPUmjPdBEDH6X2RToCDpPxHnm7ovt9vN2ysGjkdoUXc9kcBdBrA`
                              : `${getImageUrl(serviceRequestImage.image_url)}/v9TdsLkXrciiuJ2ThTYA9fpmeZTo8WUm6e8RjFpFLXZwbxkR9XNAgGL4ya7bRKD`
                          }
                        />
                      </div>)}
                  </Item>
                ))
              }
            </Gallery>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={css.CaseDetailPage}>
      <Head>
        <meta name="robots" content="noindex , nofollow" />
      </Head>
      <AppLayout>
        <AppHeader />
        <AppContainer className={css.pageContainer}>
          {/* {
            !item && isFetchItemLoading && (
              <AppSpin />
            )
          } */}
          {/* {
            fetchItemErrors && fetchItemErrors.length > 0 && (
              <AppPlaceholder
                iconType='exclamation-circle'
                title={getErrorMessage(fetchItemErrors)}
              />
            )
          } */}
          {getServiceRequestItem()}
        </AppContainer>
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const { serviceRequestUUID: itemId } = params;
  if (itemId && itemId !== "undefined" && itemId.length === 16) {
    try {
      const item = await apiCore.get(null, `v2/service_feed/uuid/${itemId}`)
      if (item) {
        const {
          cover_image_url: coverImageUrl,
          result,
          uuid,
          product_title: productTitle = '',
        } = item
        if (result !== 'pass' && result !== 'not_pass') {
          return { props: {} }
        }
        const pageTitle = `#${uuid} ${productTitle} | ${getResultTitle(result).toUpperCase()} | ${getLocaleMessages(locale)?.[`app_title`]}`
        const pageMetaTagItemMap: any = {
          'og-type': {
            property: "og:type",
            content: "article",
          },
          'og-title': {
            property: "og:title",
            content: pageTitle,
          },
        }
        if (coverImageUrl) {
          pageMetaTagItemMap['og-image'] = {
            property: 'og:image',
            content: resizeImageUrl(getImageUrl(coverImageUrl), { width: 600 }),
          }
        }
        return {
          props: {
            pageTitle,
            pageMetaTagItemMap,
            item,
          },
        };
      }
    } catch (error) {
      console.log(`error for v1/article/slug/${itemId}`, error);
    }
  }
  return {
    notFound: true,
  }
}

export async function getStaticPaths() {
  return { paths: [], fallback: "blocking" };
}

export default ServiceRequestDetailPage

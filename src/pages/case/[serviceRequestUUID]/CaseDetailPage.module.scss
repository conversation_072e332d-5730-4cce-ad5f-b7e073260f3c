.CaseDetailPage {
  background: #000;
  min-height: 100vh;
  -webkit-user-select: none;
  user-select: none;

  .pageContainer {
    // padding: 48px 0;
    max-width: 800px;
    padding-top: 48px;
    padding-bottom: 48px;
    min-height: 100vh;

    @include responsive('md') {
      padding-top: 48px;
      padding-bottom: 48px;
    }

    .sectionHeader {
      margin-bottom: 24px;
    }

    .itemQrcode {
      width: fit-content;
      text-align: center;
      padding: 12px;
      // padding-bottom: 0;
      border-radius: 5px;
      background-color: #fff;
      margin: 24px 0;
    }

    .itemInformationSection {
      border: 1px solid $color-separator-white-1;
      color: $color-app-white;
      margin-bottom: 24px;
      border-radius: 3px;
      background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
      border-radius: $border-radius-theme-2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 24px;
      padding-top: 12px;
      text-align: center;
      background-repeat: repeat;
      background-position: center;
      background-size: auto;
      position: relative;
      overflow: hidden;
      // background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='598' height='245' viewBox='0 0 598 245' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3ctext x='30' y='40' fill='rgba(255,255,255,0.2)'%3eTEXT GOES HERE%3c/text%3e%3c/svg%3e ");

      @include responsive('md') {
        padding: 40px;
        padding-top: 24px;
      }

      .itemInformationSectionBackground {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.03;
        display: flex;
        font-weight: bold;
        row-gap: 6px;
        column-gap: 24px;
        max-width: 800px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        pointer-events: none;
      }

      .itemInformationContent {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .itemInformationImage {
        display: flex;
        margin-bottom: 24px;

        .itemProductBrandCoverImage {
          img {
            width: 100px;
            height: 100px;
            -webkit-user-drag: none;
          }
        }

        .itemProductModelCoverImage {
          img {
            width: 100px;
            height: 100px;
            -webkit-user-drag: none;
          }
        }
      }

      .itemProductCategoryTitle {
        font-size: 16px;
        opacity: 0.6;
        // @include responsive('md') {
        //   font-size: 18px;
        // }
      }

      .itemProductBrandTitle {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 24px;
      }

      .itemProductModelTitle {
        font-size: 14px;
        font-weight: bold;

        @include responsive('md') {
          font-size: 18px;
        }
      }

      .itemSubmitDateTitle {
        color: #6a7180;
        font-size: 14px;

        // @include responsive('md') {
        //   font-size: 18px;
        // }
      }

      .itemSubmitDate {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 12px;

        @include responsive('md') {
          font-size: 18px;
        }
      }
    }

    .serviceRequesItem {
      margin-bottom: 36px;

      // @include responsive('md') {
      //   max-width: 500px;
      // }

      .itemTitle {
        color: #fff;
        text-transform: uppercase;
        font-size: 20px;
        line-height: 28px;
        font-weight: 900;
        margin-bottom: 24px;
        text-align: center;
        // margin-right: 16px;
        // margin-left: 16px;

        @include responsive('md') {
          margin-right: unset;
          margin-left: unset;
          font-size: 35px;
          line-height: 40px;
        }
      }

      .sectionTitle {
        color: #fff;
        // text-transform: uppercase;
        // opacity: 0.7;
        margin-bottom: 12px;
        font-size: 18px;

        @include responsive('md') {
          font-size: 20px;
        }
      }

      .itemResultSection {
        color: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        // margin-bottom: 48px;

        .itemResult {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;

          img {
            width: 100px;
            height: 100px;
          }

          .itemResultTitle {
            color: #fff;
            text-transform: uppercase;
            font-size: 20px;
            line-height: 20px;
            font-weight: 900;
            padding: 6px 12px;
            border-radius: 3px;

            @include responsive('md') {
              margin-right: unset;
              margin-left: unset;
              // font-size: 24px;
            }
          }
        }

        .standardLink {
          color: $color-app-white;
          font-size: 12px;
          height: 30px;
          border: 1px solid $color-app-white;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 12px;
          border-radius: 15px;
          column-gap: 6px;
          opacity: 0.6;
          transition: all 0.25s ease-in;

          &:hover {
            opacity: 1;
          }
        }

        .itemCompletedAt {
          font-size: 14px;
          opacity: 0.6;
          line-height: 24px;
          text-align: center;
          margin-bottom: 12px;
        }
      }

      .itemReplicaScoreSection {
        color: $color-app-white;
        border: 1px solid $color-separator-white-2;
        background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
        border-radius: $border-radius-theme-1;
        margin-bottom: 24px;
        padding: 24px;
        display: flex;
        flex-direction: column;
        row-gap: 12px;

        .replicaScoreHeader {
          color: $color-app-white;
          display: flex;
          // flex-direction: column;
          align-items: center;
          column-gap: 24px;

          .botPart {
            display: flex;
            flex-direction: column;
            align-items: center;
            row-gap: 6px;
            column-gap: 12px;
            flex-shrink: 0;

            .botAvatar {
              width: 50px;
              height: 50px;
              background-repeat: no-repeat;
              background-position: center;
              background-size: cover;
            }

            .botTitle {
              font-size: 14px;
              font-weight: bold;
            }
          }

          .replicaScorePart {
            display: flex;
            flex-direction: column;
            row-gap: 12px;
            width: 100%;

            .replicaScoreHeader {
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
              column-gap: 12px;

              .replicaScoreLabel {
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: $border-radius-theme-1;

                .replicaScore {
                  font-weight: bold;
                  font-size: 20px;
                }
              }

              .replicaScoreTitle {
                font-weight: bold;
                font-size: 20px;
              }
            }

            .replicaScoreBar {
              width: 100%;
              position: relative;

              .replicaScoreBackground {
                height: 14px;
                width: 100%;
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                border-radius: 7px;
                overflow: hidden;

                .replicaScoreBarComponent {
                  height: 100%;
                }
              }

              .replicaArrowGrid {
                position: relative;
                display: grid;
                grid-template-columns: repeat(10, 1fr);

                .replicaArrowContainer {
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  .replicaArrow {
                    // width: 10px;
                    // height: 10px;
                    font-weight: 20px;
                  }
                }
              }
            }
          }
        }

        .replicaScoreDescription {
          opacity: 0.6;
          font-size: 12px;
          white-space: pre-line;
        }
      }

      .itemAuthenitcatorCommentSection {
        color: #fff;
        // border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        margin-bottom: 24px;

        .sectionTitle {
          padding: 12px 0;
          width: 100%;
          color: #fff;
          font-size: 16px;
          font-weight: bold;
          text-transform: uppercase;

          @include responsive('md') {
            font-size: 14px;
          }
        }

        .itemAuthenitcatorCommentGrid {
          display: grid;
          row-gap: 24px;
          column-gap: 24px;
          grid-template-columns: repeat(1, 1fr);

          @include responsive('md') {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        .itemAuthenitcatorComment {
          color: $color-app-white;
          border: 1px solid $color-separator-white-2;
          // background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
          border-radius: $border-radius-theme-1;
          padding: 24px;
          display: flex;
          flex-direction: column;
          row-gap: 12px;

          .itemAuthenitcatorCommentAuthenticator {
            display: flex;
            flex-direction: row;
            align-items: center;
            font-weight: bold;

            .itemAuthenitcatorCommentAuthenticatorHeader {
              display: flex;
              flex-direction: row;
              column-gap: 12px;
              align-items: center;
              cursor: pointer;
            }

            .itemAuthenitcatorCommentAuthenticatorAvatar {
              width: 30px;
              height: 30px;
              background-repeat: no-repeat;
              background-size: cover;
              background-position: center;
              border-radius: 50%;
              overflow: hidden;
            }

            .itemAuthenitcatorCommentAuthenticatorInformation {
              display: flex;
              flex-direction: column;
              row-gap: 6px;

              .itemAuthenitcatorCommentAuthenticatorTitle {
                font-size: 14px;
                line-height: 16px;
              }

              .itemAuthenitcatorCommentAuthenticatorId {
                font-size: 12px;
                line-height: 16px;
                opacity: 0.6;
              }
            }
          }

          .itemAuthenitcatorCommentContent {
            white-space: pre-line;
            font-weight: bold;
            font-size: 14px;
          }

          .itemAuthenitcatorCommentDate {
            margin-bottom: 6px;
            font-size: 12px;
            opacity: 0.6;
          }
        }
      }

      .itemImageSection {
        .itemRequestImageGrid {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          row-gap: 24px;
          column-gap: 24px;

          @include responsive('md') {
            grid-template-columns: repeat(4, 1fr);
          }
        }

        .itemRequestImage {
          border-radius: 3px;
          overflow: hidden;
          cursor: pointer;

          img {
            border-radius: 3px;
            overflow: hidden;
            width: 100%;
            height: 100%;
            pointer-events: none;
            object-fit: cover;
            aspect-ratio: 1;
          }
        }
      }

      .item-social {
        display: flex;
        align-items: center;
        margin: 12px 0;
        flex-wrap: wrap;
        margin-right: 16px;
        margin-left: 16px;

        @include responsive('md') {
          margin-bottom: 24px;
          margin-right: unset;
          margin-left: unset;
        }

        .share-social-title {
          color: #444444;
          display: none;

          @include responsive('md') {
            display: block;
          }
        }

        .ant-divider {
          height: 32px;
          display: none;
          margin: 0 12px;

          @include responsive('md') {
            display: block;
          }
        }

        .react-share__ShareButton {
          flex-shrink: 0;
          margin-right: 12px;
          border-radius: 3px;
          overflow: hidden;
          // margin-bottom: 12px;
          display: flex;
          align-items: center;
          cursor: pointer;

          @include responsive('md') {
            margin-bottom: unset;
          }

          &.share-facebook-button {
            // background-color: #3b5998;
          }

          &.share-twitter-button {
            // background-color: #00aced;
          }

          &.share-linkedin-button {
            // background-color: #007fb1;
          }

          .share-title {
            margin: 0 12px 0 0;
            color: #fff;
            font-weight: 900;
          }
        }
      }
    }
  }

  :global {

    .container {
      padding-top: 48px;
      padding-bottom: 48px;
    }
  }
}

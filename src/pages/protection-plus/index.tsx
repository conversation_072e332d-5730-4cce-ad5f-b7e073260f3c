import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import ProtectionPlusContent from 'components/ProtectionPlusContent'
import { useIntl } from 'react-intl'
import { getLocaleMessages } from 'utils/locale'
import css from './ProtectionPlusPage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

const ProtectionPlusPage = ({ pageTitle }: any) => {
  const intl = useIntl()
  return (
    <div className={css.ProtectionPlusPage}>
      <AppPageHiddenH1
        title={pageTitle}
      />
      <AppLayout>
        <AppHeader />
        <div className={css.contentContainer}>
          <ProtectionPlusContent />
        </div>
        <DownloadSection />
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const pageTitle = `${getLocaleMessages(locale)?.[`protection_plus_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
  const pageDescription = getLocaleMessages(locale)?.[`protection_plus_page_item_1_answer`]
  const pageMetaTagItemMap: any = {
    'og-type': {
      property: "og:type",
      content: "article",
    },
    'og-title': {
      property: "og:title",
      content: pageTitle,
    },
    'og-description': {
      property: 'og:description',
      content: pageDescription,
    },
  }
  return {
    props: {
      pageTitle,
      pageMetaTagItemMap,
    }
  }
}

export default ProtectionPlusPage

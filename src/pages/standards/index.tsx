import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import { useIntl } from 'react-intl'
import { getLocaleMessages } from 'utils/locale'
import css from './StandardsPage.module.scss'
import StandardsContent from 'components/StandardsContent'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

const StandardsPage = ({ pageTitle }: any) => {

    const intl = useIntl()
    return (
        <div className={css.StandardsPage}>
            <AppPageHiddenH1
                title={pageTitle}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.contentContainer}>
                    <StandardsContent />
                </div>
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`standards_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
    const pageMetaTagItemMap: any = {
        'og-type': {
            property: "og:type",
            content: "article",
        },
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        }
    }
}


export default StandardsPage


import React, { useEffect, useState } from "react";
import Image from "next/image";
import Head from "next/head";
import { useRouter } from "next/router";
import { RootState } from "reducers";

import Header from "components/Login/Header";
import useAppSelector from "hooks/useAppSelector";
import { homePath } from "components/StartAuthentication/constant";

export default function RegisterSuccess() {
  const router = useRouter();
  const appState: any = ({} = useAppSelector((state: RootState) => state.app));
  const [countdown, setCountdown] = useState(5);

  const { loginRedirectPath = homePath } = appState || {};

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prevCount) => {
        if (prevCount <= 1) {
          clearInterval(timer);
          router.push(loginRedirectPath);
          return 1;
        }
        return prevCount - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [loginRedirectPath, router]);

  return (
    <div className="h-screen bg-black text-white flex flex-col items-center">
      <Head>
        <title>REGISTER SUCCESS | LEGIT APP</title>
      </Head>

      {/* Header */}
      <Header />

      {/* Content */}
      <div className="w-full max-w-xl flex flex-col items-center mt-[65px] sm:px-0 px-6">
        <div className="sm:mb-10 mb-8">
          <Image
            src="/login/email-confirmed.svg"
            alt="Email"
            width={160}
            height={160}
            className="object-contain sm:w-40 sm:h-40 w-32 h-32"
          />
        </div>

        <h1 className="sm:text-[52px] leading-none text-3xl font-[900] text-center sm:mb-10 mb-8">
          YOU&apos;RE REGISTERED WITH LEGIT APP
        </h1>

        <div className="mb-6 text-base sm:w-[80%]">
          You can use these details to log back into Wise. We will redirect you
          automatically in <span className="font-bold">{countdown}</span>{" "}
          seconds.
        </div>

        <button
          className="w-full bg-btn-gradient font-bold px-4 py-3 rounded-full"
          onClick={() => {
            router.push(loginRedirectPath);
          }}
        >
          Continue
        </button>
      </div>
    </div>
  );
}

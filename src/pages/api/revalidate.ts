// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { APP_DEFAULT_LOCALE, APP_LOCALES } from 'constants/app'
import _ from 'lodash'
import type { NextApiRequest, NextApiResponse } from 'next'
import getConfig from 'next/config'
const { publicRuntimeConfig } = getConfig()
const { API_REVALIDATION_TOKEN } = publicRuntimeConfig

type ResponseData = {
	message?: string;
	revalidated?: boolean;
} | string

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<ResponseData>,
) {
	if (req.query.secret !== API_REVALIDATION_TOKEN) {
		return res.status(401).json({ message: 'Invalid token' })
	}

	try {
		let path = _.get(req, 'query.path')
		APP_LOCALES.forEach((locale: any) => {
			path = (path as any).replace(`/${locale}/`, '')
		});

		const regex = /^\/+/
		const cleanPath = (path as any).replace(regex, '')
		await Promise.all(APP_LOCALES.map((locale: any) => {
			const localePath = locale === APP_DEFAULT_LOCALE ? `/` : `/${locale}/`
			const finalPath = `${localePath}${cleanPath}`
			return res.revalidate(finalPath as string)
		}))
		// await res.revalidate(cleanPath as string)
		// const canonicalUrl = (locale === defaultLocale ? '' : `/${locale}`) + (asPath === '/' ? '' : cleanPath);
		return res.json({ revalidated: true })
	} catch (error) {
		return res.status(500).send(`Error revalidating: ${JSON.stringify(error)}`)
	}
}

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/router";
import bowser from "bowser";
import { RootState } from "reducers";
import clsx from "clsx";

import { PATH_ROUTE } from "constants/app";
import Header from "components/Login/Header";
import Password from "components/Login/Password";
import apiCore from "utils/apiCore";
import { userSignIn, clearLoginRedirectPath, fetchUser } from "actions/app";
import { parseError } from "utils/error";
import { showSuccessPopupMessage, showErrorPopupMessage } from "utils/message";
import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import { homePath } from "components/StartAuthentication/constant";

export default function Login() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const appState: any = ({} = useAppSelector((state: RootState) => state.app));

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { loginRedirectPath } = appState || {};

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (email && password) {
      const browserInfo = bowser
        .getParser(window.navigator.userAgent)
        .getBrowser();
      const loginParams = {
        email,
        password,
        app: `client`,
        app_version: `0.0.0`,
        device: `web`,
        device_version: `${browserInfo.name} ${browserInfo.version}`,
      };
      try {
        setIsSubmitting(true);
        const loginResult = await apiCore.post(
          null,
          `v1/authentication`,
          loginParams
        );

        const { token } = loginResult;

        dispatch(userSignIn({ accessToken: token }));
        dispatch(fetchUser({ accessToken: token }));

        setIsSubmitting(false);
        showSuccessPopupMessage(`You have successfully logged in.`);

        const nextPathname = loginRedirectPath || homePath;
        router.push(nextPathname);
        dispatch(clearLoginRedirectPath());
      } catch (error) {
        setIsSubmitting(false);
        showErrorPopupMessage(parseError(error).message);
      }
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
  };

  return (
    <div className="h-screen bg-black text-white flex flex-col items-center">
      {/* Header */}
      <Header />

      {/* Content */}
      <div className="w-full max-w-[526px] sm:px-0 px-6 flex flex-col items-center justify-center sm:mt-24 mt-16">
        <h1 className="sm:text-2xl text-xl font-bold mb-4">Welcome back.</h1>

        <div className="sm:mb-10 mb-8">
          <span className="text-sm">New to LEGIT APP? </span>
          <Link href={PATH_ROUTE.REGISTER} className="underline font-semibold">
            Sign up
          </Link>
        </div>

        <form onSubmit={handleSubmit} className="w-full">
          <div>
            <label htmlFor="email" className="mb-1 text-sm">
              Your email address
            </label>
            <input
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="outline-none w-full bg-transparent border border-gray-600 rounded-lg px-4 py-2 mb-4"
              required
              placeholder="Email"
            />
          </div>
          <Password
            password={password}
            handlePasswordChange={handlePasswordChange}
          />

          <button
            type="submit"
            className={clsx(
              "w-full bg-btn-gradient hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-full mb-8",
              {
                "cursor-not-allowed opacity-50": isSubmitting,
              }
            )}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Logging in..." : "Log in"}
          </button>
        </form>

        {/* Trouble logging in */}
        <Link
          href={PATH_ROUTE.TROUBLE_LOGIN}
          className="text-sm font-bold mb-6 w-full underline"
        >
          Trouble logging in?
        </Link>

        <div className="mb-8 w-full">
          <p className="mb-4 text-sm">Or log in with</p>
          <div className="flex justify-between gap-4">
            <button className="flex-1 border border-gray-300 rounded-full py-2 flex justify-center items-center">
              <Image
                src="/login/google-icon.svg"
                alt="Google"
                width={24}
                height={24}
              />
            </button>
            <button className="flex-1 border border-gray-300 rounded-full py-2 flex justify-center items-center">
              <Image
                src="/login/facebook-icon.svg"
                alt="Facebook"
                width={24}
                height={24}
              />
            </button>
            <button className="flex-1 border border-gray-300 rounded-full py-2 flex justify-center items-center">
              <Image
                src="/login/apple-icon.svg"
                alt="Apple"
                width={24}
                height={24}
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

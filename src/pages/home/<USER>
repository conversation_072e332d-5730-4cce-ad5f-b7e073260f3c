import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'
import BannerSection from 'components/HomePageSection/BannerSection'
import BenefitSection from 'components/HomePageSection/BenefitSection'
import BrandSection from 'components/HomePageSection/BrandSection'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import MediaSection from 'components/HomePageSection/MediaSection'
import PricingSection from 'components/HomePageSection/PricingSection'
import ProductCategorySection from 'components/HomePageSection/ProductCategorySection'
import ResourceSection from 'components/HomePageSection/ResourceSection'
import ReviewSection from 'components/HomePageSection/ReviewSection'
import UseCaseSection from 'components/HomePageSection/UseCaseSection'
import VideoSection from 'components/HomePageSection/VideoSection'
import WorkFlowSection from 'components/HomePageSection/WorkFlowSection'
import { APP_NAME, APP_OG_IMAGE_URL, APP_URL, ZENDESK_KEY, ZENDESK_SETTINGS } from 'constants/app'
import SCHEMA_POTENTIAL_ACTION from 'constants/schema/protentialActions'
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import Script from 'next/script'
import { useIntl } from 'react-intl'
import Zendesk from 'react-zendesk'
import { Organization, WithContext } from 'schema-dts'
import css from './HomePage.module.scss'

const Home = () => {
    const intl = useIntl()
    const canonicalUrl = useCanonicalUrl()

    const pageSchema: WithContext<Organization> = {
        "@context": "https://schema.org",
        "@type": "Organization",
        url: canonicalUrl,
        name: intl.formatMessage({ id: 'app_title' }),
        description: intl.formatMessage({ id: 'app_og_description' }),
        "sameAs": [
            "https://x.com/legitappcom",
            "https://www.instagram.com/legitappcom/",
            "https://www.linkedin.com/company/legitapp/"
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "Customer Service",
            "email": "<EMAIL>",
            "url": "https://www.legitapp.com/contact"
        },
        "founder": {
            "@type": "Person",
            "name": "KaKit Chen"
        },
        "foundingDate": "2020",
        image: APP_OG_IMAGE_URL,
        // aggregateRating: {
        //     "@type": "AggregateRating",
        //     "ratingValue": "4.9",
        //     "reviewCount": "20000"
        // },
        // review: [
        //     ...SCHEMA_PRODUCT_REVIEWS,
        // ],
        potentialAction: SCHEMA_POTENTIAL_ACTION,
    };

    return (
        <div className={css.HomePage}>
            <Script
                id='home-page-schema'
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(pageSchema),
                }}
            />
            <AppPageHiddenH1
                title={`${intl.formatMessage({ id: 'home_page_hero_section_title_1' })} ${intl.formatMessage({ id: 'home_page_hero_section_title_2' })} ${intl.formatMessage({ id: 'home_page_hero_section_subtitle' })}`}
            />
            <AppLayout>
                <AppHeader />
                <BannerSection />
                <MediaSection />
                <VideoSection />
                <ProductCategorySection />
                <BenefitSection />
                <WorkFlowSection />
                <PricingSection />
                <UseCaseSection />
                <BrandSection />
                <ReviewSection />
                <ResourceSection />
                <DownloadSection />
                <AppFooter />
            </AppLayout>
            <Zendesk
                defer
                zendeskKey={ZENDESK_KEY}
                {...ZENDESK_SETTINGS}
            // onLoaded={() => { }}
            />
        </div>
    )
}

export default Home

import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import LegitTokenContent from 'components/LegitTokenContent'
import { useIntl } from 'react-intl'
import { getLocaleMessages } from 'utils/locale'
import css from './LegitTokenPage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

const LegitTokenPage = ({ pageTitle }: any) => {
  const intl = useIntl()

  return (
    <div className={css.LegitTokenPage}>
      <AppPageHiddenH1
        title={pageTitle}
      />
      <AppLayout>
        <AppHeader />
        <div className={css.contentContainer}>
          <LegitTokenContent />
        </div>
        <DownloadSection />
        <AppFooter />
      </AppLayout>
    </div>
  )
}

export async function getStaticProps({
  params,
  locale,
}: any) {
  const pageTitle = `${getLocaleMessages(locale)?.[`legit_token_page_title`]} | ${getLocaleMessages(locale)?.[`app_title`]}`
  const pageDescription = getLocaleMessages(locale)?.[`legit_token_page_faq_section_item_1_answer`]
  const pageMetaTagItemMap: any = {
    'og-type': {
      property: "og:type",
      content: "article",
    },
    'og-title': {
      property: "og:title",
      content: pageTitle,
    },
    'og-description': {
      property: 'og:description',
      content: pageDescription,
    },
  }
  return {
    props: {
      pageTitle,
      pageMetaTagItemMap,
    }
  }
}

export default LegitTokenPage

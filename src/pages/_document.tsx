import { createCache, extractStyle, StyleProvider } from '@ant-design/cssinjs';
import type { DocumentContext } from 'next/document';
import Document, { Head, Html, Main, NextScript } from 'next/document';
import Script from 'next/script';
import * as gtag from 'utils/gtag';

const MyDocument = () => (
  <Html lang="en">
    <Head>
      <link rel='apple-touch-icon' sizes='57x57' href='/favicon/apple-icon-57x57.png' />
      <link rel='apple-touch-icon' sizes='60x60' href='/favicon/apple-icon-60x60.png' />
      <link rel='apple-touch-icon' sizes='72x72' href='/favicon/apple-icon-72x72.png' />
      <link rel='apple-touch-icon' sizes='76x76' href='/favicon/apple-icon-76x76.png' />
      <link rel='apple-touch-icon' sizes='114x114' href='/favicon/apple-icon-114x114.png' />
      <link rel='apple-touch-icon' sizes='120x120' href='/favicon/apple-icon-120x120.png' />
      <link rel='apple-touch-icon' sizes='144x144' href='/favicon/apple-icon-144x144.png' />
      <link rel='apple-touch-icon' sizes='152x152' href='/favicon/apple-icon-152x152.png' />
      <link rel='apple-touch-icon' sizes='180x180' href='/favicon/apple-icon-180x180.png' />
      <link rel='icon' type='image/png' sizes='192x192' href='/favicon/android-icon-192x192.png' />
      <link rel='icon' type='image/png' sizes='32x32' href='/favicon/favicon-32x32.png' />
      <link rel='icon' type='image/png' sizes='96x96' href='/favicon/favicon-96x96.png' />
      <link rel='icon' type='image/png' sizes='16x16' href='/favicon/favicon-16x16.png' />
      <link rel='manifest' href='/favicon/manifest.json' />
      <link href='https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700;800&display=swap' rel='stylesheet' />

      <meta name='msapplication-TileColor' content='#ffffff' />
      <meta name='msapplication-TileImage' content='/ms-icon-144x144.png' />
      <meta name='theme-color' content='#000000' />
      <meta key='content-type' httpEquiv='Content-Type' content='text/html; charset=utf-8' />
      <meta content='yes' name='apple-mobile-web-app-capable' />
      <meta content='yes' name='mobile-web-app-capable' />
      <meta content='black' name='apple-mobile-web-app-status-bar-style' />
      <meta content='LEGIT APP Authentication' name='apple-mobile-web-app-title' />
      {/* Ahrefs Web Analytics */}
      <script src="https://analytics.ahrefs.com/analytics.js" data-key="ZXfRshcyhI09VROPZ+Mpvw" async></script>
      {/* Global Site Tag (gtag.js) - Google Analytics */}
      <Script
        id="gtag-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${gtag.GA_TRACKING_ID}');
          `,
        }}
      />
    </Head>
    <body>
      <Main />
      <NextScript />
    </body>
  </Html>
);

MyDocument.getInitialProps = async (ctx: DocumentContext) => {
  const cache = createCache();
  const originalRenderPage = ctx.renderPage;
  ctx.renderPage = () =>
    originalRenderPage({
      enhanceApp: (App) => (props) => (
        <StyleProvider cache={cache}>
          <App {...props} />
        </StyleProvider>
      ),
    });

  const initialProps = await Document.getInitialProps(ctx);
  const style = extractStyle(cache, true);
  return {
    ...initialProps,
    styles: (
      <>
        {initialProps.styles}
        <style dangerouslySetInnerHTML={{ __html: style }} />
      </>
    ),
  };
};

export default MyDocument;
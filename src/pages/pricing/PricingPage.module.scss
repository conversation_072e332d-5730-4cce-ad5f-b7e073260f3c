.PricingPage {
    .pricingSection {
        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .solutionCardGrid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            margin-bottom: 24px;
            width: 100%;

            @include responsive('md') {
                grid-template-columns: repeat(4, 1fr);
            }

            .solutionCard {
                min-height: 300px;
                padding: 24px;
                padding-top: 0px;
                color: $color-app-white;
                row-gap: 6px;
                border: 1px solid $color-separator-white-2;
                color: $color-app-white;
                border-radius: $border-radius-theme-2;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                align-items: center;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

                @include responsive('md') {
                    padding-top: 24px;
                }

                .iconImage {
                    width: 100%;
                    max-width: 140px;
                }

                .title {
                    font-weight: bold;
                    width: 100%;
                    text-align: center;
                    font-size: 16px;
                    line-height: 26px;
                    max-width: 200px;

                    @include responsive('md') {
                        font-size: 20px;
                        line-height: 30px;
                    }
                }

                .subtitle {
                    opacity: 0.6;
                    margin-bottom: 12px;
                    width: 100%;
                    text-align: center;
                    font-size: 14px;
                    line-height: 24px;

                    @include responsive('md') {
                        font-size: 16px;
                        line-height: 26px;
                    }
                }

                .highlights {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    row-gap: 0px;
                    margin-bottom: 24px;

                    .tagline {
                        font-size: 16px;
                        opacity: 0.6;

                        @include responsive('md') {
                            font-size: 16px;
                        }
                    }

                    .price {
                        font-size: 20px;
                        line-height: 30px;
                        font-weight: bold;

                        @include responsive('md') {
                            line-height: 40px;
                            font-size: 30px;
                        }

                    }
                }

                .coverImage {
                    min-height: 200px;
                }

                .footer {
                    width: 100%;
                    display: grid;
                    grid-template-columns: repeat(1, 1fr);
                    row-gap: 12px;
                    column-gap: 24px;
                }

                .pricingTable {
                    display: flex;
                    flex-direction: column;
                    row-gap: 6px;
                    margin-bottom: 12px;
                    height: 100%;

                    .tableTitle {
                        opacity: 0.6;
                        font-size: 14px;
                        margin-bottom: 6px;
                    }

                    .pricingItem {
                        display: flex;
                        align-items: center;
                        column-gap: 12px;
                        font-size: 14px;
                        justify-content: space-between;

                        .price {
                            font-weight: bold;
                        }

                        .turnaroundTime {}
                    }
                }
            }
        }

        .serviceCard {
            width: 100%;
            padding: 24px;
            color: $color-app-white;
            row-gap: 6px;
            border: 1px solid $color-separator-white-2;
            color: $color-app-white;
            border-radius: $border-radius-theme-2;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

            @include responsive('md') {
                padding: 40px;
            }


            .serviceMessage {
                font-weight: bold;
                width: 100%;
                text-align: center;
                font-size: 16px;
                line-height: 26px;
                margin-bottom: 24px;

                @include responsive('md') {
                    font-size: 20px;
                    line-height: 30px;
                    margin-bottom: 48px;
                }
            }

            .serviceItemGrid {
                display: grid;
                flex-direction: column;
                row-gap: 12px;
                column-gap: 12px;
                margin-bottom: 12px;
                grid-template-columns: repeat(1, 1fr);

                @include responsive('md') {
                    row-gap: 24px;
                    column-gap: 24px;
                    grid-template-columns: repeat(5, 1fr);
                }

                .serviceItem {
                    display: flex;
                    row-gap: 12px;
                    column-gap: 12px;
                    align-items: flex-start;

                    font-size: 14px;
                    line-height: 20px;
                    color: rgba(255, 255, 255, 0.8);

                    @include responsive('md') {
                        align-items: center;
                    }

                    .checkIcon {
                        margin-right: 3px;
                        width: 16px;
                        margin-top: 2px;

                        @include responsive('md') {
                            width: 16px;
                        }
                    }
                }
            }
        }
    }

    .generalTokenPlanSection {
        padding: 48px 0;

        @include responsive('md') {
            padding: 96px 0;
        }

        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .tokenPlanCardGrid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            margin-bottom: 48px;
            width: 100%;

            @include responsive('md') {
                grid-template-columns: repeat(4, 1fr);
            }

            .tokenPlanCard {
                padding: 24px;
                color: $color-app-white;
                border: 1px solid $color-separator-white-2;
                color: $color-app-white;
                border-radius: $border-radius-theme-2;
                overflow: hidden;
                display: flex;
                align-items: center;
                column-gap: 10px;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);
                background-repeat: no-repeat;
                background-size: 105%;
                background-position: center;
                position: relative;

                &.popularForSneaker {
                    background-image: url(https://legitapp-static.oss-accelerate.aliyuncs.com/background-pricing-popular-1.png);
                }

                &.popularForLuxury {
                    background-image: url(https://legitapp-static.oss-accelerate.aliyuncs.com/background-pricing-popular-2.png);
                }

                @include responsive('md') {
                    padding: 40px;
                    column-gap: 16px;
                }

                .tagLabel {
                    position: absolute;
                    top: -3px;
                    left: -3px;
                    padding: 7px 16px 4px 19px;
                    font-size: 10px;
                    display: flex;
                    align-items: center;
                    column-gap: 3px;
                    font-weight: bold;
                    border-radius: 4px;

                    @include responsive('md') {
                        font-size: 12px;
                        padding: 7px 24px 4px 27px;
                    }

                    &.popularForSneaker {
                        background-image: linear-gradient(90deg, #009BFF 60%, #3BFFFF);
                    }

                    &.popularForLuxury {
                        background-image: linear-gradient(90deg, #715D48 60%, #BAAB8D);
                    }

                    img {
                        width: 10px;
                    }
                }

                .tokenIcon {
                    width: 20px;
                    height: 20px;
                    position: relative;
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-image: url(/icon-token-new.png);

                    @include responsive('md') {
                        width: 34px;
                        height: 34px;
                    }
                }

                .planInformation {
                    margin-top: 6px;
                    row-gap: 3px;
                    display: flex;
                    flex-direction: column;
                    position: relative;

                    @include responsive('md') {
                        margin-top: unset;
                        row-gap: 10px;
                    }

                    .planToken {
                        font-weight: bold;
                        font-size: 14px;
                        line-height: 14px;

                        @include responsive('md') {
                            font-size: 20px;
                            line-height: 20px;
                        }
                    }

                    .planPrice {
                        font-weight: bold;
                        font-size: 12px;
                        line-height: 12px;

                        @include responsive('md') {
                            font-size: 16px;
                            line-height: 16px;
                        }
                    }
                }
            }
        }
    }

    .businessPlanSection {
        padding: 48px 0;
        margin-bottom: 48px;

        @include responsive('md') {
            padding: 96px 0;
            margin-bottom: 96px;
        }

        .sectionContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .sectionHeader {
            padding: 48px 0 36px 0;

            @include responsive('md') {
                padding: 96px 0 60px 0;
            }
        }

        .planCardGrid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            column-gap: 24px;
            row-gap: 24px;
            margin-bottom: 48px;
            width: 100%;

            @include responsive('md') {
                grid-template-columns: repeat(3, 1fr);
            }

            .planCard {
                min-height: 300px;
                // padding: 24px;
                // padding-top: 12px;
                color: $color-app-white;
                row-gap: 6px;
                border: 1px solid $color-separator-white-2;
                color: $color-app-white;
                border-radius: $border-radius-theme-2;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                background-image: linear-gradient(345deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 100%);

                .planCoverImage {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow: hidden;

                    img {
                        width: 105%;
                    }
                }

                .planContent {
                    padding: 24px;
                }

                .title {
                    font-weight: bold;
                    font-size: 16px;
                    width: 100%;
                    margin-bottom: 6px;
                }

                .subtitle {
                    font-size: 14px;
                    opacity: 0.6;
                    margin-bottom: 12px;
                    width: 100%;
                }

                .highlights {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    row-gap: 0px;
                    margin-bottom: 24px;

                    .tagline {
                        font-size: 16px;
                        opacity: 0.6;

                        @include responsive('md') {
                            font-size: 16px;
                        }
                    }

                    .price {
                        display: flex;
                        flex-direction: column;

                        .currentPrice {
                            font-size: 20px;
                            line-height: 30px;
                            font-weight: bold;

                            @include responsive('md') {
                                line-height: 40px;
                                font-size: 30px;
                            }
                        }

                        .originalPrice {
                            text-decoration: line-through;
                            font-size: 20px;
                            line-height: 30px;
                        }

                    }
                }

                .coverImage {
                    min-height: 200px;
                }

                .footer {
                    width: 100%;
                    display: grid;
                    grid-template-columns: repeat(1, 1fr);
                    // row-gap: 12px;
                    column-gap: 24px;

                    a {
                        background: linear-gradient(270deg,
                                rgb(89, 119, 255) 10%,
                                rgb(49, 255, 215) 101.39%);
                        background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }
                }

                .pricingTable {
                    display: flex;
                    flex-direction: column;
                    row-gap: 12px;
                    height: 100%;

                    @include responsive('md') {
                        margin-bottom: 12px;
                    }

                    .tableTitle {
                        opacity: 0.6;
                        font-size: 14px;
                        // margin-bottom: 6px;
                    }

                    .pricingItem {
                        display: flex;
                        align-items: center;
                        column-gap: 12px;
                        font-size: 14px;
                        justify-content: space-between;

                        .price {
                            flex-shrink: 0;
                            display: flex;
                            flex-direction: column;
                            align-items: flex-end;

                            .currentPrice {
                                font-size: 14px;
                                line-height: 20px;
                                font-weight: bold;

                                @include responsive('md') {
                                    font-size: 16px;
                                    line-height: 24px;
                                }
                            }

                            .originalPrice {
                                text-decoration: line-through;
                                font-size: 12px;
                                opacity: 0.6;
                                // line-height: 30px;
                            }
                        }

                        .turnaroundTime {
                            display: flex;
                            align-items: center;
                            column-gap: 10px;
                            font-weight: bold;

                            img {
                                width: 100%;
                                max-width: 20px;
                            }
                        }
                    }
                }

                .learnMoreLink {
                    font-size: 14px;
                    margin-bottom: 12px;
                    color: rgb(49, 255, 215);
                }

                .featureList {
                    display: flex;
                    flex-direction: column;
                    row-gap: 12px;
                    margin-bottom: 24px;
                    padding: 12px 0 24px 0;
                    border-bottom: 1px solid $color-separator-white-1;

                    .featureItem {
                        display: flex;
                        align-items: center;
                        column-gap: 6px;
                        font-size: 12px;

                        img {
                            width: 14px;
                        }
                    }
                }
            }
        }
    }

    .sectionActionButton {
        cursor: pointer;
        padding: 10px 12px;
        background-color: $color-app-white;
        border-radius: 4px;
        color: #000;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        // justify-content: space-between;
        column-gap: 12px;
        width: 100%;
        font-size: 14px;

        @include responsive('md') {
            width: fit-content;
            font-size: 16px;
        }
    }
}
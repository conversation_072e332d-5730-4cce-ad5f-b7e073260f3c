import { ArrowRightOutlined, PlayCircleFilled } from '@ant-design/icons'
import clsx from 'clsx'
import AppContainer from 'components/AppContainer'
import AppFooter from 'components/AppFooter'
import AppHeader from 'components/AppHeader'
import AppLayout from 'components/AppLayout'
import DownloadSection from 'components/HomePageSection/DownloadSection'
import HomePageSectionHeader from 'components/HomePageSection/HomePageSectionHeader'
import Link from 'next/link'
import { useIntl } from 'react-intl'
import getImageUrl from 'utils/imageUrl'
import { getLocaleMessages } from 'utils/locale'
import resizeImageUrl from 'utils/resizeImageUrl'
import css from './PricingPage.module.scss'
import AppPageHiddenH1 from 'components/AppPageHiddenH1'

const PricingPage = ({ pageTitle }: any) => {

    const intl = useIntl()

    return (
        <div className={css.PricingPage}>
            <AppPageHiddenH1
                title={pageTitle}
            />
            <AppLayout>
                <AppHeader />
                <div className={css.pricingSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'pricing_page_pricing_section_title' })}
                            subtitle={intl.formatMessage({ id: 'pricing_page_pricing_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'pricing_page_pricing_section_description_1' })}
                                <br />
                                {intl.formatMessage({ id: 'pricing_page_pricing_section_description_2' })}
                            </>}
                        />
                        <div className={css.solutionCardGrid}>
                            <div className={css.solutionCard}>
                                <div className={css.iconImage}>
                                    <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-category-01.png' />
                                </div>
                                <h2 className={css.title}>
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_luxury_title' })}
                                </h2>
                                <h3 className={css.subtitle}>
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_luxury_subtitle' })}
                                </h3>
                                <div className={css.highlights}>
                                    <div className={css.tagline}>{intl.formatMessage({ id: 'pricing_page_pricing_section_start_from' })}</div>
                                    <div className={css.price}>10 USD</div>
                                </div>
                                <div className={css.footer}>
                                    <div className={css.pricingTable}>
                                        <div className={css.tableTitle}>
                                            {intl.formatMessage({ id: 'pricing_page_pricing_section_option_title' })}
                                        </div>
                                        <div className={css.pricingItem}>
                                            <div className={css.turnaroundTime}>30 {intl.formatMessage({ id: 'pricing_page_pricing_section_minutes' })}</div>
                                            <div className={css.price}>20 USD</div>
                                        </div>
                                        <div className={css.pricingItem}>
                                            <div className={css.turnaroundTime}>1 {intl.formatMessage({ id: 'pricing_page_pricing_section_hour' })}</div>
                                            <div className={css.price}>15 USD</div>
                                        </div>
                                        <div className={css.pricingItem}>
                                            <div className={css.turnaroundTime}>4 {intl.formatMessage({ id: 'pricing_page_pricing_section_hours' })}</div>
                                            <div className={css.price}>10 USD</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={css.solutionCard}>
                                <div className={css.iconImage}>
                                    <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-category-02.png' />
                                </div>
                                <h2 className={css.title}>
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_sneaker_title' })}
                                </h2>
                                <h3 className={css.subtitle}>
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_sneaker_subtitle' })}
                                </h3>
                                <div className={css.highlights}>
                                    <div className={css.tagline}>{intl.formatMessage({ id: 'pricing_page_pricing_section_start_from' })}</div>
                                    <div className={css.price}>3 USD</div>
                                </div>
                                <div className={css.footer}>
                                    <div className={css.pricingTable}>
                                        <div className={css.tableTitle}>
                                            {intl.formatMessage({ id: 'pricing_page_pricing_section_option_title' })}
                                        </div>
                                        <div className={css.pricingItem}>
                                            <div className={css.turnaroundTime}>10 {intl.formatMessage({ id: 'pricing_page_pricing_section_minutes' })}</div>
                                            <div className={css.price}>5 USD</div>
                                        </div>
                                        <div className={css.pricingItem}>
                                            <div className={css.turnaroundTime}>15 {intl.formatMessage({ id: 'pricing_page_pricing_section_minutes' })}</div>
                                            <div className={css.price}>4 USD</div>
                                        </div>
                                        <div className={css.pricingItem}>
                                            <div className={css.turnaroundTime}>30 {intl.formatMessage({ id: 'pricing_page_pricing_section_minutes' })}</div>
                                            <div className={css.price}>3 USD</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={css.solutionCard}>
                                <div className={css.iconImage}>
                                    <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-category-03.png' />
                                </div>
                                <h2 className={css.title}>
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_watch_title' })}
                                </h2>
                                <h3 className={css.subtitle}>
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_watch_subtitle' })}
                                </h3>
                                <div className={css.highlights}>
                                    <div className={css.tagline}>{intl.formatMessage({ id: 'pricing_page_pricing_section_start_from' })}</div>
                                    <div className={css.price}>15 USD</div>
                                </div>
                                <div className={css.footer}>
                                    <div className={css.pricingTable}>
                                        <div className={css.tableTitle}>
                                            {intl.formatMessage({ id: 'pricing_page_pricing_section_option_title' })}
                                        </div>
                                        <div className={css.pricingItem}>
                                            <div className={css.turnaroundTime}>4 {intl.formatMessage({ id: 'pricing_page_pricing_section_hours' })}</div>
                                            <div className={css.price}>15 USD</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={css.solutionCard}>
                                <div className={css.iconImage}>
                                    <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-category-04.png' />
                                </div>
                                <h2 className={css.title}>
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_streetwear_title' })}
                                </h2>
                                <h3 className={css.subtitle}>
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_streetwear_subtitle' })}
                                </h3>
                                <div className={css.highlights}>
                                    <div className={css.tagline}>{intl.formatMessage({ id: 'pricing_page_pricing_section_start_from' })}</div>
                                    <div className={css.price}>4 USD</div>
                                </div>
                                <div className={css.footer}>
                                    <div className={css.pricingTable}>
                                        <div className={css.tableTitle}>
                                            {intl.formatMessage({ id: 'pricing_page_pricing_section_option_title' })}
                                        </div>
                                        <div className={css.pricingItem}>
                                            <div className={css.turnaroundTime}>4 {intl.formatMessage({ id: 'pricing_page_pricing_section_hours' })}</div>
                                            <div className={css.price}>4 USD</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className={css.serviceCard}>
                            <h2 className={css.serviceMessage}>
                                {intl.formatMessage({ id: 'pricing_page_pricing_section_service_title' })}
                            </h2>
                            <div className={css.serviceItemGrid}>
                                <h3 className={css.serviceItem}>
                                    <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_services_item_1' })}
                                </h3>
                                <h3 className={css.serviceItem}>
                                    <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_services_item_2' })}
                                </h3>
                                <h3 className={css.serviceItem}>
                                    <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_services_item_3' })}
                                </h3>
                                <h3 className={css.serviceItem}>
                                    <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_services_item_4' })}
                                </h3>
                                <h3 className={css.serviceItem}>
                                    <img className={css.checkIcon} src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                    {intl.formatMessage({ id: 'pricing_page_pricing_section_services_item_5' })}
                                </h3>
                            </div>
                        </div>
                    </AppContainer>
                </div >
                <div className={css.generalTokenPlanSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'pricing_page_plan_section_title' })}
                            subtitle={intl.formatMessage({ id: 'pricing_page_plan_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'pricing_page_plan_section_description_1' })}
                                <br />
                                <br />
                                {intl.formatMessage({ id: 'pricing_page_plan_section_description_2' })}
                            </>}
                        />
                        <div className={css.tokenPlanCardGrid}>
                            <div className={css.tokenPlanCard}>
                                <div className={clsx(css.tokenIcon, css.blue)} />
                                <div className={css.planInformation}>
                                    <h3 className={css.planToken}>3 {intl.formatMessage({ id: 'pricing_page_token' })}</h3>
                                    <div className={css.planPrice}>4 USD</div>
                                </div>
                            </div>
                            <div className={clsx(css.tokenPlanCard, css.popularForSneaker)}>
                                <div className={clsx(css.tagLabel, css.popularForSneaker)}>
                                    <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-popular-star.svg' />{intl.formatMessage({ id: 'pricing_page_plan_section_popular_sneaker' })}
                                </div>
                                <div className={clsx(css.tokenIcon, css.blue)} />
                                <div className={css.planInformation}>
                                    <h3 className={css.planToken}>5 {intl.formatMessage({ id: 'pricing_page_token' })}</h3>
                                    <div className={css.planPrice}>5 USD</div>
                                </div>
                            </div>
                            <div className={css.tokenPlanCard}>
                                <div className={clsx(css.tokenIcon, css.blue)} />
                                <div className={css.planInformation}>
                                    <h3 className={css.planToken}>6 {intl.formatMessage({ id: 'pricing_page_token' })}</h3>
                                    <div className={css.planPrice}>6 USD</div>
                                </div>
                            </div>
                            <div className={css.tokenPlanCard}>
                                <div className={clsx(css.tokenIcon, css.blue)} />
                                <div className={css.planInformation}>
                                    <h3 className={css.planToken}>8 {intl.formatMessage({ id: 'pricing_page_token' })}</h3>
                                    <div className={css.planPrice}>8 USD</div>
                                </div>
                            </div>
                            <div className={clsx(css.tokenPlanCard, css.popularForLuxury)}>
                                <div className={clsx(css.tagLabel, css.popularForLuxury)}>
                                    <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-popular-star.svg' />{intl.formatMessage({ id: 'pricing_page_plan_section_popular_luxuries' })}
                                </div>
                                <div className={clsx(css.tokenIcon, css.yellow)} />
                                <div className={css.planInformation}>
                                    <h3 className={css.planToken}>10 {intl.formatMessage({ id: 'pricing_page_token' })}</h3>
                                    <div className={css.planPrice}>10 USD</div>
                                </div>
                            </div>
                            <div className={css.tokenPlanCard}>
                                <div className={clsx(css.tokenIcon, css.yellow)} />
                                <div className={css.planInformation}>
                                    <h3 className={css.planToken}>20 {intl.formatMessage({ id: 'pricing_page_token' })}</h3>
                                    <div className={css.planPrice}>20 USD</div>
                                </div>
                            </div>
                            <div className={css.tokenPlanCard}>
                                <div className={clsx(css.tokenIcon, css.yellow)} />
                                <div className={css.planInformation}>
                                    <h3 className={css.planToken}>30 {intl.formatMessage({ id: 'pricing_page_token' })}</h3>
                                    <div className={css.planPrice}>30 USD</div>
                                </div>
                            </div>
                            <div className={css.tokenPlanCard}>
                                <div className={clsx(css.tokenIcon, css.yellow)} />
                                <div className={css.planInformation}>
                                    <h3 className={css.planToken}>40 {intl.formatMessage({ id: 'pricing_page_token' })}</h3>
                                    <div className={css.planPrice}>40 USD</div>
                                </div>
                            </div>
                        </div>
                        <Link
                            href={'/legit-token'}
                            className={css.sectionActionButton}
                        >
                            {intl.formatMessage({ id: `pricing_page_plan_section_action_button_title` })}<ArrowRightOutlined />
                        </Link>
                    </AppContainer>
                </div>
                <div className={css.businessPlanSection}>
                    <AppContainer className={css.sectionContainer}>
                        <HomePageSectionHeader
                            className={clsx(css.sectionHeader)}
                            title={intl.formatMessage({ id: 'pricing_page_business_section_title' })}
                            subtitle={intl.formatMessage({ id: 'pricing_page_business_section_subtitle' })}
                            description={<>
                                {intl.formatMessage({ id: 'pricing_page_business_section_description_1' })}
                            </>}
                        />
                        <div className={css.planCardGrid}>
                            <div className={css.planCard}>
                                <div className={css.planCoverImage}>
                                    <img src={resizeImageUrl(getImageUrl('https://legitapp-static.oss-accelerate.aliyuncs.com/background-pricing-business-1.png'), { width: 1000 })} />
                                </div>
                                <div className={css.planContent}>
                                    <h2 className={css.title}>
                                        {intl.formatMessage({ id: 'pricing_page_business_section_item_1_title' })}
                                    </h2>
                                    <h3 className={css.subtitle}>
                                        {intl.formatMessage({ id: 'pricing_page_business_section_item_1_subtitle' })}
                                    </h3>
                                    <div className={css.footer}>
                                        <div className={css.featureList}>
                                            <div className={css.featureItem}>
                                                <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                                {intl.formatMessage({ id: 'pricing_page_business_section_benefit_1' })}
                                            </div>
                                            <div className={css.featureItem}>
                                                <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                                {intl.formatMessage({ id: 'pricing_page_business_section_benefit_2' })}
                                            </div>
                                        </div>
                                        <div className={css.pricingTable}>
                                            <div className={css.tableTitle}>
                                                {intl.formatMessage({ id: 'pricing_page_business_section_token_plan_title' })}
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-coin-1.svg ' />
                                                    100 {intl.formatMessage({ id: 'pricing_page_token' })} + {intl.formatMessage({ id: 'pricing_page_extra' })} 10
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>100 USD</div>
                                                    <div className={css.originalPrice}>110 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-coin-2.svg ' />
                                                    250 {intl.formatMessage({ id: 'pricing_page_token' })} + {intl.formatMessage({ id: 'pricing_page_extra' })} 35
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>250 USD</div>
                                                    <div className={css.originalPrice}>285 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-coin-3.svg ' />
                                                    500 {intl.formatMessage({ id: 'pricing_page_token' })} + {intl.formatMessage({ id: 'pricing_page_extra' })} 100
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>500 USD</div>
                                                    <div className={css.originalPrice}>600 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-coin-4.svg ' />
                                                    1000 {intl.formatMessage({ id: 'pricing_page_token' })} + {intl.formatMessage({ id: 'pricing_page_extra' })} 250
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>1000 USD</div>
                                                    <div className={css.originalPrice}>1250 USD</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={css.planCard}>
                                <div className={css.planCoverImage}>
                                    <img src={resizeImageUrl(getImageUrl('https://legitapp-static.oss-accelerate.aliyuncs.com/background-pricing-business-2.png'), { width: 1000 })} />
                                </div>
                                <div className={css.planContent}>
                                    <h2 className={css.title}>
                                        {intl.formatMessage({ id: 'pricing_page_business_section_item_2_title' })}
                                    </h2>
                                    <h3 className={css.subtitle}>
                                        {intl.formatMessage({ id: 'pricing_page_business_section_item_2_subtitle' })}
                                    </h3>
                                    <div className={css.footer}>
                                        <a
                                            href={`https://www.youtube.com/watch?v=SKLgipY0b7Q`}
                                            target='_blank'
                                            rel='noopener noreferrer'
                                            className={css.learnMoreLink}
                                        >
                                            {intl.formatMessage({ id: 'pricing_page_business_section_watch_trailer_video' })} <PlayCircleFilled />
                                        </a>
                                        <Link
                                            href={'/luxe-tags'}
                                            className={css.learnMoreLink}
                                        >
                                            {intl.formatMessage({ id: 'pricing_page_business_section_learn_more_luxe_tag' })} <ArrowRightOutlined />
                                        </Link>
                                        <div className={css.featureList}>
                                            <div className={css.featureItem}>
                                                <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                                {intl.formatMessage({ id: 'pricing_page_business_section_benefit_2' })}
                                            </div>
                                            <div className={css.featureItem}>
                                                <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                                {intl.formatMessage({ id: 'pricing_page_business_section_benefit_3' })}
                                            </div>
                                            <div className={css.featureItem}>
                                                <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                                {intl.formatMessage({ id: 'pricing_page_business_section_benefit_4' })}
                                            </div>
                                        </div>
                                        <div className={css.pricingTable}>
                                            <div className={css.tableTitle}>
                                                {intl.formatMessage({ id: 'pricing_page_business_section_tag_plan_title' })}
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-luxe-1.svg ' />
                                                    10 LUXE Tags
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>120 USD</div>
                                                    <div className={css.originalPrice}>200 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-luxe-2.svg ' />
                                                    25 LUXE Tags
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>280 USD</div>
                                                    <div className={css.originalPrice}>500 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-luxe-3.svg ' />
                                                    60 LUXE Tags
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>600 USD</div>
                                                    <div className={css.originalPrice}>1200 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-luxe-4.svg ' />
                                                    150 LUXE Tags
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>1280 USD</div>
                                                    <div className={css.originalPrice}>3000 USD</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={css.planCard}>
                                <div className={css.planCoverImage}>
                                    <img src={resizeImageUrl(getImageUrl('https://legitapp-static.oss-accelerate.aliyuncs.com/background-pricing-business-3.png'), { width: 1000 })} />
                                </div>
                                <div className={css.planContent}>
                                    <h2 className={css.title}>
                                        {intl.formatMessage({ id: 'pricing_page_business_section_item_3_title' })}
                                    </h2>
                                    <h3 className={css.subtitle}>
                                        {intl.formatMessage({ id: 'pricing_page_business_section_item_3_subtitle' })}
                                    </h3>
                                    <div className={css.footer}>
                                        <a
                                            href={`https://www.youtube.com/watch?v=6Ii2lH3KD1o`}
                                            target='_blank'
                                            rel='noopener noreferrer'
                                            className={css.learnMoreLink}
                                        >
                                            {intl.formatMessage({ id: 'pricing_page_business_section_watch_trailer_video' })} <PlayCircleFilled />
                                        </a>
                                        <Link
                                            href={'/kicks-tags'}
                                            className={css.learnMoreLink}
                                        >
                                            {intl.formatMessage({ id: 'pricing_page_business_section_learn_more_kicks_tag' })} <ArrowRightOutlined />
                                        </Link>
                                        <div className={css.featureList}>
                                            <div className={css.featureItem}>
                                                <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                                {intl.formatMessage({ id: 'pricing_page_business_section_benefit_2' })}
                                            </div>
                                            <div className={css.featureItem}>
                                                <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                                {intl.formatMessage({ id: 'pricing_page_business_section_benefit_3' })}
                                            </div>
                                            <div className={css.featureItem}>
                                                <img src='https://legitapp-static.oss-accelerate.aliyuncs.com/icon-pricing-tick.svg' />
                                                {intl.formatMessage({ id: 'pricing_page_business_section_benefit_4' })}
                                            </div>
                                        </div>
                                        <div className={css.pricingTable}>
                                            <div className={css.tableTitle}>
                                                {intl.formatMessage({ id: 'pricing_page_business_section_tag_plan_title' })}
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-kick-1.svg ' />
                                                    35 KICKS Tags
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>120 USD</div>
                                                    <div className={css.originalPrice}>420 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-kick-2.svg ' />
                                                    100 KICKS Tags
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>350 USD</div>
                                                    <div className={css.originalPrice}>1200 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-kick-3.svg ' />
                                                    200 KICKS Tags
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>600 USD</div>
                                                    <div className={css.originalPrice}>2400 USD</div>
                                                </div>
                                            </div>
                                            <div className={css.pricingItem}>
                                                <div className={css.turnaroundTime}>
                                                    <img src='/icon-pricing-kick-4.svg ' />
                                                    500 KICKS Tags
                                                </div>
                                                <div className={css.price}>
                                                    <div className={css.currentPrice}>1280 USD</div>
                                                    <div className={css.originalPrice}>6000 USD</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <Link
                            href={'/contact'}
                            className={css.sectionActionButton}
                        >
                            {intl.formatMessage({ id: `pricing_page_business_section_action_button_title` })}<ArrowRightOutlined />
                        </Link>
                    </AppContainer>
                </div >
                <DownloadSection />
                <AppFooter />
            </AppLayout>
        </div>
    )
}

export async function getStaticProps({
    params,
    locale,
}: any) {
    const pageTitle = `${getLocaleMessages(locale)?.[`app_title`]} ${getLocaleMessages(locale)?.[`pricing_page_title`]}`
    const pageMetaTagItemMap: any = {
        'og-type': {
            property: "og:type",
            content: "article",
        },
        'og-title': {
            property: "og:title",
            content: pageTitle,
        },
    }
    return {
        props: {
            pageTitle,
            pageMetaTagItemMap,
        }
    }
}

export default PricingPage

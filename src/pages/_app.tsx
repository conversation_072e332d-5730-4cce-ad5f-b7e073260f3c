import { ConfigProvider } from 'antd'
import { APP_FACEBOOK_APP_ID, APP_IOS_APP_ID, APP_KEYWORDS, APP_OG_IMAGE_URL, APP_TWITTER_HANDLE, APP_URL } from "constants/app"
import useCanonicalUrl from 'hooks/useCanonicalUrl'
import type { AppProps } from "next/app"
import Head from "next/head"
import { useRouter } from "next/router"
import 'photoswipe/dist/photoswipe.css'
import { useEffect } from 'react'
import { IntlProvider } from 'react-intl'
import { Provider } from 'react-redux'
import { store } from 'store'
import "styles/globals.scss"
import theme from "styles/themConfig"
import * as gtag from 'utils/gtag'
import { getLocaleMessages } from 'utils/locale'
import removeTrailingSlash from 'utils/removeTrailingSlash'

const getLocale = (language: any) => {
  return {
    locale: language,
    messages: getLocaleMessages(language),
  }
}

export default function App({ Component, pageProps }: AppProps) {
  const router = useRouter()
  const { locale, defaultLocale, asPath } = router
  const canonicalUrl = useCanonicalUrl()
  const {
    locale: userLocale,
    messages,
  } = getLocale(locale)
  const {
    pageTitle,
    pageMetaTagItemMap = {},
  } = pageProps

  const cleanPath = removeTrailingSlash(asPath.split('#')[0].split('?')[0]);

  useEffect(() => {
    const handleRouteChange = (url: any) => {
      gtag.pageview(url)
    }
    router.events.on("routeChangeComplete", handleRouteChange)
    return () => {
      router.events.off("routeChangeComplete", handleRouteChange)
    }
  }, [router.events])

  const checkNoIndex = (component: any) => {
    if (cleanPath.indexOf('/cert/') !== -1 || cleanPath.indexOf('/tags/') !== -1) {
      return null
    }
    return component
  }

  return (
    <>
      <Head>
        <title>{pageTitle || getLocaleMessages(locale)?.[`app_title`]}</title>
        {
          checkNoIndex(
            <link
              rel="canonical"
              href={canonicalUrl}
              key="canonical"
            />
          )
        }
        <link rel='alternate' hrefLang='en-US' href={canonicalUrl} />
        <link rel='alternate' hrefLang='x-default' href={canonicalUrl} />
        <link rel='alternate' hrefLang='zh-hant-tw' href={`${APP_URL}` + `/zh-Hant` + (asPath === '/' ? '' : cleanPath)} />
        <link rel='alternate' hrefLang='zh-hant-hk' href={`${APP_URL}` + `/zh-Hant` + (asPath === '/' ? '' : cleanPath)} />
        <link rel='alternate' hrefLang='zh-CN' href={`${APP_URL}` + `/zh-Hans` + (asPath === '/' ? '' : cleanPath)} />
        {/* <link rel='alternate' hrefLang='pl-PL' href={`${APP_URL}` + `/pl` + (asPath === '/' ? '' : cleanPath)} /> */}
        <meta
          name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover"
        />
        {/* og */}
        <meta property='og:url' content={canonicalUrl} />
        <meta property='og:site_name' content={getLocaleMessages(locale)?.[`app_title`]} />
        {/* twitter */}
        <meta name='twitter:card' content='summary_large_image' />
        <meta name='twitter:site' content={APP_TWITTER_HANDLE} />
        <meta name='twitter:creator' content={APP_TWITTER_HANDLE} />
        {/* other */}
        <meta key='keywords' name='keywords' content={APP_KEYWORDS} />
        <meta name='apple-itunes-app' content={`app-id=${APP_IOS_APP_ID}`} />
        <meta property='fb:app_id' content={`${APP_FACEBOOK_APP_ID}`} />
        {/* pageMetaTagItemMap */}
        {
          pageMetaTagItemMap['og-type']
            ? (
              <>
                <meta property='og:type' content={pageMetaTagItemMap['og-type']?.content} />
              </>
            ) :
            (
              <>
                <meta property='og:type' content='website' />
              </>
            )
        }
        {
          pageMetaTagItemMap['og-title']
            ? (
              <>
                <meta property='og:title' content={pageMetaTagItemMap['og-title']?.content} />
                <meta name='twitter:title' content={pageMetaTagItemMap['og-title']?.content} />
                <meta name='twitter:image:alt' content={pageMetaTagItemMap['og-title']?.content} />
              </>
            ) :
            (
              <>
                <meta property='og:title' content={getLocaleMessages(locale)?.[`app_title`]} />
                <meta name='twitter:title' content={getLocaleMessages(locale)?.[`app_title`]} />
                <meta name='twitter:image:alt' content={getLocaleMessages(locale)?.[`app_title`]} />
              </>
            )
        }
        {
          pageMetaTagItemMap['og-description']
            ? (
              <>
                <meta property='og:description' content={pageMetaTagItemMap['og-description']?.content} />
                <meta name='twitter:description' content={pageMetaTagItemMap['og-description']?.content} />
                <meta name='description' content={pageMetaTagItemMap['og-description']?.content} />
              </>
            ) :
            (
              <>
                <meta property='og:description' content={getLocaleMessages(locale)?.[`app_og_description`]} />
                <meta name='twitter:description' content={getLocaleMessages(locale)?.[`app_og_description`]} />
                <meta name='description' content={getLocaleMessages(locale)?.[`app_og_description`]} />
              </>
            )
        }
        {
          pageMetaTagItemMap['og-image']
            ? (
              <>
                <meta property='og:image' content={pageMetaTagItemMap['og-image']?.content} />
                <meta name='twitter:image' content={pageMetaTagItemMap['og-image']?.content} />
              </>
            ) :
            (
              <>
                <meta property='og:image' content={APP_OG_IMAGE_URL} />
                <meta name='twitter:image' content={APP_OG_IMAGE_URL} />
              </>
            )
        }
      </Head>
      <Provider store={store}>
        <IntlProvider
          locale={userLocale}
          defaultLocale={defaultLocale}
          messages={messages}
        >
          <ConfigProvider theme={theme}>
            {/* <NextNProgress
              color='#FF1E60'
              options={{
                showSpinner: false,
              }}
            /> */}
            <Component {...pageProps} />
          </ConfigProvider>
        </IntlProvider>
      </Provider>
    </>
  )
}

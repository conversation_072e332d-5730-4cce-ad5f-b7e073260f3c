// Responsive layout ref to antd grid design
// https://ant.design/components/grid/#Col
@mixin responsive($breakpoint) {
    @if $breakpoint =="xxl" {
      @media only screen and (min-width: $grid-width-xxl) {
        @content;
      }
    }
  
    @else if $breakpoint =="xl" {
      @media only screen and (min-width: $grid-width-xl) {
        @content;
      }
    }
  
    @else if $breakpoint =="lg" {
      @media only screen and (min-width: $grid-width-lg) {
        @content;
      }
    }
  
    @else if $breakpoint =="md" {
      @media only screen and (min-width: $grid-width-md) {
        @content;
      }
    }
  
    @else if $breakpoint =="sm" {
      @media only screen and (min-width: $grid-width-sm) {
        @content;
      }
    }
  
    @else if $breakpoint =="xs" {
      @media only screen and (max-width: $grid-width-xs) {
        @content;
      }
    }
  }
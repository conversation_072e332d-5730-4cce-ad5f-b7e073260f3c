@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  // color: rgb(var(--foreground-rgb));
  // background: linear-gradient(to bottom,
  //     transparent,
  //     rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
  background-color: #000;
  font-family: "Montserrat";
  min-height: 100vh;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

.ant-message {
  .app-pop-up-message {
    .ant-message-notice-content {
      // border-radius: 10px;
      // background-color: #000;
      // color: ;
      font-family: "Montserrat";
    }
  }
}

.pswp__img {
  pointer-events: none !important;
  object-fit: cover !important;
}

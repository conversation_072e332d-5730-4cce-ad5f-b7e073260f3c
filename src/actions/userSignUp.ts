import { fetchUser, userSignIn } from 'actions/app'
import bowser from 'bowser'
import { USER_SIGN_UP_REQUEST_FAIL, USER_SIGN_UP_REQUEST_LOAD, USER_SIGN_UP_REQUEST_SUCCEED } from 'constants/actionTypes'
import {
  APP_VERSION,
} from 'constants/app'
import api from 'utils/apiCore'

export const sendSignUpRequest = (values: any = {}) => async (dispatch: any = {}) => {
  dispatch({ type: USER_SIGN_UP_REQUEST_LOAD })
  try {
    const browserInfo = bowser.getParser(window.navigator.userAgent).getBrowser()
    const deviceVersion = `${browserInfo.name} ${browserInfo.version}`
    const createUserParams = {
      ...values,
    }
    delete createUserParams.remember_me
    // register user
    await api.post(dispatch, 'user', createUserParams)
    // login user
    const result = await api.post(dispatch, 'authentication', {
      ...values,
      app: 'client',
      app_version: APP_VERSION,
      device: 'web',
      device_version: deviceVersion,
    })
    // console.log('result.token', result.token)
    if (result.token) {
      // const user = await await api.post(null, 'me', {}, result.token)
      dispatch({ type: USER_SIGN_UP_REQUEST_SUCCEED })
      await dispatch(userSignIn({ accessToken: result.token, rememberMe: values.remember_me }))
      await dispatch(fetchUser({ accessToken: result.token }))
    }
  } catch (errors) {
    dispatch({
      type: USER_SIGN_UP_REQUEST_FAIL,
      requestErrors: errors,
    })
  }
}

import apiCore from 'utils/apiCore'
import {
  ARTICLEDETAIL_ENTER_RESET,
  ARTICLEDETAIL_FETCHITEM_LOAD,
  ARTICLEDETAIL_FETCHITEM_SUCCEED,
  ARTICLEDETAIL_FETCHITEM_FAIL,
} from '../constants/actionTypes'
import { AppDispatch } from 'store'
// import getFieldFromItem from '../utils/getFieldFromItem'

export const keys = [
  'id',
  'title',
  'created_at',
  'updated_at',
]

export const reset = () => ({
  type: ARTICLEDETAIL_ENTER_RESET,
})

export const fetchItem = (params: any) => async (dispatch: AppDispatch) => {
  !params.isRefreshing && dispatch({ type: ARTICLEDETAIL_FETCHITEM_LOAD })
  try {
    const item = await apiCore.get(dispatch, `v1/article/slug/${params.slug}`)
    item.content = item.content
      .replace('legitapp-prod.oss-ap-southeast-1.aliyuncs.com', 'legitapp-prod.oss-accelerate.aliyuncs.com')
    dispatch({
      type: ARTICLEDETAIL_FETCHITEM_SUCCEED,
      item,
    })
  } catch (errors) {
    dispatch({
      type: ARTICLEDETAIL_FETCHITEM_FAIL,
      fetchItemErrors: errors,
    })
  }
}

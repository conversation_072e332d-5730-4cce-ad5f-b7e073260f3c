import { AppDispatch } from 'store'
import apiCore from 'utils/apiCore'
import {
    VERIFIED_BUSINESS_DETAIL_ENTER_RESET,
    VERIFIED_BUSINESS_DETAIL_REQUEST_FAIL,
    VERIFIED_BUSINESS_DETAIL_REQUEST_LOAD,
    VERIFIED_BUSINESS_DETAIL_REQUEST_SUCCEED,
} from '../constants/actionTypes'

export const reset = () => ({
  type: VERIFIED_BUSINESS_DETAIL_ENTER_RESET,
})

export const fetchData = (params: any = {}) => async (dispatch: AppDispatch) => {
  dispatch({ type: VERIFIED_BUSINESS_DETAIL_REQUEST_LOAD })
  try {
    const requestResult = await apiCore.get(dispatch, `v1/business_verified/slug/${params.slug}`)
    dispatch({
      type: VERIFIED_BUSINESS_DETAIL_REQUEST_SUCCEED,
      requestResult,
    })
  } catch (errors) {
    // console.log('errors', errors)
    dispatch({
      type: VERIFIED_BUSINESS_DETAIL_REQUEST_FAIL,
      requestErrors: errors,
    })
  }
}

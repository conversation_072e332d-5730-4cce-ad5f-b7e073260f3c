import { AppDispatch } from 'store'
import apiCore from 'utils/apiCore'
import { IOrderDetail } from 'types/orders'
import {
  ORDER_DETAIL_ENTER_RESET,
  ORDER_DETAIL_FETCHITEM_FAIL,
  ORDER_DETAIL_FETCHITEM_LOAD,
  ORDER_DETAIL_FETCHITEM_SUCCEED,
} from '../constants/actionTypes'

export const reset = () => ({
  type: ORDER_DETAIL_ENTER_RESET,
} as const)

export interface FetchOrderDetailParams {
  id: string;
  accessToken: string;
}

export const fetchOrderDetail = (params: FetchOrderDetailParams) => async (dispatch: AppDispatch) => {
  const { id, accessToken } = params;

  dispatch({ type: ORDER_DETAIL_FETCHITEM_LOAD })

  try {
    const orderDetail: IOrderDetail = await apiCore.get(
      null,
      `v1/service_request/${id}`,
      {},
      accessToken
    )

    dispatch({
      type: ORDER_DETAIL_FETCHITEM_SUCCEED,
      orderDetail,
    })
  } catch (errors) {
    dispatch({
      type: ORDER_DETAIL_FETCHITEM_FAIL,
      fetchItemErrors: errors,
    })
  }
}

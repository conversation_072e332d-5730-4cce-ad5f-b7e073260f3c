import { AppDispatch } from 'store'
import apiCore from 'utils/apiCore'
import {
  START_AUTHENTICATION_HOME_RESET,
  START_AUTHENTICATION_HOME_FETCH_FAIL,
  START_AUTHENTICATION_HOME_FETCH_LOAD,
  START_AUTHENTICATION_HOME_FETCH_SUCCEED,
} from '../constants/actionTypes'

export const reset = () => ({
  type: START_AUTHENTICATION_HOME_RESET,
})

interface FetchHomeDataParams {
  accessToken?: string
}

export const fetchHomeData = (params: FetchHomeDataParams = {}) => async (dispatch: AppDispatch) => {
  const {
    accessToken,
  } = params

  dispatch({
    type: START_AUTHENTICATION_HOME_FETCH_LOAD,
  })

  try {
    const result = await apiCore.get(dispatch, 'v1/home', {}, accessToken)
    
    dispatch({
      type: START_AUTHENTICATION_HOME_FETCH_SUCCEED,
      data: result,
    })

    return Promise.resolve(result)
  } catch (error) {
    dispatch({
      type: START_AUTHENTICATION_HOME_FETCH_FAIL,
      errors: error,
    })
    return Promise.reject(error)
  }
}

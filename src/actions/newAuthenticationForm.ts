import {
  NEW_AUTHENTICATION_FORM_UPDATE,
  NEW_AUTHENTICATION_FORM_RESET,
  NEW_AUTHENTICATION_FORM_UPDATE_UPLOAD_PHOTO_LIST,
} from "../constants/actionTypes";
import { INewAuthenticationFormState, IProductTitle } from "types/orders";

export const updateNewAuthenticationForm = (
  payload: Partial<INewAuthenticationFormState>
) => ({
  type: NEW_AUTHENTICATION_FORM_UPDATE,
  payload,
});

export const resetNewAuthenticationForm = () => ({
  type: NEW_AUTHENTICATION_FORM_RESET,
});

export const setServiceSet = (
  serviceSet: INewAuthenticationFormState["serviceSet"]
) => updateNewAuthenticationForm({ serviceSet });

export const setCategoryBrandModel = (payload: {
  category_id: string;
  brand_id: string;
  model_id: string;
}) => updateNewAuthenticationForm(payload);

export const setCurrentServiceLevel = (
  currentServiceLevel: INewAuthenticationFormState["currentServiceLevel"]
) => updateNewAuthenticationForm({ currentServiceLevel });

export const setCurrentAdditionalServiceList = (
  currentAdditionalServiceList: INewAuthenticationFormState["currentAdditionalServiceList"]
) => updateNewAuthenticationForm({ currentAdditionalServiceList });

export const setUploadPhotoList = (
  uploadPhotoList: INewAuthenticationFormState["uploadPhotoList"]
) => updateNewAuthenticationForm({ uploadPhotoList });

export const updateUploadPhotoList = (
  updater: (
    prevList: INewAuthenticationFormState["uploadPhotoList"]
  ) => INewAuthenticationFormState["uploadPhotoList"]
) => ({
  type: NEW_AUTHENTICATION_FORM_UPDATE_UPLOAD_PHOTO_LIST,
  updater,
});

export const setCertificateOwnerName = (certificateOwnerName: string) =>
  updateNewAuthenticationForm({ certificateOwnerName });

export const setUserCustomCode = (user_custom_code: string) =>
  updateNewAuthenticationForm({ user_custom_code });

export const setUserRemark = (user_remark: string) =>
  updateNewAuthenticationForm({ user_remark });
export const setProductTitle = (product_title: IProductTitle) =>
  updateNewAuthenticationForm({ product_title });

export const setFormFields = (fields: {
  user_custom_code?: string;
  product_source_currency?: string;
  product_title?: IProductTitle;
  service_level_id?: number | null;
  service_extra_service_ids?: number[] | null;
  service_request_promotion_code?: string | null;
  user_remark?: string;
}) => updateNewAuthenticationForm(fields);

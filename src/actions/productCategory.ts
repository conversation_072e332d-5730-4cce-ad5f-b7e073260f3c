import apiCore from "utils/apiCore";
import {
  PRODUCT_CATEGORY_ENTER_RESET,
  PRODUCT_CATEGORY_FETCHITEMS_LOAD,
  PRODUCT_CATEGORY_FETCHITEMS_SUCCEED,
  PRODUCT_CATEGORY_FETCHITEMS_FAIL,
} from "../constants/actionTypes";
import { AppDispatch } from "store";

export const reset = () => ({
  type: PRODUCT_CATEGORY_ENTER_RESET,
});

interface FetchDataParams {
  accessToken?: string;
}

export const fetchCategoryList =
  (params: FetchDataParams = {}) =>
  async (dispatch: AppDispatch) => {
    const { accessToken } = params;

    dispatch({
      type: PRODUCT_CATEGORY_FETCHITEMS_LOAD,
    });

    try {
      const result = await apiCore.get(
        dispatch,
        "v1/product_category",
        {},
        accessToken
      );
      const { data: items, total } = result;

      dispatch({
        type: PRODUCT_CATEGORY_FETCHITEMS_SUCCEED,
        items,
        total,
      });

      return Promise.resolve(result);
    } catch (error) {
      dispatch({
        type: PRODUCT_CATEGORY_FETCHITEMS_FAIL,
        fetchItemsErrors: error,
      });
      return Promise.reject(error);
    }
  };

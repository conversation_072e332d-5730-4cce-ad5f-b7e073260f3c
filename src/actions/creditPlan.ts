import apiCore from "utils/apiCore";
import {
  CREDIT_PLAN_RESET,
  CREDIT_PLAN_FETCHITEMS_LOAD,
  CREDIT_PLAN_FETCHITEMS_SUCCEED,
  CREDIT_PLAN_FETCHITEMS_FAIL,
} from "../constants/actionTypes";
import { AppDispatch } from "store";

export const reset = () => ({
  type: CREDIT_PLAN_RESET,
});

interface FetchDataParams {
  accessToken?: string;
}

export const fetchCreditPlan =
  (params: FetchDataParams = {}) =>
  async (dispatch: AppDispatch) => {
    const { accessToken } = params;

    dispatch({
      type: CREDIT_PLAN_FETCHITEMS_LOAD,
    });

    try {
      const result = await apiCore.get(
        dispatch,
        "v1/credit_plan",
        {},
        accessToken
      );

      const { data: items, total, currency_rate } = result;

      dispatch({
        type: CREDIT_PLAN_FETCHITEMS_SUCCEED,
        items,
        currency_rate,
        total,
      });

      return Promise.resolve(result);
    } catch (error) {
      dispatch({
        type: CREDIT_PLAN_FETCHITEMS_FAIL,
        fetchItemsErrors: error,
      });
      return Promise.reject(error);
    }
  };

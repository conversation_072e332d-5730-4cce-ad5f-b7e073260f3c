import { AppDispatch } from 'store'
import apiCore from 'utils/apiCore'
import {
  SERVICEREQUESTDETAIL_ENTER_RESET,
  SERVICEREQUESTDETAIL_FETCHITEM_FAIL,
  SERVICEREQUESTDETAIL_FETCHITEM_LOAD,
  SERVICEREQUESTDETAIL_FETCHITEM_SUCCEED,
} from '../constants/actionTypes'
// import getFieldFromItem from '../utils/getFieldFromItem'

export const keys = [
  'id',
  'title',
  'created_at',
  'updated_at',
]

export const reset = () => ({
  type: SERVICEREQUESTDETAIL_ENTER_RESET,
})

export const fetchItem = (params: any) => async (dispatch: AppDispatch) => {
  !params.isRefreshing && dispatch({ type: SERVICEREQUESTDETAIL_FETCHITEM_LOAD })
  try {
    const item = await apiCore.get(dispatch, `v2/service_feed/uuid/${params.uuid}`)
    dispatch({
      type: SERVICEREQUESTDETAIL_FETCHITEM_SUCCEED,
      item,
    })
  } catch (errors) {
    dispatch({
      type: SERVICEREQUESTDETAIL_FETCHITEM_FAIL,
      fetchItemErrors: errors,
    })
  }
}

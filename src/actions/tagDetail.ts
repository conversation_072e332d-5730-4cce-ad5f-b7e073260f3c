import apiCore from 'utils/apiCore'
import {
  TAG<PERSON><PERSON><PERSON>_ENTER_RESET,
  TAGDETAIL_FETCHITEM_LOAD,
  TAGDETAIL_FETCHITEM_SUCCEED,
  TAGDETAIL_FETCHITEM_FAIL,
} from '../constants/actionTypes'
import { AppDispatch } from 'store'
// import getFieldFromItem from '../utils/getFieldFromItem'

export const keys = [
  'id',
  'title',
  'created_at',
  'updated_at',
]

export const reset = () => ({
  type: TAGDETAIL_ENTER_RESET,
})

export const fetchItem = (params: any) => async (dispatch: AppDispatch) => {
  !params.isRefreshing && dispatch({ type: TAGDETAIL_FETCHITEM_LOAD })
  try {
    const item = await apiCore.get(dispatch, `v2/service_feed/legit_tag_uuid/${params.uuid}`)
    dispatch({
      type: TAGDE<PERSON>IL_FETCHITEM_SUCCEED,
      item,
    })
  } catch (errors) {
    dispatch({
      type: TAGDE<PERSON>IL_FETCHITEM_FAIL,
      fetchItemErrors: errors,
    })
  }
}

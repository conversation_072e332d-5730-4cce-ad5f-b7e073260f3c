import { AppDispatch } from 'store'
import apiCore from 'utils/apiCore'
import {
  SERVICEREQUESTMESSAGE_ENTER_RESET,
  SERVICEREQUESTMESSAGE_FETCHITEMS_FAIL,
  SERVICEREQUESTMESSAGE_FETCHITEMS_LOAD,
  SERVICEREQUESTMESSAGE_FETCHITEMS_SUCCEED,
} from '../constants/actionTypes'

export interface FetchServiceRequestMessagesParams {
  service_request_id: string;
  accessToken: string;
  offset?: number;
  limit?: number;
}

export const reset = () => ({
  type: SERVICEREQUESTMESSAGE_ENTER_RESET,
})

export const fetchServiceRequestMessages = (params: FetchServiceRequestMessagesParams) => async (dispatch: AppDispatch) => {
  const { service_request_id, accessToken, offset = 0, limit = 25 } = params;
  
  dispatch({ type: SERVICEREQUESTMESSAGE_FETCHITEMS_LOAD })
  
  try {
    const query = {
      service_request_id,
      $offset: offset,
      $limit: limit,
    };

    const result = await apiCore.get(
      null,
      'v1/service_request_message',
      query,
      accessToken
    )
    
    const { data: messages, total } = result;
    
    dispatch({
      type: SERVICEREQUESTMESSAGE_FETCHITEMS_SUCCEED,
      messages,
      total,
    })
  } catch (errors) {
    dispatch({
      type: SERVICEREQUESTMESSAGE_FETCHITEMS_FAIL,
      fetchItemsErrors: errors,
    })
  }
}

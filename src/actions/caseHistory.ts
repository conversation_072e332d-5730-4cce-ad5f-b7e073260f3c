import { AppDispatch } from "store";
import apiCore from "utils/apiCore";
import {
  CASE_HISTORY_RESET,
  CASE_HISTORY_FETCHITEMS_LOAD,
  CASE_HISTORY_FETCHITEMS_SUCCEED,
  CASE_HISTORY_FETCHITEMS_FAIL,
} from "../constants/actionTypes";
import { LIST_PAGESIZE_24 } from "../constants/app";
import { ICaseHistory } from "../types/app";

export const reset = () => ({
  type: CASE_HISTORY_RESET,
});

export const fetchItems =
  (params: any = {}, originalItems: ICaseHistory[] = []) =>
  async (dispatch: AppDispatch) => {
    const { $offset = 0, $limit = LIST_PAGESIZE_24 } = params;

    dispatch({ type: CASE_HISTORY_FETCHITEMS_LOAD });

    try {
      const query: any = {
        $offset,
        $limit,
      };

      const result = await apiCore.get(dispatch, `v1/service_feed`, query);
      const { data: items, total } = result;

      const allItems: ICaseHistory[] = originalItems.concat(items);
      const uniqueItemsMap = new Map<number, ICaseHistory>();

      allItems.forEach((item) => {
        if (!uniqueItemsMap.has(item.id)) {
          uniqueItemsMap.set(item.id, item);
        }
      });

      const uniqueItems = Array.from(uniqueItemsMap.values());

      dispatch({
        type: CASE_HISTORY_FETCHITEMS_SUCCEED,
        items: uniqueItems,
        total,
      });

      return Promise.resolve(result);
    } catch (errors) {
      dispatch({
        type: CASE_HISTORY_FETCHITEMS_FAIL,
        fetchItemsErrors: errors,
      });

      return Promise.reject(errors);
    }
  };

import { AppDispatch } from 'store'
import apiCore from 'utils/apiCore'
import {
  SERVICEREQUESTLIST_ENTER_RESET,
  SERVICEREQUESTLIST_FETCHITEMS_FAIL,
  SERVICEREQUESTLIST_FETCHITEMS_LOAD,
  SERVICEREQUESTLIST_FETCHITEMS_SUCCEED,
} from '../constants/actionTypes'
import { LIST_PAGESIZE } from '../constants/app'

export const searchKeys = [
  'id',
]

export const reset = () => ({
  type: SERVICEREQUESTLIST_ENTER_RESET,
})

export const fetchItems = (params: any = {}, originalItems = []) => async (dispatch: AppDispatch) => {
  const {
    $offset = 0,
    $limit = LIST_PAGESIZE,
  } = params
  dispatch({ type: SERVICEREQUESTLIST_FETCHITEMS_LOAD })
  try {
    const query = {
      $offset,
      $limit,
    }
    const result = await apiCore.get(dispatch, `v1/service_feed`, query)
    const {
      data: items,
      total,
    } = result
    // console.log('items', items)
    dispatch({
      type: SERVICEREQUESTLIST_FETCHITEMS_SUCCEED,
      items: originalItems.concat(items),
      total,
    })
  } catch (errors) {
    // console.log('errors', errors)
    dispatch({
      type: SERVICEREQUESTLIST_FETCHITEMS_FAIL,
      fetchItemsErrors: errors,
    })
  }
}

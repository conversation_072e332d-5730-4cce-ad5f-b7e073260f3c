import apiCore from "utils/apiCore";
import {
  CURRENCY_RATE_RESET,
  CURRENCY_RATE_FETCHITEMS_LOAD,
  CURRENCY_RATE_FETCHITEMS_SUCCEED,
  CURRENCY_RATE_FETCHITEMS_FAIL,
} from "../constants/actionTypes";
import { AppDispatch } from "store";

export const reset = () => ({
  type: CURRENCY_RATE_RESET,
});

export const fetchCurrencyRates =
  () =>
  async (dispatch: AppDispatch) => {

    dispatch({
      type: CURRENCY_RATE_FETCHITEMS_LOAD,
    });

    try {
      const result = await apiCore.get(
        dispatch,
        "v1/currency_rate",
        {},
      );

      const { data: items, total } = result;

      dispatch({
        type: CURRENCY_RATE_FETCHITEMS_SUCCEED,
        items,
        total,
      });

      return Promise.resolve(result);
    } catch (error) {
      dispatch({
        type: CURRENCY_RATE_FETCHITEMS_FAIL,
        fetchItemsErrors: error,
      });
      return Promise.reject(error);
    }
  };

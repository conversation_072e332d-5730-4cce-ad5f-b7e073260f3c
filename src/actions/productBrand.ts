import apiCore from "utils/apiCore";
import {
  PRODUCT_BRAND_ENTER_RESET,
  PRODUCT_BRAND_FETCHITEMS_LOAD,
  PRODUCT_BRAND_FETCHITEMS_SUCCEED,
  PRODUCT_BRAND_FETCHITEMS_FAIL,
} from "../constants/actionTypes";
import { AppDispatch } from "store";

export const reset = () => ({
  type: PRODUCT_BRAND_ENTER_RESET,
});

interface FetchDataParams {
  categoryId: number | string;
  accessToken?: string;
}

export const fetchBrandList =
  (params: FetchDataParams) => async (dispatch: AppDispatch) => {
    const { categoryId, accessToken } = params;

    if (!categoryId) {
      return Promise.reject([{ message: "Category ID is required" }]);
    }

    dispatch({
      type: PRODUCT_BRAND_FETCHITEMS_LOAD,
    });

    try {
      const query = {
        category_id: categoryId,
      };

      const result = await apiCore.get(
        dispatch,
        "v1/product_brand",
        query,
        accessToken
      );

      const { data: items, total } = result;

      dispatch({
        type: PRODUCT_BRAND_FETCHITEMS_SUCCEED,
        items,
        total,
        categoryId,
      });

      return Promise.resolve(result);
    } catch (error) {
      dispatch({
        type: PRODUCT_BRAND_FETCHITEMS_FAIL,
        fetchItemsErrors: error,
      });
      return Promise.reject(error);
    }
  };

import { AppDispatch } from 'store'
import apiCore from 'utils/apiCore'
import {
    VERIFIED_BUSINESS_LIST_ENTER_RESET,
    VERIFIED_BUSINESS_LIST_REQUEST_FAIL,
    VERIFIED_BUSINESS_LIST_REQUEST_LOAD,
    VERIFIED_BUSINESS_LIST_REQUEST_SUCCEED,
} from '../constants/actionTypes'
import { LIST_PAGESIZE } from '../constants/app'

export const reset = () => ({
  type: VERIFIED_BUSINESS_LIST_ENTER_RESET,
})

export const fetchData = (params: any = {}, originalItems = []) => async (dispatch: AppDispatch) => {
  const {
    $offset = 0,
    $limit = LIST_PAGESIZE,
    // sort = 'index',
    // order = 'descend',
    // search,
    // language,
  } = params
  dispatch({ type: VERIFIED_BUSINESS_LIST_REQUEST_LOAD })
  try {
    const query = {
      $offset,
      $limit,
      // $sort: { published_at: -1 },
      // public: 1,
      // enabled: 1,
    }
    const requestResult = await apiCore.get(dispatch, `v1/business_verified`, query)
    dispatch({
      type: VERIFIED_BUSINESS_LIST_REQUEST_SUCCEED,
      requestResult: {
        ...requestResult,
        data: originalItems.concat(requestResult.data),
      },
    })
  } catch (errors) {
    // console.log('errors', errors)
    dispatch({
      type: VERIFIED_BUSINESS_LIST_REQUEST_FAIL,
      requestErrors: errors,
    })
  }
}

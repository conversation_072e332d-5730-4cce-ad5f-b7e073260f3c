import bowser from 'bowser'
import { USER_FORGOT_PASSWORD_REQUEST_FAIL, USER_FORGOT_PASSWORD_REQUEST_LOAD, USER_FORGOT_PASSWORD_REQUEST_SUCCEED } from 'constants/actionTypes'
import {
    APP_VERSION,
} from 'constants/app'
import api from 'utils/apiCore'

export const sendForgotpasswordRequest = (values: any = {}) => async (dispatch: any = {}) => {
    dispatch({ type: USER_FORGOT_PASSWORD_REQUEST_LOAD })
    try {
        const browserInfo = bowser.getParser(window.navigator.userAgent).getBrowser()
        const deviceVersion = `${browserInfo.name} ${browserInfo.version}`
        await api.post(dispatch, 'utility/forgot_password', {
            ...values,
            app: 'client',
            app_version: APP_VERSION,
            device: 'web',
            device_version: deviceVersion,
        })
        dispatch({ type: USER_FORGOT_PASSWORD_REQUEST_SUCCEED })
    } catch (errors) {
        dispatch({
            type: USER_FORGOT_PASSWORD_REQUEST_FAIL,
            requestErrors: errors,
        })
    }
}

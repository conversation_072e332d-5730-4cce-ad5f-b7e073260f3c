import {
    USER_PROFILE_REQUEST_FAIL,
    USER_PROFILE_REQUEST_LOAD,
    USER_PROFILE_REQUEST_SUCCEED,
    APP_ACCESS_TOKEN_SET,
    APP_ACCESS_TOKEN_CLEAR,
    APP_COOKIES_ACCEPTED_SET,
    APP_LOGIN_REDIRECT_PATH_SET,
    APP_LOGIN_REDIRECT_PATH_CLEAR,
  } from 'constants/actionTypes'
  import { STORAGE_KEY } from 'constants/app'
  import _ from 'lodash'
  import api from 'utils/apiCore'
  import Storage from 'utils/storage'

  export const setCookiesAccepted = () => (dispatch: any) => {
    dispatch({ type: APP_COOKIES_ACCEPTED_SET })
  }

  export const userSignIn = (values: any) => (dispatch: any) => {
    const {
      accessToken,
    } = values
    // only save the access token to local storage when rememberMe is true
    // rememberMe &&
    localStorage.setItem('accessToken', accessToken)
    dispatch({
      type: APP_ACCESS_TOKEN_SET,
      accessToken,
    })
  }

  export const userSignOut = () => (dispatch: any) => {
    localStorage.removeItem('accessToken')
    Storage.remove(STORAGE_KEY.USER)
    dispatch({ type: APP_ACCESS_TOKEN_CLEAR })
  }

  export const fetchUser = (params: any) => async (dispatch: any) => {
    dispatch({ type: USER_PROFILE_REQUEST_LOAD })
    try {
      const { accessToken } = params
      const [
        userResult,
      ] = await Promise.all([
        api.get(dispatch, 'v1/me', {}, accessToken),
      ])
      const user = userResult
      Storage.set(STORAGE_KEY.USER, user)
      dispatch({
        type: USER_PROFILE_REQUEST_SUCCEED,
        user,
      })
    } catch (errors) {
      const errorCode = _.get(errors, '[0].code')
      if (errorCode === 401) {
        dispatch(userSignOut())
      }
      dispatch({
        type: USER_PROFILE_REQUEST_FAIL,
        fetchUserErrors: errors,
      })
    }
  }

  export const setLoginRedirectPath = (values: any) => (dispatch: any) => {
    const {
      loginRedirectPath,
    } = values
    if (!loginRedirectPath) {
      return
    }
    dispatch({
      type: APP_LOGIN_REDIRECT_PATH_SET,
      loginRedirectPath,
    })
  }

  export const clearLoginRedirectPath = () => (dispatch: any) => {
    dispatch({
      type: APP_LOGIN_REDIRECT_PATH_CLEAR,
    })
  }

import apiCore from 'utils/apiCore'
import {
  ARTICLELIST_ENTER_RESET,
  ARTICLELIST_FETCHITEMS_LOAD,
  ARTICLELIST_FETCHITEMS_SUCCEED,
  ARTICLELIST_FETCHITEMS_FAIL,
} from '../constants/actionTypes'
import { LIST_PAGESIZE } from '../constants/app'
import { AppDispatch } from 'store'

export const searchKeys = [
  'id',
]

export const reset = () => ({
  type: ARTICLELIST_ENTER_RESET,
})

export const fetchItems = (params: any = {}, originalItems = []) => async (dispatch: AppDispatch) => {
  const {
    $offset = 0,
    $limit = LIST_PAGESIZE,
    // sort = 'index',
    // order = 'descend',
    // search,
    // language,
  } = params
  dispatch({ type: ARTICLELIST_FETCHITEMS_LOAD })
  try {
    const query = {
      $offset,
      $limit,
      $sort: { published_at: -1 },
      language: 'en', // getArticleLanguage(language),
      // article_type_id: 1,
      public: 1,
      enabled: 1,
    }
    const result = await apiCore.get(dispatch, `v1/article`, query)
    const {
      data: items,
      total,
    } = result
    dispatch({
      type: ARTICLELIST_FETCHITEMS_SUCCEED,
      items: originalItems.concat(items),
      total,
    })
  } catch (errors) {
    // console.log('errors', errors)
    dispatch({
      type: ARTICLELIST_FETCHITEMS_FAIL,
      fetchItemsErrors: errors,
    })
  }
}

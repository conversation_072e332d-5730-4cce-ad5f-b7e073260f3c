import apiCore from "utils/apiCore";
import {
  PRODUCT_MODEL_ENTER_RESET,
  PRODUCT_MODEL_FETCHITEMS_LOAD,
  PRODUCT_MODEL_FETCHITEMS_SUCCEED,
  PRODUCT_MODEL_FETCHITEMS_FAIL,
} from "../constants/actionTypes";
import { AppDispatch } from "store";

export const reset = () => ({
  type: PRODUCT_MODEL_ENTER_RESET,
});

interface FetchDataParams {
  categoryId: number | string;
  brandId: number | string;
  accessToken?: string;
}

export const fetchModelList =
  (params: FetchDataParams) => async (dispatch: AppDispatch) => {
    const { categoryId, brandId, accessToken } = params;

    if (!categoryId) {
      return Promise.reject([{ message: "Category ID is required" }]);
    }

    if (!brandId) {
      return Promise.reject([{ message: "Brand ID is required" }]);
    }

    dispatch({
      type: PRODUCT_MODEL_FETCHITEMS_LOAD,
    });

    try {
      const query: any = {
        category_id: categoryId,
        brand_id: brandId,
      };

      const result = await apiCore.get(
        dispatch,
        "v1/product_model",
        query,
        accessToken
      );

      const { data: items, total } = result;

      dispatch({
        type: PRODUCT_MODEL_FETCHITEMS_SUCCEED,
        items,
        total,
        categoryId,
        brandId,
      });

      return Promise.resolve(result);
    } catch (error) {
      dispatch({
        type: PRODUCT_MODEL_FETCHITEMS_FAIL,
        fetchItemsErrors: error,
      });
      return Promise.reject(error);
    }
  };

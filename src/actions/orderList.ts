import { AppDispatch } from "store";
import apiCore from "utils/apiCore";
import {
  ORDER_LIST_ENTER_RESET,
  ORDER_LIST_FETCHITEMS_FAIL,
  ORDER_LIST_FETCHITEMS_LOAD,
  ORDER_LIST_FETCHITEMS_SUCCEED,
  ORDER_LIST_FETCH_PHOTOS_REQUIRED_LOAD,
  ORDER_LIST_FETCH_PHOTOS_REQUIRED_SUCCEED,
  ORDER_LIST_FETCH_PHOTOS_REQUIRED_FAIL,
} from "../constants/actionTypes";
import { LIST_PAGESIZE } from "../constants/app";

export const reset = () => ({
  type: ORDER_LIST_ENTER_RESET,
});

export const fetchOrderList =
  (params: any = {}, originalItems = [], accessToken?: string) =>
  async (dispatch: AppDispatch) => {
    const { $offset = 0, $limit = LIST_PAGESIZE } = params;

    dispatch({ type: ORDER_LIST_FETCHITEMS_LOAD });

    try {
      const query = {
        $offset,
        $limit,
      };

      const result = await apiCore.get(
        dispatch,
        `v1/service_request`,
        query,
        accessToken
      );
      const { data: items, total } = result;

      dispatch({
        type: ORDER_LIST_FETCHITEMS_SUCCEED,
        items: originalItems.concat(items),
        total,
      });

      return Promise.resolve(result);
    } catch (errors) {
      dispatch({
        type: ORDER_LIST_FETCHITEMS_FAIL,
        fetchItemsErrors: errors,
      });

      return Promise.reject(errors);
    }
  };

export const fetchPhotosRequiredOrders =
  (accessToken?: string) =>
  async (dispatch: AppDispatch) => {
    dispatch({ type: ORDER_LIST_FETCH_PHOTOS_REQUIRED_LOAD });

    try {
      const query = {
        "status[0]": "user_pending",
        $limit: 1,
      };

      const result = await apiCore.get(
        dispatch,
        `v1/service_request`,
        query,
        accessToken
      );
      const { data: items } = result;

      dispatch({
        type: ORDER_LIST_FETCH_PHOTOS_REQUIRED_SUCCEED,
        photosRequiredItems: items,
      });

      return Promise.resolve(result);
    } catch (errors) {
      dispatch({
        type: ORDER_LIST_FETCH_PHOTOS_REQUIRED_FAIL,
        fetchPhotosRequiredErrors: errors,
      });

      return Promise.reject(errors);
    }
  };
